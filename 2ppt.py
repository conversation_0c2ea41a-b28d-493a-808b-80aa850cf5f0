#!/usr/bin/env python3
from pptx import Presentation
from pptx.util import Inches, Pt
from bs4 import BeautifulSoup
import re

def convert_html_to_pptx(html_path, pptx_path):
    # 读取HTML文件
    with open(html_path, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f.read(), 'html.parser')

    # 创建PPTX文档
    prs = Presentation()
    
    # 设置幻灯片宽高（16:9比例）
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)

    # 遍历所有幻灯片
    for slide_div in soup.select('div.slide'):
        # 创建新幻灯片
        slide_layout = prs.slide_layouts[5]  # 空白布局
        slide = prs.slides.add_slide(slide_layout)
        
        # 添加标题
        title = slide_div.select_one('.slide-title').get_text(strip=True)
        txBox = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12), Inches(1))
        tf = txBox.text_frame
        p = tf.add_paragraph()
        p.text = title
        p.font.bold = True
        p.font.size = Pt(28)

        # 处理内容区域
        content = slide_div.select_one('.slide-content')
        process_content_elements(content, slide)

    # 保存文件
    prs.save(pptx_path)

def process_content_elements(element, slide):
    # 递归处理内容元素
    for child in element.children:
        if child.name == 'div':
            class_list = child.get('class', [])
            
            if 'two-column' in class_list:
                process_two_columns(child, slide)
            elif 'info-box' in class_list:
                add_textbox(child, slide, 
                           left=Inches(0.5), top=Inches(1.5),
                           width=Inches(12), height=Inches(1.5),
                           bg_color=(238, 244, 252))  # info-box背景色
            elif 'warning-box' in class_list:
                add_textbox(child, slide,
                           left=Inches(0.5), top=Inches(3),
                           width=Inches(5.5), height=Inches(2),
                           bg_color=(253, 235, 231))  # warning-box背景色
            else:
                process_content_elements(child, slide)
                
        elif child.name in ['h3', 'h4']:
            add_heading(child, slide)
            
        elif child.name == 'ul':
            add_bullet_list(child, slide)

def add_textbox(element, slide, left, top, width, height, bg_color):
    # 添加带背景色的文本框
    textbox = slide.shapes.add_textbox(left, top, width, height)
    tf = textbox.text_frame
    tf.text = element.get_text(strip=True)
    
    # 设置背景颜色
    fill = textbox.fill
    fill.solid()
    fill.fore_color.rgb = RGBColor(*bg_color)
    
    # 设置字体样式
    for paragraph in tf.paragraphs:
        paragraph.font.size = Pt(12)
        paragraph.font.color.rgb = RGBColor(52, 73, 94)  # 深灰色

if __name__ == '__main__':
    input_html = '/Users/<USER>/Desktop/cursor_temp/gpu.html'
    output_pptx = '/Users/<USER>/Desktop/cursor_temp/presentation.pptx'
    convert_html_to_pptx(input_html, output_pptx)