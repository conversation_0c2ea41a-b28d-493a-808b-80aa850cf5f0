<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            padding: 0;
            font-size: 14px;
            color: #333;
        }
        .container {
            width: 800px; /* Adjust as needed */
            margin: 0 auto;
            position: relative;
        }
        h1 {
            font-size: 32px;
            margin-bottom: 30px;
            border-bottom: 2px dotted #ccc; /* Simulate the dotted line under Invoice */
            display: inline-block;
            padding-bottom: 5px;
        }
        .header-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .left-info, .right-info {
            flex: 1;
            padding-right: 20px;
        }
        .right-info {
            padding-left: 20px;
        }
        .info-block {
            margin-bottom: 15px;
        }
        .info-block div {
            padding: 2px 0;
        }
        .info-label {
            display: inline-block;
            width: 120px; /* Align labels */
            color: #666;
        }
        .address-block {
            line-height: 1.6;
            margin-top: 5px;
        }
        .address-block strong {
            display: block;
            margin-bottom: 5px;
        }
        .address-block span {
            display: block;
        }
        .table-container {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 8px 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #555;
            border-bottom: 2px solid #aaa; /* Stronger line under header */
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 5px;
        }
        .total-label {
            width: 120px;
            font-weight: bold;
            text-align: left;
        }
        .total-value {
            width: 100px;
            text-align: right;
        }
        .amount-due {
            font-size: 16px;
            margin-top: 15px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            font-weight: bold;
        }
        .bottom-info {
            margin-top: 50px;
            font-size: 12px;
            color: #777;
        }
        .gray-box {
            position: absolute;
            top: 0;
            right: 0;
            width: 50px; /* Adjust size as needed */
            height: 50px; /* Adjust size as needed */
            background-color: #ccc; /* Simulate gray color */
            /* This is a simple placeholder. The original image's shape is more complex. */
        }
        /* Dotted outlines - purely for visual simulation if you want */
        .dotted-outline {
            border: 1px dotted rgba(0,0,0,0.3);
            padding: 2px;
            display: inline-block;
        }
        /* More precise dotted outlines for blocks */
        .info-block div, .address-block, .table-container table, .total-section div, .bottom-info {
            box-sizing: border-box; /* Include padding and border in the element's total width and height */
        }
        .invoice-number-dotted, .date-dotted, .due-date-dotted {
            border: 1px dotted blue; /* Example for specific dotted borders */
            display: inline-block;
            padding: 0 5px;
        }
        .cursor-address-dotted, .cursor-contact-dotted {
            border: 1px dotted blue;
            display: block; /* For address lines */
            padding: 2px 5px;
        }
        .bill-to-header-dotted {
            border: 1px dotted red;
            display: inline-block;
            padding: 0 5px;
        }
        .bill-to-address-dotted, .bill-to-contact-dotted {
             border: 1px dotted blue;
             display: block;
             padding: 2px 5px;
        }
        .description-dotted, .qty-dotted, .unit-price-dotted, .amount-dotted {
            border: 1px dotted red;
            display: inline-block;
            padding: 0 5px;
        }
        .cursor-pro-dotted, .subtotal-dotted, .total-dotted, .amount-due-dotted, .anysphere-dotted {
            border: 1px dotted blue;
            display: inline-block;
            padding: 0 5px;
        }
        /* To simulate the exact dotted boxes, we'd need more specific div wrappers */
        /* For simplicity, the main CSS above doesn't include the dotted lines as real borders unless specified */
    </style>
</head>
<body>
    <div class="container">
        <div class="gray-box"></div>

        <h1>Invoice</h1>

        <div class="header-section">
            <div class="left-info">
                <div class="info-block">
                    <div class="invoice-number-dotted"><span class="info-label">Invoice number</span> INV-2024-6241</div>
                    <div class="date-dotted"><span class="info-label">Date of issue</span> July 20, 2025</div>
                    <div class="due-date-dotted"><span class="info-label">Date due</span> July 20, 2025</div>
                </div>

                <div class="address-block">
                    <div class="bill-to-header-dotted">Cursor</div>
                    <span class="cursor-address-dotted">801 West End Avenue</span>
                    <span class="cursor-address-dotted">New York, New York 10025</span>
                    <span class="cursor-address-dotted">United States</span>
                    <span class="cursor-contact-dotted">+1 831-425-9504</span>
                    <span class="cursor-contact-dotted"><EMAIL></span>
                </div>
            </div>

            <div class="right-info">
                <div class="address-block">
                    <div class="bill-to-header-dotted">Bill to</div>
                    <span class="bill-to-address-dotted">MARK GREEN</span>
                    <span class="bill-to-address-dotted">123 Maple Street</span>
                    <span class="bill-to-address-dotted">Anytown, CA 90210</span>
                    <span class="bill-to-address-dotted">United States</span>
                    <span class="bill-to-contact-dotted"><EMAIL></span>
                    <span class="bill-to-contact-dotted">IN GST 29ABCDE1234F243</span>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 60%;"><span class="description-dotted">Description</span></th>
                        <th class="text-right" style="width: 10%;"><span class="qty-dotted">Qty</span></th>
                        <th class="text-right" style="width: 15%;"><span class="unit-price-dotted">Unit price</span></th>
                        <th class="text-right" style="width: 15%;"><span class="amount-dotted">Amount</span></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="cursor-pro-dotted">Cursor Pro</span></td>
                        <td class="text-right"><span class="cursor-pro-dotted">1</span></td>
                        <td class="text-right"><span class="cursor-pro-dotted">$20.00</span></td>
                        <td class="text-right"><span class="cursor-pro-dotted">$20.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="cursor-pro-dotted">Jun 12 – Jul 12, 2025</span></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-row">
                    <div class="total-label"><span class="subtotal-dotted">Subtotal</span></div>
                    <div class="total-value"><span class="subtotal-dotted">$20.00</span></div>
                </div>
                <div class="total-row">
                    <div class="total-label"><span class="total-dotted">Total</span></div>
                    <div class="total-value"><span class="total-dotted">$20.00</span></div>
                </div>
                <div class="total-row amount-due">
                    <div class="total-label"><span class="amount-due-dotted">Amount due</span></div>
                    <div class="total-value"><span class="amount-due-dotted">$20.37 USD</span></div>
                </div>
            </div>
        </div>

        <div class="bottom-info">
            <span class="anysphere-dotted">Anysphere, Inc.</span><br>
            <span class="anysphere-dotted">US EIN 87-4436547</span>
        </div>
    </div>
</body>
</html>