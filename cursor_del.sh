#!/bin/bash

echo "开始彻底清理 Cursor..."

# 关闭 Cursor 进程
echo "正在关闭 Cursor 进程..."
pkill -f "Cursor" 2>/dev/null
pkill -f "cursor" 2>/dev/null
sleep 2

# 删除应用程序
echo "删除应用程序..."
rm -rf /Applications/Cursor.app

# 删除用户级缓存
echo "删除用户级缓存..."
rm -rf ~/Library/Caches/Cursor
rm -rf ~/Library/Caches/com.todesktop.230313mzl4w4u92*

# 删除应用支持文件
echo "删除应用支持文件..."
rm -rf ~/Library/Application\ Support/Cursor

# 删除偏好设置
echo "删除偏好设置..."
rm -rf ~/Library/Preferences/com.todesktop.230313mzl4w4u92*

# 删除日志文件
echo "删除日志文件..."
rm -rf ~/Library/Logs/Cursor

# 删除保存的应用状态
echo "删除应用状态..."
rm -rf ~/Library/Saved\ Application\ State/com.todesktop.230313mzl4w4u92*

# 删除WebKit缓存
echo "删除WebKit缓存..."
rm -rf ~/Library/Caches/com.todesktop.230313mzl4w4u92.webkit

# 删除用户配置
echo "删除用户配置..."
rm -rf ~/.cursor

# 补充清理项目

# 删除 HTTPStorages（网络存储）
echo "删除网络存储数据..."
rm -rf ~/Library/HTTPStorages/com.todesktop.230313mzl4w4u92*

# 删除 WebKit 存储
echo "删除WebKit存储..."
rm -rf ~/Library/WebKit/com.todesktop.230313mzl4w4u92*

# 删除 Containers 数据（沙盒数据）
echo "删除沙盒数据..."
rm -rf ~/Library/Containers/com.todesktop.230313mzl4w4u92*

# 删除 Group Containers 数据
echo "删除群组容器数据..."
rm -rf ~/Library/Group\ Containers/*cursor*
rm -rf ~/Library/Group\ Containers/*todesktop*

# 删除 Cookies 和 Internet 相关数据
echo "删除Cookies和网络数据..."
rm -rf ~/Library/Cookies/com.todesktop.230313mzl4w4u92*

# 删除扩展相关数据
echo "删除扩展数据..."
# VSCode/Cursor 扩展通常存储在以下位置
rm -rf ~/.vscode-oss/extensions
rm -rf ~/.cursor-server
rm -rf ~/.cursor-tutor

# 删除可能的符号链接和配置
echo "删除符号链接和配置..."
rm -rf ~/.local/share/cursor
rm -rf ~/.config/cursor

# 删除 LaunchServices 数据库中的记录
echo "清理LaunchServices数据库..."
/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -kill -r -domain local -domain system -domain user

# 删除可能的自动启动项
echo "删除自动启动项..."
rm -rf ~/Library/LaunchAgents/*cursor*
rm -rf ~/Library/LaunchAgents/*todesktop*

# 删除 Spotlight 索引中的相关数据
echo "重建Spotlight索引..."
sudo mdutil -i off /
sudo mdutil -E /
sudo mdutil -i on /

# 删除系统级缓存（需要管理员权限）
echo "删除系统级缓存（可能需要密码）..."
sudo rm -rf /Library/Caches/Cursor 2>/dev/null
sudo rm -rf /Library/Caches/com.todesktop.230313mzl4w4u92* 2>/dev/null

# 删除可能的系统级偏好设置
sudo rm -rf /Library/Preferences/com.todesktop.230313mzl4w4u92* 2>/dev/null

# 删除下载记录
echo "清理下载记录..."
rm -rf ~/Downloads/Cursor*.dmg 2>/dev/null
rm -rf ~/Downloads/cursor*.dmg 2>/dev/null

# 清理垃圾箱中可能的相关文件
echo "清理垃圾箱..."
rm -rf ~/.Trash/Cursor* 2>/dev/null

# 验证清理结果
echo "验证清理结果..."
echo "查找残留的 Cursor 文件："
find ~ -name "*cursor*" -o -name "*Cursor*" 2>/dev/null | head -10

echo "查找残留的 todesktop 文件："
find ~ -name "*todesktop*" 2>/dev/null | head -10

echo "Cursor 清理完成！"
echo "建议重启系统以确保所有更改生效。"
