import matplotlib.pyplot as plt
import numpy as np

def plot_eos_logits_over_tokens(single_prompt_data,save_path=None):
    """
    绘制单个prompt生成过程中EOS token的logits随token位置的变化
    
    参数:
    single_prompt_data: 单个prompt运行的详细数据，包含每个生成步骤的logits
    """
    EOS_TOKEN_ID = 50256  # 根据您使用的模型调整
    
    # 假设single_prompt_data包含一个'token_logits'列表
    # 每个元素是一个字典，表示该位置生成的所有token的logits
    token_positions = []
    eos_logits_per_step = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i+1)
        eos_logits_per_step.append(step_data['eos_probability'][0])
        
    # 绘制图表
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, eos_logits_per_step, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Logits')
    plt.title('EOS Token Logits Throughout Single Prompt Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        rate_save_path = save_path.replace('.png', '_rate.png')
        plt.tight_layout()
        plt.savefig(rate_save_path)
        print(f"变化率图表已保存到: {rate_save_path}")
    else:
        plt.tight_layout()
        plt.show()
    return plt


def plot_topk_logits_over_tokens(single_prompt_data,top_k=None,save_path=None):

    token_positions = []
    top_logits = []
    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i+1)

        if top_k==None:#只看top1的logits变化
            top_logits.append(list(step_data['top_tokens'].keys())[0])
        else:
            top_logits.append(np.mean(list(step_data['top_tokens'].keys())[top_k-1]))
    # import pdb; pdb.set_trace()
    # 绘制图表
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, top_logits, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Logits')
    plt.title('EOS Token Logits Throughout Single Prompt Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    if save_path:
        rate_save_path = save_path.replace('.png', '_rate.png')
        plt.tight_layout()
        plt.savefig(rate_save_path)
        print(f"变化率图表已保存到: {rate_save_path}")
    else:
        plt.tight_layout()
        plt.show()
    return plt

def plot_topk_logits_statistics(single_prompt_data, top_k=5, save_path=None):
    """
    计算并绘制top-k logits的各种统计量随token位置的变化
    
    参数:
    single_prompt_data: 单个prompt运行的详细数据
    top_k: 要分析的top k个logits数量
    save_path: 保存图表的路径前缀
    """
    token_positions = []
    stats = {
        'variance': [],
        'mean': [],
        'std': [],
        'max': [],
        'min': [],
        'range': []
    }
    
    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i+1)
        # 获取top-k logits
        top_k_logits = list(step_data['top_tokens'].keys())[:top_k]
        
        # 计算各种统计量
        stats['variance'].append(np.var(top_k_logits))
        stats['mean'].append(np.mean(top_k_logits))
        stats['std'].append(np.std(top_k_logits))
        stats['max'].append(np.max(top_k_logits))
        stats['min'].append(np.min(top_k_logits))
        stats['range'].append(np.max(top_k_logits) - np.min(top_k_logits))
    
    # 为每个统计量创建单独的图表
    for stat_name, stat_values in stats.items():
        plt.figure(figsize=(12, 6))
        plt.plot(token_positions, stat_values, marker='o', linestyle='-')
        plt.xlabel('Token Position')
        plt.ylabel(f'{stat_name.capitalize()} of Top-{top_k} Logits')
        plt.title(f'{stat_name.capitalize()} of Top-{top_k} Logits Throughout Generation')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save_path:
            stat_save_path = f"{save_path}_{stat_name}_top{top_k}.png"
            plt.savefig(stat_save_path)
            print(f"{stat_name}统计图表已保存到: {stat_save_path}")
        else:
            plt.show()
        plt.close()
    
    return stats