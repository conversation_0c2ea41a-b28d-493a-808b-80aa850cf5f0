import matplotlib.pyplot as plt
import numpy as np
import scipy.stats as stats
import os
import torch

def plot_eos_logits_over_tokens(single_prompt_data, save_path=None):
    """
    Plot the logits of the EOS token during the generation of a single prompt.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run, containing logits for each generation step.
    """
    token_positions = []
    eos_logits_per_step = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        eos_logits_per_step.append(step_data['eos_probability'])
        
    # Plot the chart
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, eos_logits_per_step, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Logits')
    plt.title('EOS Token Logits Throughout Single Prompt Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"EOS Token Logits chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_topk_logits_over_tokens(single_prompt_data, top_k=None, save_path=None):
    token_positions = []
    top_logits = []
    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)

        if top_k is None:  # Only look at the top 1 logits
            top_logits.append(list(step_data['top_tokens'].values())[0])
        else:
            top_logits.append(np.mean(list(step_data['top_tokens'].values())[:top_k]))

    # Plot the chart
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, top_logits, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('Top-k Token Logits')
    plt.title('Top-k Token Logits Throughout Single Prompt Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Top-k Token Logits chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_topk_logits_statistics(single_prompt_data, top_k=5, save_path=None):
    """
    Calculate and plot various statistics of top-k logits over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    top_k: Number of top k logits to analyze.
    save_path: Prefix for saving the chart.
    """
    token_positions = []
    stats = {
        'variance': [],
        'mean': [],
        'std': [],
        'max': [],
        'min': [],
        'range': []
    }
    
    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        # Get top-k logits
        top_k_logits = list(step_data['top_tokens'].values())[:top_k]
        # Calculate various statistics
        stats['variance'].append(np.var(top_k_logits))
        stats['mean'].append(np.mean(top_k_logits))
        stats['std'].append(np.std(top_k_logits))
        stats['max'].append(np.max(top_k_logits))
        stats['min'].append(np.min(top_k_logits))
        stats['range'].append(np.max(top_k_logits) - np.min(top_k_logits))
    
    print(f"\n=== Top-{top_k} Logits Statistics ===")
    print(f"Analyzed {len(token_positions)} tokens' logits")
    print(f"Statistics Overview:")
    for stat_name, values in stats.items():
        print(f"  {stat_name.capitalize()}: Mean = {np.mean(values):.4f}, Min = {np.min(values):.4f}, Max = {np.max(values):.4f}")
    
    # Create separate charts for each statistic
    for stat_name, stat_values in stats.items():
        plt.figure(figsize=(12, 6))
        plt.plot(token_positions, stat_values, marker='o', linestyle='-')
        plt.xlabel('Token Position')
        plt.ylabel(f'{stat_name.capitalize()} of Top-{top_k} Logits')
        plt.title(f'{stat_name.capitalize()} of Top-{top_k} Logits Throughout Generation')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        if save_path:
            stat_save_path = f"{save_path}_{stat_name}_top{top_k}.png"
            plt.savefig(stat_save_path)
            print(f"{stat_name} statistics chart saved to: {stat_save_path}")
        else:
            plt.show()
        plt.close()
    
    return stats

def plot_eos_rank_over_tokens(single_prompt_data, save_path=None):
    """
    Plot the rank of the EOS token over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    save_path: Path to save the chart.
    """
    token_positions = []
    eos_ranks = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        # Get the rank of the EOS token
        eos_rank = step_data.get('eos_rank', 0)  # Assume eos_rank is in step_data
        eos_ranks.append(eos_rank)
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, eos_ranks, marker='o', linestyle='-', color='blue')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Rank')
    plt.title('EOS Token Rank Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"EOS Token Rank chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_entropy_over_tokens(single_prompt_data, save_path=None):
    """
    Plot the entropy of the candidate token distribution over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    save_path: Path to save the chart.
    """
    token_positions = []
    entropies = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        # Calculate the entropy of candidate tokens
        probs = list(step_data['top_tokens'].values())
        entropy = -np.sum(probs * np.log2(probs))
        entropies.append(entropy)
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, entropies, marker='o', linestyle='-', color='red')
    plt.xlabel('Token Position')
    plt.ylabel('Entropy of Candidate Tokens')
    plt.title('Entropy of Candidate Tokens Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Entropy chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_mean_logits_over_tokens(single_prompt_data, save_path=None):
    """
    Plot the mean logits of all tokens over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    save_path: Path to save the chart.
    """
    token_positions = []
    mean_logits = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        # Calculate the mean of all tokens' logits
        logits = list(step_data['top_tokens'].values())
        mean_logit = np.mean(logits)
        mean_logits.append(mean_logit)
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, mean_logits, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('Mean Logits of All Tokens')
    plt.title('Mean Logits of All Tokens Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Mean Logits chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_topk_entropy_over_tokens(single_prompt_data, top_k=None, save_path=None):
    """
    Plot the entropy of the top-k token probability distribution over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    top_k: Number of top k tokens to analyze.
    save_path: Path to save the chart.
    """
    token_positions = []
    topk_entropies = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        topk_entropies.append(step_data['topk_entropy'])
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, topk_entropies, marker='o', linestyle='-', color='purple')
    plt.xlabel('Token Position')
    plt.ylabel('Entropy of Top-k Tokens')
    plt.title(f'Entropy of Top-k Tokens Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Top-k Entropy chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_cosine_similarity_over_tokens(tracked_steps_logits, eos_embedding, save_path=None):
    """Plot the cosine similarity between logits and EOS embedding over time.
    
    Args:
        tracked_steps_logits: Recorded logits data for each step.
        eos_embedding: EOS token embedding vector.
        save_path: Path to save the chart.
    """
    # Calculate cosine similarities
    cosine_similarities = []
    for step_data in tracked_steps_logits:
        logits = np.array(list(step_data['top_tokens'].values()))
        # Ensure dimension matching
        if len(logits) < len(eos_embedding):
            padded_logits = np.zeros(len(eos_embedding))
            padded_logits[:len(logits)] = logits
            logits = padded_logits
        elif len(logits) > len(eos_embedding):
            logits = logits[:len(eos_embedding)]
        
        cosine_similarity = np.dot(logits, eos_embedding) / (np.linalg.norm(logits) * np.linalg.norm(eos_embedding))
        cosine_similarities.append(cosine_similarity)
    
    # Create the chart
    plt.figure(figsize=(12, 6))
    plt.plot(cosine_similarities, marker='o')
    plt.title('Cosine Similarity with EOS Embedding Over Time')
    plt.xlabel('Generation Step')
    plt.ylabel('Cosine Similarity')
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        print(f"Cosine Similarity chart saved to: {save_path}")
    plt.close()

def plot_kurtosis_skewness_over_tokens(single_prompt_data, save_path=None):
    """
    Plot the kurtosis and skewness of all logits over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    save_path: Path to save the chart.
    """
    token_positions = []
    kurtosis_values = []
    skewness_values = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        logits = list(step_data['top_tokens'].values())
        kurtosis = stats.kurtosis(logits)
        skewness = stats.skew(logits)
        kurtosis_values.append(kurtosis)
        skewness_values.append(skewness)
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, kurtosis_values, marker='o', linestyle='-', color='brown', label='Kurtosis')
    plt.plot(token_positions, skewness_values, marker='o', linestyle='-', color='gray', label='Skewness')
    plt.xlabel('Token Position')
    plt.ylabel('Kurtosis and Skewness')
    plt.title('Kurtosis and Skewness of All Logits Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Kurtosis and Skewness chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_percentile_entropies_over_tokens(single_prompt_data, percentiles=None, save_path=None):
    """
    Plot the entropy of different percentile logits over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    percentiles: List of percentiles to plot.
    save_path: Path to save the chart.
    """
    if percentiles is None:
        percentiles = [0.1, 0.2, 0.01, 0.001]  # 默认值
        
    token_positions = []
    percentile_entropies = {f'{p:.1f}': [] for p in percentiles}

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        for percentile in percentile_entropies.keys():
            percentile_entropies[percentile].append(step_data['percentile_entropies'][percentile])
    
    plt.figure(figsize=(12, 6))
    for percentile, entropies in percentile_entropies.items():
        plt.plot(token_positions, entropies, marker='o', linestyle='-', label=f'{float(percentile)*100}%')
    
    plt.xlabel('Token Position')
    plt.ylabel('Entropy')
    plt.title('Entropy of Different Percentile Logits Throughout Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Percentile Entropies chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_deviation_over_tokens(single_prompt_data, top_k=None, save_path=None):
    """
    Plot the deviation of top-k logits from the mean of all logits over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    top_k: Number of top tokens to analyze.
    save_path: Path to save the chart.
    """
    token_positions = []
    mean_deviations = []
    max_deviations = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        mean_deviations.append(step_data['mean_deviation'])
        max_deviations.append(step_data['max_deviation'])
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, mean_deviations, marker='o', linestyle='-', color='blue', label='Mean Deviation')
    plt.plot(token_positions, max_deviations, marker='o', linestyle='-', color='red', label='Max Deviation')
    plt.xlabel('Token Position')
    plt.ylabel('Deviation from Mean of All Logits')
    plt.title(f'Deviation of Top-k Logits from Mean of All Logits')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Deviation chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_historical_deviation_over_tokens(single_prompt_data, top_k=None, save_path=None):
    """
    Plot the deviation of top-k logits from historical average over time.
    
    Parameters:
    single_prompt_data: Detailed data for a single prompt run.
    top_k: Number of top tokens to analyze.
    save_path: Path to save the chart.
    """
    token_positions = []
    historical_mean_deviations = []
    historical_max_deviations = []

    for i, step_data in enumerate(single_prompt_data):
        token_positions.append(i + 1)
        historical_mean_deviations.append(step_data['historical_mean_deviation'])
        historical_max_deviations.append(step_data['historical_max_deviation'])
    
    plt.figure(figsize=(12, 6))
    plt.plot(token_positions, historical_mean_deviations, marker='o', linestyle='-', color='blue', label='Mean Historical Deviation')
    plt.plot(token_positions, historical_max_deviations, marker='o', linestyle='-', color='red', label='Max Historical Deviation')
    plt.xlabel('Token Position')
    plt.ylabel('Deviation from Historical Average')
    plt.title(f'Deviation of Top-k Logits from Historical Average')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Historical Deviation chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_attention_to_previous_tokens(attention_data, save_path=None):
    """
    绘制当前step对前面token的注意力分布。
    
    Parameters:
    attention_data: 注意力数据
    save_path: 保存路径
    """
    # 获取所有层的注意力数据
    layer_attentions = {}
    for step_data in attention_data:
        layer_idx = step_data['layer_idx']
        if layer_idx not in layer_attentions:
            layer_attentions[layer_idx] = []
        layer_attentions[layer_idx].append(step_data['metrics']['attention_to_previous'])
    
    # 绘制每个层的注意力分布
    plt.figure(figsize=(12, 6))
    for layer_idx, attentions in layer_attentions.items():
        # 计算每个位置的平均注意力
        mean_attention = np.mean(attentions, axis=0)
        plt.plot(mean_attention, label=f'Layer {layer_idx}')
    
    plt.xlabel('Previous Token Position')
    plt.ylabel('Attention Weight')
    plt.title('Attention Distribution to Previous Tokens')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Attention to Previous Tokens chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_head_consistency(attention_data, save_path=None):
    """
    绘制注意力头一致性随时间的变化。
    
    Parameters:
    attention_data: 注意力数据
    save_path: 保存路径
    """
    # 获取所有层的头一致性数据
    layer_consistencies = {}
    for step_data in attention_data:
        layer_idx = step_data['layer_idx']
        if layer_idx not in layer_consistencies:
            layer_consistencies[layer_idx] = []
        layer_consistencies[layer_idx].append(step_data['metrics']['head_consistency'])
    
    # 绘制每个层的头一致性
    plt.figure(figsize=(12, 6))
    for layer_idx, consistencies in layer_consistencies.items():
        plt.plot(consistencies, label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Position')
    plt.ylabel('Head Consistency (Cosine Similarity)')
    plt.title('Attention Head Consistency Over Time')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Head Consistency chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_eos_attention(attention_data, save_path=None):
    """
    绘制各层对EOS token的注意力随时间的变化。
    
    Parameters:
    attention_data: 注意力数据
    save_path: 保存路径
    """
    # 获取所有层对EOS的注意力数据
    layer_eos_attentions = {}
    for step_data in attention_data:
        layer_idx = step_data['layer_idx']
        if layer_idx not in layer_eos_attentions:
            layer_eos_attentions[layer_idx] = []
        layer_eos_attentions[layer_idx].append(step_data['metrics']['eos_attention'])
    
    # 绘制每个层对EOS的注意力
    plt.figure(figsize=(12, 6))
    for layer_idx, attentions in layer_eos_attentions.items():
        plt.plot(attentions, label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Position')
    plt.ylabel('Attention to EOS Token')
    plt.title('Attention to EOS Token Over Time')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"EOS Attention chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_attention_entropy(attention_data, save_path=None):
    """
    绘制注意力熵随时间的变化。
    
    Parameters:
    attention_data: 注意力数据
    save_path: 保存路径
    """
    # 获取所有层的注意力熵数据
    layer_entropies = {}
    for step_data in attention_data:
        layer_idx = step_data['layer_idx']
        if layer_idx not in layer_entropies:
            layer_entropies[layer_idx] = []
        layer_entropies[layer_idx].append(step_data['metrics']['attention_entropy'])
    
    # 绘制每个层的注意力熵
    plt.figure(figsize=(12, 6))
    for layer_idx, entropies in layer_entropies.items():
        plt.plot(entropies, label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Position')
    plt.ylabel('Attention Entropy')
    plt.title('Attention Entropy Over Time')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Attention Entropy chart saved to: {save_path}")
    else:
        plt.show()
    return plt

def plot_layer_attention_entropy(attention_data, layer_indices=None, save_path=None):
    """
    绘制每一层的注意力熵随token变化的图表。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_indices: 要绘制的层索引，如果为None则绘制所有层
        save_path: 图表保存路径
    """
    # 按层索引分组
    layer_data = {}
    for item in attention_data:
        layer_idx = item['layer_idx']
        if layer_indices is not None and layer_idx not in layer_indices:
            continue
        
        if layer_idx not in layer_data:
            layer_data[layer_idx] = []
        
        # 确保有指标数据
        if 'metrics' in item and 'avg_entropy' in item['metrics']:
            token_idx = item['token_idx']
            entropy = item['metrics']['avg_entropy']
            layer_data[layer_idx].append((token_idx, entropy))
    
    # 没有数据则返回
    if not layer_data:
        print("警告：没有找到有效的注意力熵数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    for layer_idx, data in sorted(layer_data.items()):
        if not data:
            continue
        
        # 提取token索引和熵值
        token_indices = [item[0] for item in data]
        entropies = [item[1] for item in data]
        
        # 绘制该层的数据
        plt.plot(token_indices, entropies, marker='o', label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Entropy')
    plt.title('Attention Entropy by Layer over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力熵图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_head_attention_entropy(attention_data, layer_idx, head_indices=None, save_path=None):
    """
    绘制特定层中每个注意力头的熵变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 要绘制的层索引
        head_indices: 要绘制的头索引，如果为None则绘制所有头
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 提取每个头的熵数据
    head_data = {}
    for item in layer_items:
        if 'metrics' in item and 'head_entropies' in item['metrics']:
            token_idx = item['token_idx']
            head_entropies = item['metrics']['head_entropies']
            
            for head_idx, entropy in enumerate(head_entropies):
                if head_indices is not None and head_idx not in head_indices:
                    continue
                
                if head_idx not in head_data:
                    head_data[head_idx] = []
                
                head_data[head_idx].append((token_idx, entropy))
    
    # 没有数据则返回
    if not head_data:
        print(f"警告：层{layer_idx}的注意力头没有有效的熵数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    for head_idx, data in sorted(head_data.items()):
        if not data:
            continue
        
        # 提取token索引和熵值
        token_indices = [item[0] for item in data]
        entropies = [item[1] for item in data]
        
        # 绘制该头的数据
        plt.plot(token_indices, entropies, marker='o', label=f'Head {head_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Entropy')
    plt.title(f'Layer {layer_idx} - Attention Entropy by Head over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"层{layer_idx}的注意力头熵图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_change_rate(attention_data, layer_indices=None, save_path=None):
    """
    绘制每一层的注意力变化率随token变化的图表。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_indices: 要绘制的层索引，如果为None则绘制所有层
        save_path: 图表保存路径
    """
    # 按层索引分组
    layer_data = {}
    for item in attention_data:
        layer_idx = item['layer_idx']
        if layer_indices is not None and layer_idx not in layer_indices:
            continue
        
        if layer_idx not in layer_data:
            layer_data[layer_idx] = []
        
        # 确保有变化率数据
        if 'change_rate' in item and 'avg_change_rate' in item['change_rate']:
            token_idx = item['token_idx']
            change_rate = item['change_rate']['avg_change_rate']
            layer_data[layer_idx].append((token_idx, change_rate))
    
    # 没有数据则返回
    if not layer_data:
        print("警告：没有找到有效的注意力变化率数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    for layer_idx, data in sorted(layer_data.items()):
        if not data:
            continue
        
        # 提取token索引和变化率
        token_indices = [item[0] for item in data]
        change_rates = [item[1] for item in data]
        
        # 绘制该层的数据
        plt.plot(token_indices, change_rates, marker='o', label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Change Rate (L2 Norm)')
    plt.title('Attention Weight Change Rate by Layer over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力变化率图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_head_attention_change_rate(attention_data, layer_idx, head_indices=None, save_path=None):
    """
    绘制特定层中每个注意力头的变化率。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 要绘制的层索引
        head_indices: 要绘制的头索引，如果为None则绘制所有头
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 提取每个头的变化率数据
    head_data = {}
    for item in layer_items:
        if 'change_rate' in item and 'head_change_rates' in item['change_rate']:
            token_idx = item['token_idx']
            head_rates = item['change_rate']['head_change_rates']
            
            # 确定头的数量
            num_heads = len(head_rates) // item['attention_weights'].shape[1]  # 假设每个batch有相同数量的头
            
            # 按头分组
            for i in range(len(head_rates)):
                head_idx = i % num_heads
                
                if head_indices is not None and head_idx not in head_indices:
                    continue
                
                if head_idx not in head_data:
                    head_data[head_idx] = []
                
                head_data[head_idx].append((token_idx, head_rates[i]))
    
    # 没有数据则返回
    if not head_data:
        print(f"警告：层{layer_idx}的注意力头没有有效的变化率数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    for head_idx, data in sorted(head_data.items()):
        if not data:
            continue
        
        # 提取token索引和变化率
        token_indices = [item[0] for item in data]
        change_rates = [item[1] for item in data]
        
        # 绘制该头的数据
        plt.plot(token_indices, change_rates, marker='o', label=f'Head {head_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Change Rate (L2 Norm)')
    plt.title(f'Layer {layer_idx} - Attention Weight Change Rate by Head over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"层{layer_idx}的注意力头变化率图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_layer_embedding_changes(embedding_data, layer_name, token_indices=None, save_path=None):
    """
    绘制指定层的embedding随token变化的图表。
    
    Args:
        embedding_data: 包含embedding数据的字典
        layer_name: 要绘制的层名称
        token_indices: 要包含的token索引，如果为None则包含所有token
        save_path: 图表保存路径
    """
    # 确保有该层的数据
    if layer_name not in embedding_data:
        print(f"警告：没有找到层 '{layer_name}' 的embedding数据")
        return
    
    layer_embeddings = embedding_data[layer_name]
    
    # 筛选指定的token
    if token_indices is not None:
        filtered_embeddings = [(idx, emb) for idx, emb in layer_embeddings if idx in token_indices]
    else:
        filtered_embeddings = layer_embeddings
    
    if not filtered_embeddings:
        print(f"警告：层 '{layer_name}' 没有符合条件的embedding数据")
        return
    
    # 提取token索引和embedding
    token_indices = [item[0] for item in filtered_embeddings]
    embeddings = [item[1] for item in filtered_embeddings]
    
    # 计算每个embedding的范数（作为一种嵌入变化的度量）
    embedding_norms = [np.linalg.norm(emb) for emb in embeddings]
    
    # 如果有多个token，计算相邻embedding之间的差异
    if len(embeddings) > 1:
        embedding_changes = []
        for i in range(1, len(embeddings)):
            # 计算欧氏距离
            diff = np.linalg.norm(embeddings[i] - embeddings[i-1])
            embedding_changes.append((token_indices[i], diff))
        
        # 绘制embedding变化图
        plt.figure(figsize=(12, 6))
        plt.plot([item[0] for item in embedding_changes], [item[1] for item in embedding_changes], 
                 marker='o', label=f'{layer_name} Embedding Changes')
        plt.xlabel('Token Index')
        plt.ylabel('Embedding Change (L2 Distance)')
        plt.title(f'Layer {layer_name} - Embedding Changes Between Consecutive Tokens')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save_path:
            change_path = f"{save_path.rsplit('.', 1)[0]}_changes.{save_path.rsplit('.', 1)[1]}"
            plt.savefig(change_path)
            print(f"Embedding变化图表已保存到: {change_path}")
        else:
            plt.show()
        plt.close()
    
    # 绘制embedding范数图
    plt.figure(figsize=(12, 6))
    plt.plot(token_indices, embedding_norms, marker='o', label=f'{layer_name} Embedding Norms')
    plt.xlabel('Token Index')
    plt.ylabel('Embedding Norm')
    plt.title(f'Layer {layer_name} - Embedding Norms over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"Embedding范数图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_weights_heatmap(attention_data, layer_idx, head_idx, save_path=None):
    """
    绘制特定层、特定头的注意力权重热力图。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 层索引
        head_idx: 头索引
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 提取注意力权重矩阵
    attention_matrices = []
    token_indices = []
    
    for item in layer_items:
        weights = np.array(item['metrics']['attention_weights'][head_idx])
        attention_matrices.append(weights)
        token_indices.append(item['token_idx'])
    
    if not attention_matrices:
        print(f"警告：层{layer_idx}的头{head_idx}没有注意力权重数据")
        return
    
    # 创建一个大的热力图，展示所有token的注意力权重
    # 热力图的每一行对应一个生成的token，每一列对应该token对输入序列中每个token的注意力权重
    num_tokens = len(attention_matrices)
    max_seq_len = max([w.shape[0] for w in attention_matrices])
    
    # 创建一个填充后的注意力矩阵
    attention_heatmap = np.zeros((num_tokens, max_seq_len))
    for i, weights in enumerate(attention_matrices):
        seq_len = weights.shape[0]
        attention_heatmap[i, :seq_len] = weights
    
    # 绘制热力图
    plt.figure(figsize=(12, 8))
    plt.imshow(attention_heatmap, aspect='auto', cmap='viridis')
    plt.colorbar(label='Attention Weight')
    plt.xlabel('Token Position in Sequence')
    plt.ylabel('Generated Token Index')
    plt.title(f'Layer {layer_idx}, Head {head_idx} - Attention Weights Heatmap')
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力权重热力图已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_entropy_by_layer(attention_data, save_path=None):
    """
    绘制每一层的平均注意力熵随token生成过程的变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        save_path: 图表保存路径
    """
    # 按层索引和token索引组织数据
    layer_entropy = {}
    token_indices = set()
    
    for item in attention_data:
        layer_idx = item['layer_idx']
        token_idx = item['token_idx']
        
        if layer_idx not in layer_entropy:
            layer_entropy[layer_idx] = {}
        
        layer_entropy[layer_idx][token_idx] = item['metrics']['avg_attention_entropy']
        token_indices.add(token_idx)
    
    if not layer_entropy:
        print("警告：没有找到注意力熵数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    for layer_idx, entropies in sorted(layer_entropy.items()):
        tokens = sorted(entropies.keys())
        entropy_values = [entropies[t] for t in tokens]
        
        plt.plot(tokens, entropy_values, marker='o', label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Average Attention Entropy')
    plt.title('Average Attention Entropy by Layer Over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力熵图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_head_entropy(attention_data, layer_idx, save_path=None):
    """
    绘制特定层中每个注意力头的熵随token生成过程的变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 要绘制的层索引
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 获取头的数量
    num_heads = len(layer_items[0]['metrics']['attention_entropy'])
    
    # 按头和token索引组织数据
    head_entropy = {}
    for head_idx in range(num_heads):
        head_entropy[head_idx] = {}
    
    for item in layer_items:
        token_idx = item['token_idx']
        for head_idx in range(num_heads):
            head_entropy[head_idx][token_idx] = item['metrics']['attention_entropy'][head_idx]
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    for head_idx, entropies in sorted(head_entropy.items()):
        tokens = sorted(entropies.keys())
        entropy_values = [entropies[t] for t in tokens]
        
        plt.plot(tokens, entropy_values, marker='o', label=f'Head {head_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Entropy')
    plt.title(f'Layer {layer_idx} - Attention Entropy by Head Over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力头熵图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_input_attention_ratio(attention_data, save_path=None):
    """
    绘制每一层对输入序列的注意力权重比值随token生成过程的变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        save_path: 图表保存路径
    """
    # 按层索引和token索引组织数据
    layer_ratios = {}
    token_indices = set()
    
    for item in attention_data:
        layer_idx = item['layer_idx']
        token_idx = item['token_idx']
        
        if layer_idx not in layer_ratios:
            layer_ratios[layer_idx] = {}
        
        layer_ratios[layer_idx][token_idx] = item['metrics']['avg_input_tokens_ratio']
        token_indices.add(token_idx)
    
    if not layer_ratios:
        print("警告：没有找到注意力比值数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    for layer_idx, ratios in sorted(layer_ratios.items()):
        tokens = sorted(ratios.keys())
        ratio_values = [ratios[t] for t in tokens]
        
        plt.plot(tokens, ratio_values, marker='o', label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Input Tokens Attention Ratio')
    plt.title('Attention to Input Tokens Ratio by Layer Over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"输入注意力比值图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_change_rate_by_layer(attention_data, save_path=None):
    """
    绘制每一层的注意力变化率随token生成过程的变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        save_path: 图表保存路径
    """
    # 按层索引和token索引组织数据
    layer_change_rates = {}
    token_indices = set()
    
    for item in attention_data:
        layer_idx = item['layer_idx']
        token_idx = item['token_idx']
        
        if 'change_rate' in item and 'avg_change_rate' in item['change_rate']:
            if layer_idx not in layer_change_rates:
                layer_change_rates[layer_idx] = {}
            
            layer_change_rates[layer_idx][token_idx] = item['change_rate']['avg_change_rate']
            token_indices.add(token_idx)
    
    if not layer_change_rates:
        print("警告：没有找到注意力变化率数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    for layer_idx, rates in sorted(layer_change_rates.items()):
        tokens = sorted(rates.keys())
        rate_values = [rates[t] for t in tokens]
        
        plt.plot(tokens, rate_values, marker='o', label=f'Layer {layer_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Change Rate (L2 Norm)')
    plt.title('Attention Change Rate by Layer Over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力变化率图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_head_change_rate(attention_data, layer_idx, save_path=None):
    """
    绘制特定层中每个注意力头的变化率随token生成过程的变化。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 要绘制的层索引
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 检查是否有变化率数据
    valid_items = [item for item in layer_items if 'change_rate' in item and 'head_change_rates' in item['change_rate']]
    if not valid_items:
        print(f"警告：层{layer_idx}没有有效的变化率数据")
        return
    
    # 获取头的数量
    num_heads = len(valid_items[0]['change_rate']['head_change_rates'])
    
    # 按头和token索引组织数据
    head_change_rates = {}
    for head_idx in range(num_heads):
        head_change_rates[head_idx] = {}
    
    for item in valid_items:
        token_idx = item['token_idx']
        head_rates = item['change_rate']['head_change_rates']
        
        for head_idx in range(min(num_heads, len(head_rates))):
            head_change_rates[head_idx][token_idx] = head_rates[head_idx]
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    for head_idx, rates in sorted(head_change_rates.items()):
        tokens = sorted(rates.keys())
        rate_values = [rates[t] for t in tokens]
        
        plt.plot(tokens, rate_values, marker='o', label=f'Head {head_idx}')
    
    plt.xlabel('Token Index')
    plt.ylabel('Attention Change Rate (L2 Norm)')
    plt.title(f'Layer {layer_idx} - Attention Change Rate by Head Over Token Generation')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力头变化率图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def plot_attention_metrics_summary(attention_data, layer_idx, save_path=None):
    """
    为特定层绘制注意力指标汇总图表。
    
    Args:
        attention_data: 包含所有注意力数据的列表
        layer_idx: 要绘制的层索引
        save_path: 图表保存路径
    """
    # 筛选特定层的数据
    layer_items = [item for item in attention_data if item['layer_idx'] == layer_idx]
    
    if not layer_items:
        print(f"警告：层{layer_idx}没有数据")
        return
    
    # 提取各项指标数据
    token_indices = []
    entropy_values = []
    input_ratio_values = []
    change_rate_values = []
    
    for item in layer_items:
        token_idx = item['token_idx']
        token_indices.append(token_idx)
        
        # 注意力熵
        entropy = item['metrics'].get('avg_attention_entropy', 0)
        entropy_values.append(entropy)
        
        # 输入序列注意力比值
        input_ratio = item['metrics'].get('avg_input_tokens_ratio', 0)
        input_ratio_values.append(input_ratio)
        
        # 注意力变化率
        if 'change_rate' in item and 'avg_change_rate' in item['change_rate']:
            change_rate = item['change_rate']['avg_change_rate']
        else:
            change_rate = 0
        change_rate_values.append(change_rate)
    
    # 创建子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))
    
    # 绘制注意力熵
    ax1.plot(token_indices, entropy_values, marker='o', color='blue')
    ax1.set_xlabel('Token Index')
    ax1.set_ylabel('Average Attention Entropy')
    ax1.set_title(f'Layer {layer_idx} - Average Attention Entropy')
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 绘制输入序列注意力比值
    ax2.plot(token_indices, input_ratio_values, marker='o', color='green')
    ax2.set_xlabel('Token Index')
    ax2.set_ylabel('Input Tokens Attention Ratio')
    ax2.set_title(f'Layer {layer_idx} - Input Tokens Attention Ratio')
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 绘制注意力变化率
    ax3.plot(token_indices, change_rate_values, marker='o', color='red')
    ax3.set_xlabel('Token Index')
    ax3.set_ylabel('Attention Change Rate (L2 Norm)')
    ax3.set_title(f'Layer {layer_idx} - Attention Change Rate')
    ax3.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"注意力指标汇总图表已保存到: {save_path}")
    else:
        plt.show()
    plt.close()

def calculate_input_attention_ratio(attention_maps, input_seq_len):
    """
    计算当前token对输入序列的注意力权重与对所有token的注意力权重的比值
    
    Args:
        attention_maps: 注意力权重列表，每个元素形状为[num_heads, seq_len, seq_len]或[num_heads, 1, seq_len]
        input_seq_len: 输入序列长度
    
    Returns:
        input_ratio_by_layer: 每层的输入注意力比例，字典格式，键为层索引，值为列表形式的比例值
    """
    input_ratio_by_layer = {}
    
    # 遍历每层的注意力图
    for layer_idx, attn_map in enumerate(attention_maps):
        if layer_idx not in input_ratio_by_layer:
            input_ratio_by_layer[layer_idx] = []

        # 确定是prefill阶段还是生成阶段
        is_prefill = attn_map.shape[1] > 1
        
        if is_prefill:
            # Prefill阶段，注意力形状为[num_heads, seq_len, seq_len]
            # 我们只考虑最后一个token的注意力
            last_token_attn = attn_map[:, -1, :]  # [num_heads, seq_len]
            
            input_attn_sum = last_token_attn[:, :input_seq_len].sum(dim=1)  # [num_heads]

            total_attn_sum = last_token_attn.sum(dim=1)  # [num_heads]
            
            # 计算比值并在所有头上平均
            ratio = (input_attn_sum / total_attn_sum).mean().item()
            input_ratio_by_layer[layer_idx].append(ratio)
        else:
            # 生成阶段，注意力形状为[num_heads, 1, seq_len]
            # 计算对输入序列的注意力总和
            input_attn_sum = attn_map[:, 0, :input_seq_len].sum(dim=1)  # [num_heads]
            total_attn_sum = attn_map[:, 0, :].sum(dim=1)  # [num_heads]
            
            # 计算比值并在所有头上平均
            ratio = (input_attn_sum / total_attn_sum).mean().item()
            input_ratio_by_layer[layer_idx].append(ratio)
    
    return input_ratio_by_layer

def calculate_attention_entropy(attention_maps):
    """
    计算注意力分布的熵值，反映注意力集中程度
    
    Args:
        attention_maps: 注意力权重列表，每个元素形状为[num_heads, seq_len, seq_len]或[num_heads, 1, seq_len]
    
    Returns:
        entropy_by_layer: 每层的注意力熵，字典格式，键为层索引，值为列表形式的熵值
    """
    entropy_by_layer = {}
    
    # 遍历每层的注意力图
    for layer_idx, attn_map in enumerate(attention_maps):
        if layer_idx not in entropy_by_layer:
            entropy_by_layer[layer_idx] = []
        
        # 确定是prefill阶段还是生成阶段
        is_prefill = attn_map.shape[1] > 1
        
        if is_prefill:
            # Prefill阶段，我们只考虑最后一个token的注意力分布
            last_token_attn = attn_map[:, -1, :]  # [num_heads, seq_len]
            
            # 计算每个头的注意力分布
            attn_probs = last_token_attn.softmax(dim=1)  # [num_heads, seq_len]
            
            # 计算每个头的熵
            eps = 1e-10  # 防止log(0)
            head_entropies = -torch.sum(attn_probs * torch.log2(attn_probs + eps), dim=1)  # [num_heads]
            
            # 取所有头的平均熵作为该层的熵值
            entropy = head_entropies.mean().item()
            entropy_by_layer[layer_idx].append(entropy)
        else:
            # 生成阶段，我们考虑当前token的注意力分布
            # 计算每个头的注意力分布
            attn_probs = attn_map[:, 0, :].softmax(dim=1)  # [num_heads, seq_len]
            
            # 计算每个头的熵
            eps = 1e-10  # 防止log(0)
            head_entropies = -torch.sum(attn_probs * torch.log2(attn_probs + eps), dim=1)  # [num_heads]
            
            # 取所有头的平均熵作为该层的熵值
            entropy = head_entropies.mean().item()
            entropy_by_layer[layer_idx].append(entropy)
    
    return entropy_by_layer

def calculate_attention_change_rate(current_maps, previous_maps):
    """
    计算连续token间注意力权重的L2范数差异
    
    Args:
        current_maps: 当前step的注意力权重列表
        previous_maps: 上一step的注意力权重列表
    
    Returns:
        change_rate_by_layer: 每层的注意力变化率，字典格式，键为层索引，值为变化率
    """
    if previous_maps is None:
        # 第一个step没有前一个step的数据
        return None
    
    change_rate_by_layer = {}
    
    # 遍历每层的注意力图
    for layer_idx, (curr_attn, prev_attn) in enumerate(zip(current_maps, previous_maps)):
        # 确定是prefill阶段还是生成阶段
        is_curr_prefill = curr_attn.shape[1] > 1
        is_prev_prefill = prev_attn.shape[1] > 1
        
        if is_curr_prefill and is_prev_prefill:
            # 两个都是prefill阶段，比较最后一个token的注意力
            curr_last_token = curr_attn[:, -1, :]  # [num_heads, seq_len]
            prev_last_token = prev_attn[:, -1, :]  # [num_heads, seq_len]
            
            # 计算L2范数差异
            # 首先确保两个tensor形状相同
            min_len = min(curr_last_token.shape[-1], prev_last_token.shape[-1])
            curr_last_token = curr_last_token[:, :min_len]
            prev_last_token = prev_last_token[:, :min_len]
            
            # 计算L2范数差异
            l2_diff = torch.norm(curr_last_token - prev_last_token, dim=1)  # [num_heads]
            
            # 取所有头的平均
            change_rate = l2_diff.mean().item()
            change_rate_by_layer[layer_idx] = change_rate
        elif not is_curr_prefill and not is_prev_prefill:
            # 两个都是生成阶段，比较当前token的注意力
            curr_token = curr_attn[:, 0, :]  # [num_heads, seq_len]
            prev_token = prev_attn[:, 0, :]  # [num_heads, seq_len]
            
            # 计算L2范数差异
            # 首先确保两个tensor形状相同
            min_len = min(curr_token.shape[-1], prev_token.shape[-1])
            curr_token = curr_token[:, :min_len]
            prev_token = prev_token[:, :min_len]
            
            # 计算L2范数差异
            l2_diff = torch.norm(curr_token - prev_token, dim=1)  # [num_heads]
            
            # 取所有头的平均
            change_rate = l2_diff.mean().item()
            change_rate_by_layer[layer_idx] = change_rate
        else:
            # 一个是prefill阶段，一个是生成阶段，不进行比较
            change_rate_by_layer[layer_idx] = 0.0
    
    return change_rate_by_layer

def process_and_plot_attention_metrics(attention_outputs, input_seq_len, save_dir="./pic"):
    """
    处理注意力输出并绘制指标图表
    
    Args:
        attention_outputs: 从模型中获取的注意力输出列表
        input_seq_len: 输入序列长度
        save_dir: 保存图表的目录
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 从attention_outputs中提取attention_maps
    token_indices = []
    attention_maps_by_token = []
    
    for output in attention_outputs:
        token_idx = output['token_idx']
        token_indices.append(token_idx)
        attention_maps_by_token.append(output['attention_maps'])
    
    # 按步骤计算指标
    input_ratio_data = {}
    entropy_data = {}
    change_rate_data = {}
    
    # 按token处理注意力数据
    prev_attention_maps = None
    for i, (token_idx, attention_maps) in enumerate(zip(token_indices, attention_maps_by_token)):
        # 计算当前token的输入注意力比例
        input_ratio = calculate_input_attention_ratio(attention_maps, input_seq_len)
        for layer_idx, ratios in input_ratio.items():
            if layer_idx not in input_ratio_data:
                input_ratio_data[layer_idx] = {'token_indices': [], 'ratios': []}
            input_ratio_data[layer_idx]['token_indices'].append(token_idx)
            input_ratio_data[layer_idx]['ratios'].append(ratios[0])  # 取第一个值，因为每个token只有一个比例值
        
        # 计算当前token的注意力熵
        entropy = calculate_attention_entropy(attention_maps)
        for layer_idx, entropies in entropy.items():
            if layer_idx not in entropy_data:
                entropy_data[layer_idx] = {'token_indices': [], 'entropies': []}
            entropy_data[layer_idx]['token_indices'].append(token_idx)
            entropy_data[layer_idx]['entropies'].append(entropies[0])  # 取第一个值，因为每个token只有一个熵值
        # 计算当前token的注意力变化率
        if prev_attention_maps is not None:
            change_rate = calculate_attention_change_rate(attention_maps, prev_attention_maps)
            if change_rate is not None:
                for layer_idx, rate in change_rate.items():
                    if layer_idx not in change_rate_data:
                        change_rate_data[layer_idx] = {'token_indices': [], 'rates': []}
                    change_rate_data[layer_idx]['token_indices'].append(token_idx)
                    change_rate_data[layer_idx]['rates'].append(rate)
        
        prev_attention_maps = attention_maps
    
    # 绘制输入注意力比例图表
    for layer_idx, data in input_ratio_data.items():
        plt.figure(figsize=(10, 6))
        plt.plot(data['token_indices'], data['ratios'], marker='o', linestyle='-')
        plt.xlabel('Token Index')
        plt.ylabel('Input Attention Ratio')
        plt.title(f'Layer {layer_idx} - Input Attention Ratio')
        plt.grid(True)
        plt.savefig(os.path.join(save_dir, f'input_attention_ratio_layer_{layer_idx}.png'))
        plt.close()
    
    # 绘制注意力熵图表
    for layer_idx, data in entropy_data.items():
        plt.figure(figsize=(10, 6))
        plt.plot(data['token_indices'], data['entropies'], marker='o', linestyle='-')
        plt.xlabel('Token Index')
        plt.ylabel('Attention Entropy')
        plt.title(f'Layer {layer_idx} - Attention Entropy')
        plt.grid(True)
        plt.savefig(os.path.join(save_dir, f'attention_entropy_layer_{layer_idx}.png'))
        plt.close()
    
    # 绘制注意力变化率图表
    for layer_idx, data in change_rate_data.items():
        plt.figure(figsize=(10, 6))
        plt.plot(data['token_indices'], data['rates'], marker='o', linestyle='-')
        plt.xlabel('Token Index')
        plt.ylabel('Attention Change Rate')
        plt.title(f'Layer {layer_idx} - Attention Change Rate')
        plt.grid(True)
        plt.savefig(os.path.join(save_dir, f'attention_change_rate_layer_{layer_idx}.png'))
        plt.close()
    
    print(f"所有指标图表已保存到 {save_dir} 目录")

def plot_logits_metrics(tracker_data, save_dir="./logits_pic"):
    """
    绘制模型生成过程中的logits相关指标，并保存到指定目录
    
    Args:
        tracker_data: TokenProbabilityTracker收集的数据，包含每一步的logits指标
        save_dir: 保存图表的目录
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 提取token位置索引
    token_positions = [i + 1 for i in range(len(tracker_data))]
    
    # 提取各指标数据
    eos_probabilities = [step_data['eos_probability'] for step_data in tracker_data]
    eos_ranks = [step_data['eos_rank'] for step_data in tracker_data]
    entropies = [step_data['entropy'] for step_data in tracker_data]
    mean_logits = [step_data['mean_logit'] for step_data in tracker_data]
    topk_entropies = [step_data['topk_entropy'] for step_data in tracker_data]
    cosine_similarities = [step_data['cosine_similarity'] for step_data in tracker_data]
    kurtosis_values = [step_data['kurtosis'] for step_data in tracker_data]
    skewness_values = [step_data['skewness'] for step_data in tracker_data]
    mean_deviations = [step_data['mean_deviation'] for step_data in tracker_data]
    max_deviations = [step_data['max_deviation'] for step_data in tracker_data]
    historical_mean_deviations = [step_data['historical_mean_deviation'] for step_data in tracker_data]
    historical_max_deviations = [step_data['historical_max_deviation'] for step_data in tracker_data]
    
    # 1. 绘制EOS概率图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, eos_probabilities, marker='o', linestyle='-', color='green')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Probability')
    plt.title('EOS Token Probability Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'eos_probability.png'))
    plt.close()
    
    # 2. 绘制EOS排名图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, eos_ranks, marker='o', linestyle='-', color='blue')
    plt.xlabel('Token Position')
    plt.ylabel('EOS Token Rank')
    plt.title('EOS Token Rank Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'eos_rank.png'))
    plt.close()
    
    # 3. 绘制熵图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, entropies, marker='o', linestyle='-', color='red')
    plt.xlabel('Token Position')
    plt.ylabel('Entropy')
    plt.title('Token Distribution Entropy Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'entropy.png'))
    plt.close()
    
    # 4. 绘制平均logits图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, mean_logits, marker='o', linestyle='-', color='purple')
    plt.xlabel('Token Position')
    plt.ylabel('Mean Logit Value')
    plt.title('Mean Logit Value Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'mean_logits.png'))
    plt.close()
    
    # 5. 绘制Top-K熵图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, topk_entropies, marker='o', linestyle='-', color='orange')
    plt.xlabel('Token Position')
    plt.ylabel('Top-K Entropy')
    plt.title('Top-K Token Entropy Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'topk_entropy.png'))
    plt.close()
    
    # 6. 绘制余弦相似度图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, cosine_similarities, marker='o', linestyle='-', color='brown')
    plt.xlabel('Token Position')
    plt.ylabel('Cosine Similarity with EOS')
    plt.title('Cosine Similarity with EOS Token Throughout Generation')
    plt.grid(True)
    plt.savefig(os.path.join(save_dir, 'cosine_similarity.png'))
    plt.close()
    
    # 7. 绘制峰度和偏度图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, kurtosis_values, marker='o', linestyle='-', color='green', label='Kurtosis')
    plt.plot(token_positions, skewness_values, marker='o', linestyle='-', color='blue', label='Skewness')
    plt.xlabel('Token Position')
    plt.ylabel('Value')
    plt.title('Kurtosis and Skewness Throughout Generation')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'kurtosis_skewness.png'))
    plt.close()
    
    # 8. 绘制偏差图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, mean_deviations, marker='o', linestyle='-', color='green', label='Mean Deviation')
    plt.plot(token_positions, max_deviations, marker='o', linestyle='-', color='red', label='Max Deviation')
    plt.xlabel('Token Position')
    plt.ylabel('Deviation')
    plt.title('Deviation from Mean Logit Throughout Generation')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'deviation.png'))
    plt.close()
    
    # 9. 绘制历史偏差图
    plt.figure(figsize=(10, 6))
    plt.plot(token_positions, historical_mean_deviations, marker='o', linestyle='-', color='green', label='Mean Historical Deviation')
    plt.plot(token_positions, historical_max_deviations, marker='o', linestyle='-', color='red', label='Max Historical Deviation')
    plt.xlabel('Token Position')
    plt.ylabel('Historical Deviation')
    plt.title('Deviation from Historical Average Throughout Generation')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'historical_deviation.png'))
    plt.close()
    
    # 10. 绘制百分比熵图
    if 'percentile_entropies' in tracker_data[0]:
        percentiles = tracker_data[0]['percentile_entropies'].keys()
        percentile_data = {}
        for percentile in percentiles:
            percentile_data[percentile] = [step_data['percentile_entropies'][percentile] for step_data in tracker_data]
        
        plt.figure(figsize=(10, 6))
        for percentile, values in percentile_data.items():
            plt.plot(token_positions, values, marker='o', linestyle='-', label=f'Percentile {percentile}')
        
        plt.xlabel('Token Position')
        plt.ylabel('Percentile Entropy')
        plt.title('Percentile Entropy Throughout Generation')
        plt.grid(True)
        plt.legend()
        plt.savefig(os.path.join(save_dir, 'percentile_entropy.png'))
        plt.close()
    
    # 11. 绘制所有指标的组合图 (标准化到0-1范围)
    plt.figure(figsize=(12, 8))
    
    # 标准化函数
    def normalize(values):
        min_val = min(values)
        max_val = max(values)
        if max_val > min_val:
            return [(v - min_val) / (max_val - min_val) for v in values]
        else:
            return [0.5 for _ in values]
    
    # 绘制标准化的指标
    plt.plot(token_positions, normalize(eos_probabilities), marker='o', linestyle='-', label='EOS Probability')
    plt.plot(token_positions, normalize(entropies), marker='o', linestyle='-', label='Entropy')
    plt.plot(token_positions, normalize(mean_logits), marker='o', linestyle='-', label='Mean Logit')
    plt.plot(token_positions, normalize(cosine_similarities), marker='o', linestyle='-', label='Cosine Similarity')
    
    plt.xlabel('Token Position')
    plt.ylabel('Normalized Value (0-1)')
    plt.title('Combined Logits Metrics (Normalized)')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'combined_metrics.png'))
    plt.close()
    
    print(f"所有 logits 指标图表已保存到 {save_dir} 目录")
