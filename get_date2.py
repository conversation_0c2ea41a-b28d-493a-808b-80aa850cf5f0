import torch
import numpy as np
from typing import List
import pickle
from transformers import AutoTokenizer, AutoModel, LogitsProcessorList, LogitsProcessor
from transformers.generation import TopPLogitsWarper, TemperatureLogitsWarper
from eos_plot import plot_logits_metrics, process_and_plot_attention_metrics
import time
import sys
import os
local_path = "/workspace/chatglm-6b"
sys.path.insert(0, local_path)
# from modeling_chatglm import ChatGLMForConditionalGeneration
# from tokenization_chatglm import ChatGLMTokenizer

# 配置超参数
CONFIG = {
    'top_k': 100,  # 跟踪的top tokens数量
    'top_k_for_entropy': 5,  # 计算熵时使用的top-k数量
    'top_k_for_deviation': 50,  # 计算与全部token偏差时使用的top-k数量
    'top_k_for_historical_deviation': 20,  # 计算与历史平均值偏差时使用的top-k数量
    'percentiles': [0.1, 0.2, 0.01, 0.001],  # 计算熵的不同比例
    'temperature': 0.1,  # 温度参数
    'top_p': 0.1,  # top-p采样参数
}

def calculate_kurtosis(x: torch.Tensor) -> torch.Tensor:
    """计算峰度
    
    Args:
        x: 输入张量
        
    Returns:
        峰度值
    """
    mean = torch.mean(x)
    std = torch.std(x)
    if std == 0:
        return torch.tensor(0.0, device=x.device)
    z = (x - mean) / std
    return torch.mean(z ** 4) - 3.0

def calculate_skewness(x: torch.Tensor) -> torch.Tensor:
    """计算偏度
    
    Args:
        x: 输入张量
        
    Returns:
        偏度值
    """
    mean = torch.mean(x)
    std = torch.std(x)
    if std == 0:
        return torch.tensor(0.0, device=x.device)
    z = (x - mean) / std
    return torch.mean(z ** 3)

class TokenProbabilityTracker(LogitsProcessor):
    def __init__(self, eos_token_id, top_k=100):
        """
        初始化跟踪器来获取EOS token的概率和top-k tokens的logits
        
        Args:
            eos_token_id: 终止符的token ID
            top_k: 要跟踪的top tokens数量
        """
        self.eos_token_id = eos_token_id
        self.top_k = top_k
        self.tracked_steps_probs = []
        self.tracked_steps_logits = []
        self.tokens_generated = 0
        self.historical_topk_logits = []  # 存储历史top-k logits值
        
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor) -> torch.FloatTensor:
        """
        在每个解码步骤中获取所需的概率信息
        """
        # 增加生成的token计数
        self.tokens_generated += 1
        
        # 输出当前步骤信息
        token_id = input_ids[0, -1].item() if input_ids.shape[1] > 1 else None
        print(f"处理第 {self.tokens_generated} 个token，token_id = {token_id}")
        
        # 获取终止符的概率
        eos_logit = scores[0, self.eos_token_id].item()
        
        # 获取top-k tokens及其logits
        top_k_logits, top_k_indices_logits = torch.topk(scores[0], self.top_k)
        
        # 计算终止符的排名
        all_ranks = torch.argsort(scores[0], descending=True)
        eos_rank = torch.where(all_ranks == self.eos_token_id)[0][0].item() + 1
        
        # 计算候选token的熵  scores = batch_size*dims
        probs = torch.softmax(scores[0], dim=0)
        entropy = -torch.sum(probs * torch.log2(probs)).item()
        
        # 计算所有token的logits的平均值
        mean_logit = torch.mean(scores[0]).item()
        
        # 计算Top-k token的熵（使用独立的top_k参数）
        top_k_for_entropy_logits, _ = torch.topk(scores[0], CONFIG['top_k_for_entropy'])
        topk_probs = torch.softmax(top_k_for_entropy_logits, dim=0)
        topk_entropy = -torch.sum(topk_probs * torch.log2(topk_probs)).item()
        
        # 计算当前step的top-k logits值与全部token logits平均值的偏差
        top_k_for_deviation_logits, _ = torch.topk(scores[0], CONFIG['top_k_for_deviation'])
        all_logits_mean = torch.mean(scores[0]).item()
        deviation = torch.abs(top_k_for_deviation_logits - all_logits_mean)
        mean_deviation = torch.mean(deviation).item()
        max_deviation = torch.max(deviation).item()
        
        # 计算当前step的top-k logits值与历史平均值的偏差
        top_k_for_historical_logits, _ = torch.topk(scores[0], CONFIG['top_k_for_historical_deviation'])
        current_topk_logits = top_k_for_historical_logits.tolist()
        self.historical_topk_logits.append(current_topk_logits)
        
        historical_mean_deviation = 0.0
        historical_max_deviation = 0.0
        
        if len(self.historical_topk_logits) > 1:
            historical_avg = np.mean(self.historical_topk_logits[:-1], axis=0)
            current_probs = np.array(current_topk_logits)
            historical_deviation = np.abs(current_probs - historical_avg)
            historical_mean_deviation = np.mean(historical_deviation)
            historical_max_deviation = np.max(historical_deviation)
        
        # 计算每个step模型最终输出的logits与EOS终止符嵌入向量的余弦相似度
        eos_embedding = scores[0, self.eos_token_id].unsqueeze(0)  # 将标量转换为1维张量
        cosine_similarity = torch.cosine_similarity(scores[0].unsqueeze(0), eos_embedding).item()
        
        # 计算所有logits的峰度和偏度
        kurtosis = calculate_kurtosis(scores[0]).item()
        skewness = calculate_skewness(scores[0]).item()
        
        # 计算不同比例logits的熵
        vocab_size = scores[0].shape[0]
        percentiles = CONFIG['percentiles']
        percentile_entropies = {}
        for percentile in percentiles:
            k = int(vocab_size * percentile)
            top_k_logits, _ = torch.topk(scores[0], k)
            probs = torch.softmax(top_k_logits, dim=0)
            entropy = -torch.sum(probs * torch.log2(probs)).item()
            percentile_entropies[f'{percentile:.1f}'] = entropy
        
        step_data_logits = {
            'eos_probability': eos_logit,
            'top_tokens': {idx.item(): logit.item() for idx, logit in zip(top_k_indices_logits, top_k_logits)},
            'token_id': token_id,
            'eos_rank': eos_rank,
            'entropy': entropy,
            'mean_logit': mean_logit,
            'topk_entropy': topk_entropy,
            'cosine_similarity': cosine_similarity,
            'kurtosis': kurtosis,
            'skewness': skewness,
            'percentile_entropies': percentile_entropies,
            'mean_deviation': mean_deviation,
            'max_deviation': max_deviation,
            'historical_mean_deviation': historical_mean_deviation,
            'historical_max_deviation': historical_max_deviation
        }
        self.tracked_steps_logits.append(step_data_logits)
        
        # 不修改原始logits
        return scores

class LogitsRecorder:
    def __init__(self, model, tokenizer):
        """初始化记录器，用于记录模型logits输出和attention输出
        
        Args:
            model: 模型实例
            tokenizer: 分词器
        """
        self.model = model
        self.tokenizer = tokenizer
        self.logits_outputs = []      # 存储logits输出
        self.attention_outputs = []   # 存储attention输出
        self.hooks = []
        self.current_token_idx = 0
        self.is_prefill = True
        self.eos_token_id = tokenizer.eos_token_id
    
    def register_hooks(self):
        # 注册transformer钩子，获取attention输出
        if hasattr(self.model, 'transformer'):
            self.hooks.append(self.model.transformer.register_forward_hook(
                lambda m, i, o: self._save_transformer_output(m, i, o)))
            print("成功注册transformer钩子，用于获取attention输出")
        else:
            print("警告：未找到model.transformer，无法注册钩子")
    

    def _save_transformer_output(self, module, input_tensor, output):
        """保存transformer输出中的attention数据
        
        Args:
            module: transformer模块
            input_tensor: 输入
            output: 输出 (BaseModelOutputWithPast类型)
        """
        # 检查输出是否包含attentions属性
        if hasattr(output, 'attentions') and output.attentions is not None:
            # 获取attentions数据
            attentions = output.attentions
            
            # 打印形状信息进行调试
            print(f"获取到transformer的attention输出，层数: {len(attentions)}")
            for i, attn in enumerate(attentions):
                print(f"第{i}层attention形状: {attn.shape}")
            
            # 将attention转移到CPU并存储
            attention_maps = []
            for attn in attentions:
                # attention形状通常为 [batch_size, num_heads, seq_len, seq_len]
                attention_np = attn.detach().cpu()
                attention_maps.append(attention_np)
            
            # 存储本次attention数据
            attention_data = {
                'token_idx': self.current_token_idx,
                'attention_maps': attention_maps,
                'timestamp': time.time()
            }
            
            self.attention_outputs.append(attention_data)
            print(f"成功保存transformer的attention输出，当前token索引: {self.current_token_idx}")
        else:
            print("警告: transformer输出不包含attentions属性或attentions为None")
            
    
    def set_decode_mode(self):
        """切换到decode模式"""
        self.is_prefill = False
    
    def set_prefill_mode(self):
        """切换到prefill模式"""
        self.is_prefill = True
    
    def set_current_token_idx(self, idx):
        """设置当前token的索引"""
        self.current_token_idx = idx
    
    def remove_hooks(self):
        """移除所有钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
    
    def save_data(self, filename="model_outputs.pkl"):
        """保存所有输出到文件"""
        with open(filename, 'wb') as f:
            pickle.dump({
                'attention_outputs': self.attention_outputs
            }, f)
        print(f"\nLogits和Attention数据已保存到'{filename}'")

class DecodingTracker(LogitsProcessor):
    def __init__(self, logits_recorder):
        """初始化解码跟踪器
        
        Args:
            logits_recorder: Logits记录器实例
        """
        self.logits_recorder = logits_recorder
        self.generated_tokens = []
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor) -> torch.FloatTensor:
        """记录每次生成的token
        
        Args:
            input_ids: 当前已生成的token ID列表
            scores: 模型输出的logits
        """
        # 将模式设置为decode
        self.logits_recorder.set_decode_mode()
        
        # 设置当前token的索引
        current_token_idx = len(self.generated_tokens)
        self.logits_recorder.set_current_token_idx(current_token_idx)
        
        # 记录当前生成的token
        token_id = input_ids[0, -1].item() if input_ids.shape[1] > 1 else None
        self.generated_tokens.append(token_id)
        
        return scores

def single_prompt_analysis(prompt, model=None, tokenizer=None):
    """
    分析单个prompt并返回结果，可以被外部模块调用
    
    Args:
        prompt: 输入提示文本
        model: 预训练模型，如果为None则重新加载
        tokenizer: 分词器，如果为None则重新加载
        
    Returns:
        包含分析结果的字典
    """
    if model is None or tokenizer is None:
        # 加载模型和分词器
        if tokenizer is None:
            tokenizer = AutoTokenizer.from_pretrained(local_path, trust_remote_code=True)
        if model is None:
            model = AutoModel.from_pretrained(local_path, trust_remote_code=True)
            model = model.half().cuda()
            model.eval()

    # 获取终止符token ID
    eos_token_id = tokenizer.eos_token_id
    
    # 初始化记录器
    logits_recorder = LogitsRecorder(model, tokenizer)
    logits_recorder.register_hooks()
    
    # 创建解码跟踪器
    decoding_tracker = DecodingTracker(logits_recorder)
    
    # 创建token概率跟踪器
    token_tracker = TokenProbabilityTracker(eos_token_id, top_k=CONFIG['top_k'])
    
    # 准备logits处理器列表
    logits_processors = LogitsProcessorList([
        token_tracker,
        decoding_tracker,
        TemperatureLogitsWarper(temperature=CONFIG['temperature']),
        TopPLogitsWarper(top_p=CONFIG['top_p'])
    ])
    
    # 计算prompt的token长度
    prompt_tokens = tokenizer.encode(prompt, add_special_tokens=True)
    prompt_length = len(prompt_tokens)
    print(f"Prompt长度: {prompt_length}")
    
    # 设置logits_recorder为prefill模式
    logits_recorder.set_prefill_mode()
    
    # 初始化序列分析器
    from sequence_analyzer import SequenceAnalyzer
    sequence_analyzer = SequenceAnalyzer(prompt_tokens, tokenizer)
    
    # 使用模型的generate方法生成文本
    print("开始生成文本...")
    
    # 将输入转换为张量
    input_ids = tokenizer.encode(prompt, return_tensors="pt").cuda()
    generation_kwargs = {
        "input_ids": input_ids,
        "max_length": 4096,
        "do_sample": False,
        "logits_processor": logits_processors,
        "return_dict_in_generate": True,  
        "output_scores": True,
        "output_attentions": True,  
        "output_hidden_states": True  
    }
    
    # 生成文本
    generation_output = model.generate(**generation_kwargs)

    # 提取生成的token
    generated_token_ids = generation_output.sequences[0, prompt_length:].tolist()
    
    # 更新序列分析器
    sequence_analyzer.update_batch(generated_token_ids)
    
    # 生成的文本
    generated_text = tokenizer.decode(generated_token_ids)
    print(f"生成的文本: {generated_text[:100]}...")
    print(f"生成的token数量: {len(generated_token_ids)}")
 
    # 移除钩子
    logits_recorder.remove_hooks()
    
    # 创建保存图表的目录
    os.makedirs("./pic", exist_ok=True)
    os.makedirs("./sequence_metrics", exist_ok=True)
    os.makedirs("./logits_pic", exist_ok=True)
    
    # 处理注意力数据并生成图表
    process_and_plot_attention_metrics(
        attention_outputs=logits_recorder.attention_outputs,
        input_seq_len=prompt_length,
        save_dir="./pic"
    )
    
    # 绘制序列分析指标
    sequence_analyzer.plot_metrics(save_dir="./sequence_metrics")
    
    # 绘制logits相关指标
    plot_logits_metrics(token_tracker.tracked_steps_logits, save_dir="./logits_pic")
    
    return {
        'prompt': prompt,
        'generated_text': generated_text,
        'prompt_length': prompt_length,
        'generated_tokens': generated_token_ids,
        'attention_outputs': logits_recorder.attention_outputs,
        'logits_outputs': token_tracker.tracked_steps_logits
    }

def main():

    tokenizer = AutoTokenizer.from_pretrained(local_path, trust_remote_code=True)
    model = AutoModel.from_pretrained(local_path, trust_remote_code=True)
    model = model.half().cuda()
    model.eval()


    # 获取终止符token ID和句号token ID
    eos_token_id = tokenizer.eos_token_id
    period_token_id = tokenizer.encode(".", add_special_tokens=False)[0]
    print(f"EOS token ID: {eos_token_id}")
    print(f"Period token ID: {period_token_id}")
    
    # 初始化记录器
    logits_recorder = LogitsRecorder(model, tokenizer)
    logits_recorder.register_hooks()
    
    # 创建解码跟踪器
    decoding_tracker = DecodingTracker(logits_recorder)
    
    # 创建token概率跟踪器
    token_tracker = TokenProbabilityTracker(eos_token_id, top_k=CONFIG['top_k'])
    
    # 设置生成参数
    prompt = "Hello, can you tell me when the USA founded？"
    
    # 准备logits处理器列表
    logits_processors = LogitsProcessorList([
        token_tracker,
        decoding_tracker,
        TemperatureLogitsWarper(temperature=CONFIG['temperature']),
        TopPLogitsWarper(top_p=CONFIG['top_p'])
    ])
    
    # 计算prompt的token长度
    prompt_tokens = tokenizer.encode(prompt, add_special_tokens=True)
    prompt_length = len(prompt_tokens)
    print(f"Prompt的长度: {prompt_length}")
    
    # 设置logits_recorder为prefill模式（默认已经是）
    logits_recorder.set_prefill_mode()
    
    # 初始化序列分析器
    from sequence_analyzer import SequenceAnalyzer
    sequence_analyzer = SequenceAnalyzer(prompt_tokens, tokenizer)
    
    # 使用模型的generate方法生成文本
    print("开始生成文本...")
    
    # 将输入也转换为半精度
    input_ids = tokenizer.encode(prompt, return_tensors="pt").cuda()
    generation_kwargs = {
        "input_ids": input_ids,
        "max_length": 4096,
        "do_sample": False,
        "logits_processor": logits_processors,
        "return_dict_in_generate": True,  
        "output_scores": True,
        "output_attentions": True,  
        "output_hidden_states": True  
    }
    

    generation_output = model.generate(**generation_kwargs)

    # # 如果是对象且有sequences属性，说明return_dict_in_generate=True生效了
    print("返回了字典类型的结果，包含sequences属性")
    generated_token_ids = generation_output.sequences[0, prompt_length:].tolist()
    
    # 更新序列分析器
    sequence_analyzer.update_batch(generated_token_ids)
    
    prompt_token_ids = tokenizer.encode(prompt, add_special_tokens=True)
    all_token_ids = prompt_token_ids + generated_token_ids
    generated_text = tokenizer.decode(generated_token_ids)
    print(f"Generated text: {generated_text}")
    print(f"Generated tokens count: {len(generated_token_ids)}")
 
    # 移除钩子
    logits_recorder.remove_hooks()
    
    # 创建保存图表的目录
    os.makedirs("./pic", exist_ok=True)
    os.makedirs("./sequence_metrics", exist_ok=True)
    os.makedirs("./logits_pic", exist_ok=True)
    
    # 绘制logits相关指标
    print("绘制logits相关指标...")
    plot_logits_metrics(token_tracker.tracked_steps_logits, save_dir="./logits_pic")
    
    # 计算并绘制注意力相关指标
    print("计算并绘制注意力指标...")
    # 处理注意力数据并生成图表
    process_and_plot_attention_metrics(
        attention_outputs=logits_recorder.attention_outputs,
        input_seq_len=prompt_length,
        save_dir="./pic"
    )
    
    # 绘制序列分析指标
    print("绘制序列分析指标...")
    sequence_analyzer.plot_metrics(save_dir="./sequence_metrics")
    
    print("所有指标分析和可视化完成！")
    
    import pdb; pdb.set_trace()
if __name__ == "__main__":
    main()