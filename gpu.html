<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>具身智能大模型训练服务器采购项目</title>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2980b9;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #34495e;
            --success: #2ecc71;
            --warning: #f39c12;
            --gradient: linear-gradient(135deg, var(--primary), var(--secondary));
        }
        
        body, html {
            margin: 0;
            padding: 0;
            font-family: "Segoe UI", "Microsoft YaHei", Arial, sans-serif;
            height: 100%;
            background-color: #f0f2f5;
            color: var(--dark);
        }
        
        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .slides {
            width: 90%;
            max-width: 1000px;
            height: 80%;
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
            background-color: white;
            padding: 2rem;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .slide.active {
            opacity: 1;
            z-index: 1;
        }
        
        .slide-header {
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 1rem;
        }
        
        .slide-title {
            font-size: 2.2rem;
            font-weight: 600;
            color: var(--dark);
            margin: 0;
            padding-bottom: 0.5rem;
            display: inline-block;
            position: relative;
        }
        
        .slide-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 4px;
            background: var(--gradient);
            border-radius: 2px;
        }
        
        .slide-content {
            flex: 1;
            padding: 1rem 0;
        }
        
        .slide-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .slide-number {
            font-size: 0.9rem;
            color: rgba(0,0,0,0.5);
        }
        
        .controls {
            display: flex;
            gap: 1rem;
        }
        
        .control-btn {
            background: var(--gradient);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.2s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        /* Content Styling */
        ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }
        
        li::before {
            content: '•';
            color: var(--primary);
            font-size: 1.2rem;
            position: absolute;
            left: 0;
        }
        
        .highlight {
            color: var(--accent);
            font-weight: 600;
        }
        
        .two-column {
            display: flex;
            gap: 2rem;
        }
        
        .column {
            flex: 1;
        }
        
        .info-box {
            background-color: rgba(52, 152, 219, 0.1);
            border-left: 4px solid var(--primary);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .warning-box {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 4px solid var(--accent);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .success-box {
            background-color: rgba(46, 204, 113, 0.1);
            border-left: 4px solid var(--success);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .tech-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 4px;
            background-color: rgba(0,0,0,0.02);
        }
        
        .tech-item-label {
            font-weight: 600;
            color: var(--dark);
            width: 120px;
        }
        
        .tech-item-value {
            flex: 1;
        }
        
        .progress-container {
            margin: 1rem 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .progress-bar {
            height: 12px;
            background-color: rgba(0,0,0,0.05);
            border-radius: 6px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--gradient);
            width: 35%;
            border-radius: 6px;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: var(--primary);
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-right: 0.5rem;
        }
        
        .badge-success {
            background-color: var(--success);
        }
        
        .badge-warning {
            background-color: var(--warning);
        }
        
        .badge-danger {
            background-color: var(--accent);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .slide-title {
                font-size: 1.8rem;
            }
            
            .two-column {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="slides">
            <!-- Slide 1: 基本信息 -->
            <div class="slide active" id="slide-1">
                <div class="slide-header">
                    <h1 class="slide-title">采购项目基本信息</h1>
                </div>
                <div class="slide-content">
                    <div class="info-box">
                        <h3>具身智能大模型训练服务器</h3>
                        <p>预算金额：<span class="highlight">130万元</span></p>
                        <p>采购方式：<span class="badge">公开招标</span></p>
                    </div>
                    
                    <div class="two-column" style="margin-top: 2rem;">
                        <div class="column">
                            <h3>项目负责人</h3>
                            <ul>
                                <li>姓名：<span class="highlight">（待填写）</span></li>
                                <li>电话：（待填写）</li>
                                <li>邮箱：（待填写）</li>
                            </ul>
                        </div>
                        <div class="column">
                            <h3>项目经办人</h3>
                            <ul>
                                <li>姓名：<span class="highlight">（待填写）</span></li>
                                <li>电话：（待填写）</li>
                                <li>邮箱：（待填写）</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">1 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 2: 项目背景 -->
            <div class="slide" id="slide-2">
                <div class="slide-header">
                    <h1 class="slide-title">采购项目背景</h1>
                </div>
                <div class="slide-content">
                    <div class="two-column">
                        <div class="column">
                            <h3>学科发展需求</h3>
                            <ul>
                                <li><span class="highlight">具身智能（Embodied AI）</span>：人工智能前沿领域</li>
                                <li>多模态数据处理、实时建模、复杂任务规划</li>
                                <li>需高性能算力支持</li>
                            </ul>
                            
                            <div class="warning-box">
                                <h4>科研任务紧迫性</h4>
                                <ul style="margin: 0">
                                    <li>国家自然科学基金、科技部课题</li>
                                    <li>目标：2026年前完成基础模型、机器人算法、多智能体平台</li>
                                </ul>
                            </div>
                        </div>
                        <div class="column">
                            <h3>显存与算力瓶颈</h3>
                            <p>现有设备：4台RTX 3090（24GB显存）</p>
                            
                            <div class="progress-container">
                                <div class="progress-label">
                                    <span>当前算力满足度</span>
                                    <span>35%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                            </div>
                            
                            <div class="info-box">
                                <p><span class="highlight">训练周期延长</span>：相比理想设备，延长约3-5倍</p>
                                <p><span class="highlight">研究进度滞后</span>：无法及时产出科研成果</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">2 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 3: 项目必要性 -->
            <div class="slide" id="slide-3">
                <div class="slide-header">
                    <h1 class="slide-title">采购项目必要性</h1>
                </div>
                <div class="slide-content">
                    <div class="two-column">
                        <div class="column">
                            <div class="info-box">
                                <h3>技术先进性</h3>
                                <ul style="margin: 0">
                                    <li><span class="highlight">A100 80GB GPU</span>：显存80GB，算力312 TFLOPS</li>
                                    <li><span class="highlight">NVLink互联</span>：带宽600GB/s，支持分布式训练</li>
                                </ul>
                            </div>
                            
                            <h3>研究不可替代性</h3>
                            <ul>
                                <li>实时交互需求，云平台延迟和安全问题</li>
                                <li>数据本地化存储需求</li>
                            </ul>
                            
                            <div class="success-box">
                                <h4>经济性</h4>
                                <p>年均折旧56万元，低于租赁成本</p>
                            </div>
                        </div>
                        <div class="column">
                            <h3>产学研协同</h3>
                            <ul>
                                <li>与苏州工业园区企业合作开发巡检机器人</li>
                                <li>促进技术应用转化</li>
                            </ul>
                            
                            <h3>预期科研成果</h3>
                            <ul>
                                <li><span class="badge badge-success">论文</span> 10-15篇顶会论文</li>
                                <li><span class="badge badge-success">专利</span> 10-15项专利</li>
                                <li><span class="badge badge-success">转化</span> 2-3项成果转化</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">3 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 4: 技术要求 -->
            <div class="slide" id="slide-4">
                <div class="slide-header">
                    <h1 class="slide-title">采购项目技术要求</h1>
                </div>
                <div class="slide-content">
                    <h3>核心配置</h3>
                    <div class="tech-item">
                        <div class="tech-item-label">GPU</div>
                        <div class="tech-item-value">8块80GB显存，TF32≥155TFLOP</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-item-label">CPU</div>
                        <div class="tech-item-value">2×Intel Xeon Gold 6348（28核）</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-item-label">内存</div>
                        <div class="tech-item-value">8×64GB DDR4 3200</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-item-label">存储</div>
                        <div class="tech-item-value">4×15.36TB NVMe SSD（RAID 10）</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-item-label">网络</div>
                        <div class="tech-item-value">2×200G InfiniBand HDR</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-item-label">电源</div>
                        <div class="tech-item-value">4×4000W钛金冗余电源</div>
                    </div>
                    
                    <h3>软件与兼容性</h3>
                    <div class="info-box">
                        <ul style="margin: 0">
                            <li>预装Ubuntu 22.04，NVIDIA驱动≥525.85.07，CUDA 12.2</li>
                            <li>支持PyTorch 2.3、TensorFlow 2.15、ROS 2</li>
                            <li>兼容Kubernetes、Slurm</li>
                        </ul>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">4 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 5: 商务要求 -->
            <div class="slide" id="slide-5">
                <div class="slide-header">
                    <h1 class="slide-title">采购项目商务要求</h1>
                </div>
                <div class="slide-content">
                    <div class="two-column">
                        <div class="column">
                            <div class="info-box">
                                <h3>交付信息</h3>
                                <p><span class="highlight">交货期限</span>：合同签订后45天</p>
                                <p><span class="highlight">交付地点</span>：苏州高等研究院至德楼3楼机房</p>
                            </div>
                            
                            <h3>付款方式</h3>
                            <ul>
                                <li><span class="badge">50%</span> 预付</li>
                                <li><span class="badge">45%</span> 验收后</li>
                                <li><span class="badge">5%</span> 质保期满</li>
                            </ul>
                            
                            <h3>包装与运输</h3>
                            <ul>
                                <li>防潮、防晒、防锈、防震</li>
                                <li>专业运输服务</li>
                            </ul>
                        </div>
                        <div class="column">
                            <h3>售后服务</h3>
                            <ul>
                                <li>提供完整技术资料</li>
                                <li>安装调试服务</li>
                                <li>故障及时响应</li>
                                <li>提供技术培训计划</li>
                            </ul>
                            
                            <div class="success-box">
                                <h4>质保信息</h4>
                                <p><span class="highlight">三年免费质保</span>，全新原装</p>
                                <p>7×24小时技术支持</p>
                                <p>重要部件备件库存</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">5 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 6: 设备查重 -->
            <div class="slide" id="slide-6">
                <div class="slide-header">
                    <h1 class="slide-title">科研仪器设备查重情况</h1>
                </div>
                <div class="slide-content">
                    <h3>院内设备现状</h3>
                    <div class="info-box">
                        <p>现有设备：4台RTX 3090服务器</p>
                        <p>负载率：<span class="highlight">≥95%</span></p>
                        <p>排队时间：<span class="highlight">≥72小时</span></p>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>设备利用率</span>
                            <span>95%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 95%"></div>
                        </div>
                    </div>
                    
                    <h3>不可共享性</h3>
                    <div class="warning-box">
                        <ul style="margin: 0">
                            <li>具身智能数据需本地化部署，确保安全性</li>
                            <li>实时交互要求，不适合远程共享</li>
                            <li>长期稳定运行需求，不适合短期调度</li>
                        </ul>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">6 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 7: 需求调查 -->
            <div class="slide" id="slide-7">
                <div class="slide-header">
                    <h1 class="slide-title">需求调查情况</h1>
                </div>
                <div class="slide-content">
                    <h3>市场调研</h3>
                    <div class="info-box">
                        <p>8卡A100服务器报价：<span class="highlight">120-130万元</span></p>
                        <p>预算预留10%溢价空间</p>
                    </div>
                    
                    <h3>供应商情况</h3>
                    <div class="two-column">
                        <div class="column">
                            <ul>
                                <li>浪潮</li>
                                <li>联想</li>
                                <li>戴尔</li>
                            </ul>
                        </div>
                        <div class="column">
                            <ul>
                                <li>华为</li>
                                <li>超聚变</li>
                                <li>曙光</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="warning-box">
                        <h4>供应链风险</h4>
                        <p>A100出口管制不确定，建议加速采购</p>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">7 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 8: 运行维护 -->
            <div class="slide" id="slide-8">
                <div class="slide-header">
                    <h1 class="slide-title">运行维护技术队伍及能力</h1>
                </div>
                <div class="slide-content">
                    <h3>运行维护人员</h3>
                    <div class="two-column">
                        <div class="column">
                            <div class="tech-item">
                                <div class="tech-item-label">专职管理人</div>
                                <div class="tech-item-value">（待填写）</div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="tech-item">
                                <div class="tech-item-label">技术人员</div>
                                <div class="tech-item-value">（待填写）</div>
                            </div>
                        </div>
                    </div>
                    
                    <h3>运行维护能力</h3>
                    <div class="info-box">
                        <p>年度维护：约<span class="highlight">2万元</span></p>
                        <p>提供免费远程技术支持</p>
                    </div>
                    
                    <h3>人员培训</h3>
                    <ul>
                        <li>厂商提供现场培训</li>
                        <li>定期技术交流会</li>
                        <li>联合开发技术研讨会</li>
                    </ul>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">8 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
            
            <!-- Slide 9: 环境保障 -->
            <div class="slide" id="slide-9">
                <div class="slide-header">
                    <h1 class="slide-title">环境及保障措施</h1>
                </div>
                <div class="slide-content">
                    <div class="two-column">
                        <div class="column">
                            <h3>设备放置环境</h3>
                            <div class="info-box">
                                <p><span class="highlight">地点</span>：至德楼3楼机房</p>
                                <p><span class="highlight">面积</span>：约20平方米</p>
                                <p><span class="highlight">安保</span>：24小时监控，门禁系统</p>
                            </div>
                            
                            <h3>水、电、气保障</h3>
                            <ul>
                                <li>双路供电</li>
                                <li>UPS备用电源</li>
                                <li>精密空调系统</li>
                            </ul>
                        </div>
                        <div class="column">
                            <h3>维护保养计划</h3>
                            <ul>
                                <li>保修期内厂家负责</li>
                                <li>期满后专人维护</li>
                                <li>定期硬件检测</li>
                                <li>软件系统更新</li>
                            </ul>
                            
                            <div class="success-box">
                                <h4>安全管理</h4>
                                <ul style="margin: 0">
                                    <li>制定操作流程与应急预案</li>
                                    <li>定期安全检查</li>
                                    <li>数据备份与恢复机制</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div class="slide-number">9 / 9</div>
                    <div class="controls">
                        <button class="control-btn" onclick="prevSlide()">上一页</button>
                        <button class="control-btn" onclick="nextSlide()">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentSlide = 1;
        const totalSlides = 9;
        
        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            
            if (n > totalSlides) {
                currentSlide = 1;
            } else if (n < 1) {
                currentSlide = totalSlides;
            } else {
                currentSlide = n;
            }
            
            slides.forEach(slide => {
                slide.classList.remove('active');
            });
            
            document.getElementById(`slide-${currentSlide}`).classList.add('active');
            
            // 更新URL哈希，但不在打印模式下
            if (!window.matchMedia('print').matches) {
                window.location.hash = `slide-${currentSlide}`;
            }
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function prevSlide() {
            showSlide(currentSlide - 1);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });
        
        // 检查URL哈希以显示特定幻灯片
        window.addEventListener('load', function() {
            if (window.location.hash) {
                const slideId = window.location.hash.substring(1);
                const slideNumber = parseInt(slideId.split('-')[1]);
                if (!isNaN(slideNumber) && slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        });
        
        // 添加打印前的处理函数
        window.addEventListener('beforeprint', function() {
            // 确保所有幻灯片在打印时都可见
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.opacity = '1';
                slide.style.position = 'relative';
                slide.style.display = 'block';
            });
            
            // 隐藏所有页脚
            document.querySelectorAll('.slide-footer').forEach(footer => {
                footer.style.display = 'none';
            });
        });
    </script>
</body>
</html>