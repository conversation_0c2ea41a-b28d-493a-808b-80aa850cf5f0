# **UI-TARS：一种基于原生代理的自动化GUI交互开创性方法**

图形用户界面（GUI）的自动化在各个领域变得越来越重要，包括软件测试、为残障用户提升可访问性，以及通过任务自动化提高整体生产力。传统的GUI自动化工具，如Appium和Selenium，在这一领域发挥了重要作用。然而，这些工具通常存在局限性，尤其是它们对UI元素的平台特定标识符（如XPath或无障碍ID）的依赖。当UI的底层结构发生变化时，这种依赖性可能导致工具脆弱性。此外，处理动态和视觉复杂的界面对这些传统方法提出了持续的挑战。为解决这些局限性，字节跳动推出了UI-TARS（用户界面-任务自动化与推理系统），这是一种新颖的原生GUI代理模型。该系统通过仅使用屏幕截图作为输入来模拟人类用户的交互行为，从而区别于其他工具^1^。本报告对UI-TARS进行了全面分析，探讨了其创新方法论、技术细节、实验验证、架构设计及其代码库结构。

---

## **2. UI-TARS的方法论创新**

UI-TARS融合了多项关键方法论创新，使其与现有的GUI自动化工具（如Appium和Selenium）显著不同。这些创新提升了其能力和性能^2^。

### **增强的感知能力**
UI-TARS的核心创新之一在于其增强的感知能力。与传统工具依赖UI元素的底层结构和属性不同，UI-TARS利用大规模的GUI屏幕截图数据集。这种丰富的视觉数据使模型能够实现上下文感知的UI元素理解和精确的标注^1^。这种方法允许UI-TARS基于视觉特征识别和解释GUI组件，提供了一种更直接、视觉驱动的方法，与Appium和Selenium依赖文档对象模型（DOM）或无障碍API的方式形成对比^1^。在处理视觉复杂或自定义UI元素时，DOM和无障碍API方法的局限性尤为明显，而UI-TARS的视觉感知能力在跨平台应用或无法直接访问UI结构的场景（如游戏或嵌入式系统）中表现出显著优势。

### **统一动作建模**
另一项重要创新是UI-TARS的**统一动作建模**。该框架将动作标准化为跨平台（包括桌面、移动和Web环境）的统一空间^1^。这种标准化通过利用大规模动作轨迹实现了精确的“接地”（即关联视觉元素与屏幕坐标）。传统工具（如Appium和Selenium）需要针对不同平台使用特定命令集执行点击、输入和滚动等操作，而UI-TARS的统一动作建模提供了一种更抽象且鲁棒性更强的执行方式^1^。开发者可定义高层动作，由UI-TARS将其转换为底层平台的具体指令，从而提高代码复用性和抽象层级。

### **系统-2推理**
UI-TARS将**系统性推理**融入多步决策过程（称为“系统-2推理”）^2^。其推理模式包括任务分解（将复杂任务拆解为可管理的子步骤）、反思性思考（评估历史动作以优化后续步骤）和里程碑识别（追踪任务目标的进展）^1^。这种认知能力使UI-TARS能够规划和执行复杂的交互，超越了Appium和Selenium的简单指令执行模式。UI-TARS不仅能执行预设指令，还能根据GUI的当前状态自主调整策略，实现更智能的自动化。

### **基于反思式在线轨迹的迭代训练**
为解决GUI自动化中的数据稀缺问题，UI-TARS采用了一种创新的迭代训练框架。该框架通过数百台虚拟机自动收集、过滤和优化交互轨迹^2^。这种持续学习能力与传统工具的静态特性（需手动更新以适应UI变化）形成鲜明对比^1^。通过从交互中持续学习并根据反馈优化策略，UI-TARS可在应用UI变化时保持高效，减少对脚本手动更新的依赖。

---

## **3. UI-TARS的技术细节**

### **状态空间建模**
UI-TARS将GUI环境建模为状态空间，每个唯一的视觉配置（由屏幕截图表示）被视为一个独立状态。状态间的转换由“统一动作建模”定义，每个标准化动作可能导致状态变化^1^。“系统-2推理”通过规划动作序列帮助模型导航状态空间，而“增强的感知”确保对当前GUI状态的准确理解^1^。通过“迭代训练”，模型不断学习状态转换的动态特性，提升动作预测和执行的准确性。

### **多模态元素定位（CV + AST）**
UI-TARS通过计算机视觉（CV）技术实现高精度的“接地”能力，即准确关联视觉元素与其屏幕坐标^4^。其核心依赖大规模GUI截图数据集，表明CV是元素定位的主要方式^1^。这一视觉驱动方法对UI代码变化的鲁棒性更强，且能识别缺乏显式结构信息的元素。

### **实验评估指标设计**
UI-TARS的性能通过多个GUI代理基准测试严格评估，涵盖感知、接地和任务执行能力^1^。主要基准包括：
- **VisualWebBench**：评估对Web界面的理解与交互能力^3^。
- **ScreenSpot Pro/ScreenSpot**：测试元素识别和定位的精度^3^。
- **OSWorld**：模拟操作系统环境中的多步骤任务完成率^1^。
- **AndroidWorld/Android Control**：评估Android移动端自动化能力^1^。
- **GUI Odyssey**：测试长序列任务的导航与交互能力^3^。
- **WebSRC**：Web交互相关基准^4^。
- **ScreenQA-short/ScreenQA**：通过问答评估移动端内容理解^3^。

UI-TARS在这些基准测试中均表现出色，性能超越GPT-4o和Claude等基线模型^1^。

---

## **4. 技术架构与数据/控制流**

UI-TARS桌面应用的架构包含以下核心组件：

| **组件**           | **数据流角色**         | **控制流角色**         | **相关片段**       |
|--------------------|------------------------|------------------------|--------------------|
| 用户界面（桌面应用） | 接收自然语言指令       | 启动自动化，管理主循环 | ^8^, ^7^, ^10^, ^9^|
| 屏幕捕获模块       | 提供截图作为视觉输入   | 触发每一步的观察阶段   | ^3^, ^4^, ^10^     |
| UI-TARS模型        | 接收指令和截图，预测动作 | 提供推理和决策逻辑     | ^3^, ^11^-^13^     |
| 动作执行器         | 将预测动作转换为OS指令 | 执行用户计算机上的操作 | ^3^, ^4^, ^10^     |
| 记忆模块           | 存储任务上下文和历史数据 | 影响推理过程           | ^3^, ^12^          |
| 语音I/O模块        | 处理自然语言与语音转换 | 提供替代交互方式       | ^8^                |

控制流始于用户通过自然语言指令启动任务，随后循环执行以下步骤：
1. **观察**：捕获当前屏幕截图。
2. **推理**：基于指令和截图生成动作决策（结合系统-1快速响应和系统-2规划）。
3. **执行**：将动作转换为OS指令并执行。
4. **反馈**：实时更新任务状态，利用记忆模块优化后续步骤。

---

## **5. UI-TARS桌面版代码结构分析**

UI-TARS桌面版代码库采用现代化的JavaScript/TypeScript单仓库架构^7^，主要目录和功能如下：

### **根目录结构**
- **配置管理**：`.changeset`（版本管理）、`.github`（工作流配置）、`.husky`（Git钩子脚本）。
- **核心代码**：`apps/desktop`（主应用逻辑）、`packages`（模块化功能包）。
- **文档与示例**：`docs`（部署指南）、`examples`（用法示例）。
- **工具脚本**：`scripts`（构建和测试脚本）。

### **核心功能包**
- **视觉与交互**：`screen-capture`（屏幕捕获）、`robot-executor`（动作执行）、`vllm`（视觉语言模型交互）。
- **多媒体处理**：`ffmpeg`（视频/音频处理）、`tts`（语音合成）。
- **状态管理**：`zustand-middleware`（状态存储）、`yjs-utils`（协作功能）。
- **平台适配**：`electron-app`（桌面端框架）、`playwright-utils`（Web自动化）。

---

## **6. 结论**

UI-TARS通过增强感知、统一动作建模、系统性推理和持续学习机制，显著提升了GUI自动化的鲁棒性和跨平台兼容性。其基于视觉的交互方式能够适应动态UI变化，减少对底层结构的依赖。实验验证表明，UI-TARS在复杂任务执行和多平台测试中均达到领先水平。代码库的模块化设计和开源特性为未来扩展和协作提供了坚实基础。未来研究可进一步优化推理能力、错误处理和交互范式。

---

#### **参考文献**
1. [UI-TARS: Pioneering Automated GUI Interaction with Native Agents - ResearchGate](https://www.researchgate.net/publication/388317146_UI-TARS_Pioneering_Automated_GUI_Interaction_with_Native_Agents)
2. [UI-TARS: Pioneering Automated GUI Interaction with Native Agents - Paper Details](https://www.chatpaper.ai/paper/18255aa1-67f3-4e78-b2b8-5624e664c404)
3. [UI-TARS-desktop GitHub仓库](https://github.com/bytedance/UI-TARS-desktop)
（其余参考文献链接请参见原文）