import fire  # 导入fire库，用于创建命令行接口
import json  # 导入json库，用于处理JSON数据
import numpy as np  # 导入numpy库，用于数值计算
import os  # 导入os库，用于文件和目录操作
import pickle as pkl  # 导入pickle库，用于序列化和反序列化Python对象
import torch  # 导入PyTorch库，用于深度学习
import tqdm  # 导入tqdm库，用于显示进度条
from sklearn.model_selection import train_test_split  # 导入用于数据集划分的函数
from torch import nn, optim  # 导入PyTorch的神经网络和优化器模块


def main(
        layer_idx,  # 要分析的模型层索引
        data_dir,  # 数据目录路径
        output_dir=,  # 输出目录路径
        json_name=,  # JSON文件名
        embedding_dir_name,  # embedding目录名
        num_labels=10,  # 分类标签数量
        prompt=False,  # 是否基于提示词embedding进行预测
        seed=0,  # 随机种子
        use_cuda=False,  # 是否使用CUDA
        save_model=None,  # 保存模型的路径
        save_data=None,  # 保存数据的路径
        save_pred=True  # 是否保存预测结果
    ):

    with open(os.path.join(data_dir, json_name), 'r') as fin:  # 打开JSON文件
        record_info = json.load(fin)['records'][1:]  # 加载记录信息，跳过第一条记录
    
    split_file = os.path.join(data_dir, 'split', f'seed{seed}.json')  # 定义数据集划分文件路径
    if not os.path.isfile(split_file):  # 如果划分文件不存在
        os.system('mkdir -p ' + os.path.join(data_dir, 'split'))  # 创建split目录
        record_ids = [x['record_id'] for x in record_info]  # 提取所有记录ID
        train_ids, test_ids = train_test_split(record_ids, random_state=seed)  # 划分训练集和测试集
        split = {'train': train_ids, 'test': test_ids}  # 创建划分字典
        with open(split_file, 'w') as fout:  # 打开文件用于写入
            json.dump(split, fout, indent=2)  # 保存划分信息到JSON文件
    else:  # 如果划分文件已存在
        with open(split_file, 'r') as fin:  # 打开文件用于读取
            split = json.load(fin)  # 加载划分信息
    
    os.system(f'mkdir -p {output_dir}')  # 创建输出目录

    if prompt:  # 如果基于提示词embedding进行预测
        print('Prediction based on averaged prompt embedding')  # 打印预测方式
    else:  # 如果基于生成token的embedding进行预测
        print('Prediction based on last generated token')  # 打印预测方式

    # 初始化数据列表
    X_train, Y_train, X_test, Y_test = list(), list(), list(), list()  # 初始化训练集和测试集
    test_prompt_info = list()  # 初始化测试提示信息列表
    incomplete_prompts = set()  # 初始化不完整提示集合
    for record in tqdm.tqdm(record_info):  # 遍历所有记录，显示进度条
        X, Y = list(), list()  # 初始化当前记录的特征和标签列表
        iters = record['iterations']  # 获取迭代信息
        iteration_count = record['iteration_count']  # 获取迭代次数

        if prompt:  # 如果基于提示词embedding进行预测

            layer_infos = [x for x in iters[0]['layers'] if x['layer_id'] == layer_idx]  # 获取指定层的信息
            if len(layer_infos) > 0:  # 如果找到了指定层的信息
                layer_info = layer_infos[0]  # 获取第一个匹配的层信息
                assert(layer_info['layer_id'] == layer_idx)  # 确保层ID正确
                layer_ts = layer_info['iter_ts']  # 获取时间戳

                tensor_path = os.path.join(data_dir, embedding_dir_name, f'L{layer_idx}_{layer_ts}+.pt')  # 构建张量文件路径
                if os.path.isfile(tensor_path):  # 如果张量文件存在
                    tensor = torch.load(tensor_path)[0].cpu()  # 加载张量到CPU
                    avg_tensor = torch.mean(tensor, dim=0)  # 计算平均张量
                    X.append(torch.reshape(avg_tensor, (1, -1)))  # 添加到特征列表
                    Y.append(iteration_count - 1)  # 添加到标签列表（剩余步骤数）
                else:  # 如果张量文件不存在
                    incomplete_prompts.add(record['record_id'])  # 添加到不完整提示集合

        else:  # 如果基于生成token的embedding进行预测

            for iter_idx, iteration in enumerate(iters[1:]):  # 遍历除第一个外的所有迭代

                layer_infos = [x for x in iteration['layers'] if x['layer_id'] == layer_idx]  # 获取指定层的信息
                layer_info = None  # 初始化层信息
                if len(layer_infos) > 0:  # 如果找到了指定层的信息
                    layer_info = layer_infos[0]  # 获取第一个匹配的层信息
                
                if layer_info is not None:  # 如果层信息不为空
                    assert layer_info['layer_id'] == layer_idx, f"record {record['record_id']}, iter {iter_idx}, actual layer id {layer_info['layer_id']}"  # 确保层ID正确
                    layer_ts = layer_info['iter_ts']  # 获取时间戳

                    tensor_path = os.path.join(data_dir, embedding_dir_name, f'L{layer_idx}_{layer_ts}+.pt')  # 构建张量文件路径
                    try:  # 尝试加载张量
                        if os.path.isfile(tensor_path):  # 如果张量文件存在
                            tensor = torch.load(tensor_path)[0].cpu()  # 加载张量到CPU
                            X.append(tensor)  # 添加到特征列表
                            Y.append(iteration_count - 1 - 1 - iter_idx)  # 添加到标签列表（剩余步骤数）
                        else:  # 如果张量文件不存在
                            incomplete_prompts.add(record['record_id'])  # 添加到不完整提示集合
                    except:  # 如果加载失败
                        if os.path.isfile(tensor_path):  # 如果文件存在但加载失败
                            print(tensor_path, "corrupted")  # 打印文件已损坏
                        continue  # 继续下一个迭代

        if len(X) > 0:  # 如果特征列表不为空
            if len(X) > 1:  # 如果有多个特征
                X = torch.cat(X, dim=0)  # 沿第0维连接特征
            else:  # 如果只有一个特征
                X = X[0]  # 取第一个特征

            if record['record_id'] in split['train']:  # 如果记录ID在训练集中
                X_train.append(X)  # 添加到训练特征
                Y_train.extend(Y)  # 添加到训练标签
            else:  # 如果记录ID在测试集中
                X_test.append(X)  # 添加到测试特征
                Y_test.extend(Y)  # 添加到测试标签

                for rs in Y:  # 遍历标签
                    test_prompt_info.append({  # 添加测试提示信息
                        'id': record['record_id'],  # 记录ID
                        'remaining_steps': rs  # 剩余步骤数
                    })  
    X_train_torch = torch.cat(X_train, dim=0).to(torch.float32)  # 连接所有训练特征并转换为float32
    X_test_torch = torch.cat(X_test, dim=0).to(torch.float32)  # 连接所有测试特征并转换为float32

    print('min and max raw labels', np.amin(Y_train), np.amax(Y_train))  # 打印原始标签的最小值和最大值
    Y_train = np.digitize(Y_train, np.linspace(0, 512, num_labels + 1)) - 1  # 将连续标签离散化为类别
    Y_train_torch = torch.tensor(Y_train, dtype=torch.long)  # 转换为PyTorch张量
    print('min and max digitized labels', np.amin(Y_train), np.amax(Y_train))  # 打印离散化后标签的最小值和最大值
    assert(np.amin(Y_train) >= 0)  # 确保最小标签不小于0
    assert(np.amax(Y_train) < num_labels)  # 确保最大标签小于标签数量
    Y_test_torch = torch.tensor(np.digitize(Y_test, np.linspace(0, 512, num_labels + 1)) - 1, dtype=torch.long)  # 离散化测试标签并转换为PyTorch张量

    perm_idx = np.random.permutation(X_train_torch.size(0))  # 生成随机排列索引
    X_train_torch = X_train_torch[perm_idx]  # 随机打乱训练特征
    Y_train_torch = Y_train_torch[perm_idx]  # 随机打乱训练标签

    if save_data is not None:  # 如果需要保存数据
        tensor_save_path = os.path.join(output_dir, save_data)  # 构建保存路径
        os.system(f'mkdir -p {tensor_save_path}')  # 创建保存目录

        fnames = ['X_train.pt', 'Y_train.pt', 'X_test.pt', 'Y_test.pt']  # 文件名列表
        datas = [X_train_torch, Y_train_torch, X_test_torch, Y_test_torch]  # 数据列表

        for dtensor, fname in zip(datas, fnames):  # 遍历数据和文件名
            torch.save(dtensor, os.path.join(tensor_save_path, fname))  # 保存张量
        
        with open(os.path.join(tensor_save_path, 'test_prompt.json'), 'w') as fout:  # 打开文件用于写入
            json.dump(test_prompt_info, fout, indent=2)  # 保存测试提示信息

    print('Train dim', X_train_torch.size(), Y_train_torch.size())  # 打印训练集维度
    print('Test dim', X_test_torch.size(), Y_test_torch.size())  # 打印测试集维度

    model = nn.Sequential(  # 创建序列模型
        nn.Linear(X_train_torch.shape[1], 512),  # 第一个线性层
        nn.ReLU(),  # ReLU激活函数
        nn.Linear(512, num_labels)  # 第二个线性层
    )

    if torch.cuda.is_available() and use_cuda:  # 如果CUDA可用且需要使用
        model.to('cuda')  # 将模型移到GPU
        X_train_torch = X_train_torch.to('cuda')  # 将训练特征移到GPU
        Y_train_torch = Y_train_torch.to('cuda')  # 将训练标签移到GPU
        X_test_torch = X_test_torch.to('cuda')  # 将测试特征移到GPU
        Y_test_torch = Y_test_torch.to('cuda')  # 将测试标签移到GPU

    batch_size = 32  # 设置批量大小
    num_epochs = 30  # 设置训练轮数
    initial_lr = 0.01  # 设置初始学习率
    final_lr = 0.  # 设置最终学习率

    # 定义损失函数（交叉熵）和优化器
    criterion = nn.CrossEntropyLoss()  # 创建交叉熵损失函数
    optimizer = optim.AdamW(model.parameters(), lr=initial_lr, weight_decay=0.01)  # 创建AdamW优化器

    # 学习率的余弦衰减
    lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, num_epochs, eta_min=final_lr)  # 创建学习率调度器

    # 训练模型
    for epoch in range(num_epochs):  # 遍历每个训练轮次
        model.train()  # 设置模型为训练模式
        with tqdm.tqdm(total=len(X_train_torch), desc=f"Epoch {epoch}") as pbar:  # 创建进度条
            for batch_idx in range(0, len(X_train_torch), batch_size):  # 遍历每个批次
                X_batch = X_train_torch[batch_idx:batch_idx+batch_size]  # 获取当前批次的特征
                Y_batch = Y_train_torch[batch_idx:batch_idx+batch_size]  # 获取当前批次的标签
                optimizer.zero_grad()  # 清空梯度
                Y_batch_pred = model(X_batch)  # 前向传播
                loss = criterion(Y_batch_pred, Y_batch)  # 计算损失
                loss.backward()  # 反向传播
                optimizer.step()  # 更新参数
                pbar.set_description(f"Epoch {epoch} Loss {loss.item()}")  # 更新进度条描述
                pbar.update(batch_size)  # 更新进度条

            lr_scheduler.step()  # 更新学习率
        
        model.eval()  # 设置模型为评估模式
        with torch.no_grad():  # 不计算梯度
            Y_test_pred_raw = model(X_test_torch)  # 对测试集进行预测
            Y_test_pred = torch.argmax(Y_test_pred_raw, dim=1)  # 获取预测类别
            err = (Y_test_pred != Y_test_torch).sum().item() / len(Y_test_torch)  # 计算错误率
            print(f'Error: {err}, Epoch: {epoch}, lr: {optimizer.param_groups[0]["lr"]}')  # 打印错误率、轮次和学习率

            if epoch == num_epochs - 1:  # 如果是最后一轮
                pred_np = Y_test_pred_raw.detach().cpu().numpy()  # 将预测结果转换为NumPy数组
                assert(pred_np.shape[0] == len(test_prompt_info))  # 确保预测结果数量正确

                for ridx in range(pred_np.shape[0]):  # 遍历每个预测结果
                    test_prompt_info[ridx]['pred'] = pred_np[ridx, :]  # 将预测结果添加到测试提示信息中
                
                if prompt:  # 如果基于提示词embedding进行预测
                    pstring = '_prompt'  # 设置文件名后缀
                else:  # 如果基于生成token的embedding进行预测
                    pstring = ""  # 设置空文件名后缀

                if save_pred:  # 如果需要保存预测结果
                    with open(os.path.join(output_dir, f'L{layer_idx}_class{num_labels}{pstring}_seed{seed}.pkl'), 'wb') as fout:  # 打开文件用于写入
                        pkl.dump(test_prompt_info, fout)  # 保存测试提示信息
    
    if save_model is not None:  # 如果需要保存模型
        os.system("mkdir -p {}".format(os.path.join(output_dir, save_model)))  # 创建保存目录
        model_path = os.path.join(output_dir, save_model, 'model.pth')  # 构建模型保存路径
        torch.save(model.state_dict(), model_path)  # 保存模型参数

if __name__ == '__main__':  # 如果作为主程序运行
    fire.Fire(main)  # 使用fire库启动main函数
