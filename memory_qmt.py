我需要开发一个股票量化程序，包含三个独立模型，记住特征输入模块需要保持高度的可扩展性，特征会不断增加.所有代码写在/workspace/qm中：

​​XGBoost模型1​​：预测某股票未来10日出现“明显拉升”的概率（分类问题）
​​XGBoost模型2​​：预测某股票明日开盘价上涨的概率（二分类问题）
​​时序模型​​：预测明日开盘价、收盘价、最高价（多目标回归问题）
模型1：10日拉升概率预测（XGBoost分类）​​
​​目标定义​​：
明显拉升 = 未来10个交易日同时满足：
✅ 日均涨幅 > 1%
✅ 至少4个交易日单日涨幅 > 2%
​​输出​​：事件发生概率值（0-1） 模型2：明日开盘涨跌概率（XGBoost二分类）​​
​​目标定义​​：
上涨：明日开盘价 > 今日收盘价 → 标签 ​​1​​
下跌：明日开盘价 ≤ 今日收盘价 → 标签 ​​-1​​
​​输出​​：上涨概率值（-1到1）多目标价格预测（时序回归）​​
​​预测目标​​：明日开盘价、收盘价、最高价（三个独立输出）
​​模型选择​​：
优先使用​​LSTM​​或​​Transformer​​（需处理多步输出）
备选方案：​​ARIMA​​或​​Prophet​​（若数据量不足）
​​输出格式​​：长度为3的浮点数组 [open_pred, close_pred, high_pred]。​​基础特征​​（所有模型共用）：
开盘价、收盘价、最高价、最低价、成交量
前1/3/5日收益率、波动率
​​技术指标​​（至少包含）：
移动平均线（SMA_5, SMA_10, SMA_20）
MACD（含信号线和柱状图）
RSI（14日）
布林带（上轨、下轨）
​​时序特征​​（模型3专用）：
滞后特征（lag=1,3,5）
季节性分解（周/月周期）


conda环境，数据源为免费api接口，训练模型的数据可以通过现有的免费中国a股市场的接口下载。使用pytorch框架，并提前下载5年内的数据用做训练和回测。需要实现实时预测，但是每日只需要进行两次预测，频率要求并不高。


使用https://github.com/1nchaos/adata 这个数据源，具体他的接口和介绍在这里：https://adata.30006124.xyz/dic/stockInfo.html。5年的数据量太大了 ，请你使用最近2年的就可以SQlite进行存储。定时任务，9.35预测一次，然后14.50预测一次。当然时序模型的预测任务也要设在这两个数据点，有必要可以设置两个时序模型，分别应对9.35和14.50时间点的预测。需要web界面。特征我会不断的增加，构建新的量化因子，因此需要保持可扩展性，加入新的因子，模型仍然可以训练。需要完整的回测系统，回测指标就是你说的那些就足够了，当然增加一项新的也是可以的。同时，不要在本服务器进行真实的训练任务和回测，不用下载大量数据，只需要极少量的数据验证代码可以运行即可。同时代码至少有函数级的注视，每个函数不要超过50行有效代码，尽可能一个函数一个功能，做到解耦，参看华为的代码要求。

现在我已经深入了解了adata数据源的接口和功能。基于您的需求，我已经收集了足够的信息来制定详细的实施计划。

关键发现：

adata数据源：提供丰富的A股数据接口，包括股票基本信息、行情数据、财务数据等
主要接口：
stock.info.all_code() - 获取所有股票代码
stock.market.get_market() - 获取K线数据
stock.market.get_market_current() - 获取实时行情
stock.info.trade_calendar() - 获取交易日历
数据格式：标准的OHLCV格式，支持前复权数据
技术要求：conda环境、PyTorch框架、SQLite存储、Web界面、定时任务
架构设计考虑：

高度可扩展的特征工程模块
三个独立的模型管道
统一的数据管理和预测调度
完整的回测系统
函数级注释，每个函数不超过50行代码
让我进入下一个模式来设计解决方案。

[MODE: INNOVATE]

基于深入的技术调研，我来探讨多种架构方案和技术选择：

核心架构方案对比：

方案1：微服务架构

优点：模块完全解耦，易于扩展和维护
缺点：复杂度较高，适合大型系统
适用性：对于三个模型的需求可能过度设计
方案2：模块化单体架构（推荐）

优点：结构清晰，易于开发和调试，符合当前需求规模
缺点：模块间耦合度相对较高
适用性：非常适合当前的三模型量化系统
方案3：插件化架构

优点：特征扩展性极强，符合"特征会不断增加"的需求
缺点：初期开发复杂度较高
适用性：可以与方案2结合使用
特征工程扩展性设计方案：

方案A：配置驱动的特征工厂

方案B：装饰器模式的特征注册

方案C：继承式特征扩展

模型架构选择：

XGBoost模型：

优点：性能稳定，解释性强，适合表格数据
实现：使用scikit-learn接口，便于集成
时序模型选择：

LSTM方案：适合序列建模，PyTorch实现灵活
Transformer方案：更强的长期依赖建模能力
混合方案：CNN+LSTM，结合局部特征和序列特征
数据管理策略：

方案1：分层存储

原始数据层：SQLite存储OHLCV数据
特征数据层：计算后的技术指标
模型数据层：训练就绪的特征矩阵
方案2：时间分区存储

按月/季度分区，提高查询效率
支持增量更新
定时任务设计：

方案A：APScheduler

优点：Python原生，易于集成
支持cron表达式，精确控制时间
方案B：Celery

优点：分布式任务队列，更强大
缺点：复杂度较高，可能过度设计
Web界面技术选择：

方案1：Flask + Bootstrap

优点：轻量级，快速开发
适合：展示预测结果和回测报告
方案2：FastAPI + Vue.js

优点：现代化，API文档自动生成
适合：更复杂的交互需求
回测系统设计：

事件驱动回测：

模拟真实交易环境
支持多种回测指标计算
向量化回测：

计算效率更高
适合大规模历史数据回测
架构方案使用方案3和方案2的结合，你自我权衡，以方便看懂，修改为主。配置驱动的特征工厂更好。数据管理策略采用分层存储。定时任务直接使用apsheduler就可以，不用过于复杂。web界面使用flask+boostrap即可，注意要美观。回测系统可以实现事件驱动回测，都实现一下，并在web端展示。