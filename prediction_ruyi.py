import tensorflow as tf
import transformers
import os
import random
import numpy as np
import pandas as pd
import logging
from transformers import BertTokenizerFast, TFAlbertForSequenceClassification
from functools import partial
from cc_utils import get_dataloader

# =====================
# 设置随机种子，保证实验可复现
# =====================
seed = 42
os.environ['PYTHONHASHSEED'] = str(seed)
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

# =====================
# 禁用TensorFlow日志
# =====================
logging.getLogger('tensorflow').disabled = True
logging.getLogger('tensorflow').setLevel(logging.ERROR)

# =====================
# 参数配置（与train.py保持一致）
# =====================
seq_len = 512
batch_size = 16
mode = "test"
valid_tfrecord_path = f"hdfs://haruna/user/tiger/xql/liuruyi/bmodel/data/{mode}_valid_tfrecord"
buffer_size = 6
shuffle_size = 6
num_parallel_calls = -1
num_parallel_reads = -1

# =====================
# 数据加载函数（与train.py一致）
# =====================
def example_parser(example, feature_description, evaluate_flag):
    feature = tf.io.parse_example(example, feature_description)
    input_ids = feature['input_ids']
    attention_mask = feature['attention_mask']
    label = feature['label']
    return {'input_ids': input_ids, 'attention_mask': attention_mask}, label

def get_dataloader(data_path, seq_len, batch_size, evaluate_flag=False):
    label_name = 'label'
    basic_cols = ['user_id', 'date']
    feature_cols = ['input_ids', 'attention_mask']
    feature_description = {}
    for key in [label_name] + basic_cols + feature_cols:
        if key == label_name:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.int64, default_value=[0])})
        elif key in basic_cols:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.string, default_value=[""])})
        else:
            feature_description.update({key: tf.io.FixedLenFeature([seq_len], tf.int64, default_value=[0]*seq_len)})
    filelist = tf.io.gfile.glob(os.path.join(data_path, "*.gz"))
    options = tf.data.Options()
    options.experimental_deterministic = True if evaluate_flag is True else False
    options.experimental_threading.private_threadpool_size = 5
    options.experimental_threading.max_intra_op_parallelism = 1
    autotune = tf.data.experimental.AUTOTUNE
    dataloader = tf.data.TFRecordDataset(
        filelist,
        compression_type="GZIP",
        num_parallel_reads=num_parallel_reads if num_parallel_reads is not None else autotune
    )
    dataloader = dataloader.with_options(options)
    dataloader = dataloader.batch(batch_size)
    dataloader = dataloader.map(
        partial(example_parser, feature_description=feature_description, evaluate_flag=evaluate_flag),
        num_parallel_calls=num_parallel_calls if num_parallel_calls is not None else autotune
    )
    dataloader = dataloader.prefetch(buffer_size=buffer_size)
    return dataloader

# =====================
# 加载验证集
# =====================
valid_dataloader = get_dataloader(valid_tfrecord_path, seq_len=seq_len, batch_size=batch_size, evaluate_flag=True)

# =====================
# 统计正负样本分布
# =====================
label_counter = {0: 0, 1: 0}
for _, labels in valid_dataloader:
    labels_np = labels.numpy().flatten()
    for l in labels_np:
        if l in label_counter:
            label_counter[l] += 1
        else:
            label_counter[l] = 1
print("\n[验证集标签分布]")
print(f"负样本(0): {label_counter.get(0, 0)}  正样本(1): {label_counter.get(1, 0)}  其它: {sum(v for k,v in label_counter.items() if k not in [0,1])}")

# =====================
# 加载模型
# =====================
model = TFAlbertForSequenceClassification.from_pretrained('training_results_5/model_epoch_1', num_labels=2)

# =====================
# 编译模型（与train.py一致）
# =====================
optimizer = tf.keras.optimizers.Adam(learning_rate=2e-5)
loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
model.compile(optimizer=optimizer, loss=loss, metrics=['sparse_categorical_accuracy'])

# =====================
# 评估并打印验证集输入和模型输出
# =====================
print("\n==================== 验证集评估 ====================")
val_results = model.evaluate(valid_dataloader, verbose=1)
print(f"Validation Loss: {val_results[0]:.6f}")
print(f"Validation Accuracy: {val_results[1]:.6f}")

print(f"\n[验证集样本输入与模型输出] (仅展示前2个batch)")
for batch_idx, (inputs, labels) in enumerate(valid_dataloader):
    if batch_idx >= 2:  # 只展示前2个batch
        break
        
    # 获取模型输出
    outputs = model(inputs, training=False)
    if isinstance(outputs, tuple):
        logits = outputs[0]  # 第一个元素是logits
    else:
        logits = outputs.logits
        
    # 转换为numpy数组
    input_ids = inputs['input_ids'].numpy()
    attention_mask = inputs['attention_mask'].numpy()
    labels_np = labels.numpy()
    logits_np = logits.numpy()
    
    # 计算预测结果
    predictions = np.argmax(logits_np, axis=-1)
    
    print(f"\nBatch {batch_idx + 1}:")
    print("input_ids shape:", input_ids.shape)
    print("attention_mask shape:", attention_mask.shape)
    print("labels shape:", labels_np.shape)
    print("logits shape:", logits_np.shape)
    
    # 打印样本级别的详细信息
    for i in range(min(2, len(labels_np))):  # 每个batch只打印前2个样本
        print(f"\n样本 {i}:")
        print(f"标签: {labels_np[i]}")
        print(f"预测概率: {logits_np[i]}")
        print(f"预测类别: {predictions[i]}")
        print(f"是否正确: {predictions[i] == labels_np[i]}")
        
    # 打印batch级别的统计信息
    batch_acc = np.mean(predictions == labels_np)
    print(f"\nBatch {batch_idx + 1} 准确率: {batch_acc:.4f}")
    
    # 打印正负样本预测分布
    pos_preds = predictions[labels_np == 1]
    neg_preds = predictions[labels_np == 0]
    print(f"正样本预测为1的比例: {np.mean(pos_preds == 1):.4f}")
    print(f"负样本预测为0的比例: {np.mean(neg_preds == 0):.4f}")

# 打印模型结构信息
print("\n[模型结构信息]")
print(f"模型可训练参数数量: {model.count_params()}")
print("模型层信息:")
for layer in model.layers:
    print(f"层名: {layer.name}, 可训练: {layer.trainable}, 参数数量: {layer.count_params()}") 