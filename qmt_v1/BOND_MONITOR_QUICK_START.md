# 债券监控系统快速启动指南

## 系统概述

债券监控系统已成功实现，专注于中国债券市场监控：
- **监控时间**: 每个交易日14:30
- **监控内容**: 中短债、十年期长债、30年期长债、高等级信用债的当日利率涨跌幅
- **数据维护**: 自动维护30天历史涨跌幅队列

## 快速启动

### 1. 进入工作目录
```bash
cd /Users/<USER>/Desktop/cursor_temp/qmt_v1/workspace/qm
```

### 2. 启动债券监控调度器
```bash
python3 start_bond_monitor.py start
```

### 3. 手动执行一次监控（测试）
```bash
python3 start_bond_monitor.py monitor
```

### 4. 查看系统状态
```bash
python3 start_bond_monitor.py status
```

### 5. 查看历史数据
```bash
python3 start_bond_monitor.py history
```

## 系统文件结构

```
qmt_v1/
├── bond_monitor/                    # 债券监控模块
│   ├── bond_data_fetcher.py        # AkShare数据获取器
│   ├── bond_database.py            # 数据库管理器
│   ├── bond_monitor.py             # 监控核心逻辑
│   ├── bond_scheduler.py           # 独立调度器
│   ├── config.yaml                 # 监控配置
│   ├── main.py                     # 命令行接口
│   └── README.md                   # 详细说明
│
└── workspace/qm/
    ├── scheduler/
    │   └── bond_monitor_scheduler.py # 集成调度器
    ├── start_bond_monitor.py        # 启动脚本
    ├── config/database.yaml         # 数据库配置（已更新）
    └── requirements.txt             # 依赖（已更新）
```

## 监控输出示例

当系统正常运行时，您会看到类似以下的监控结果：

```
=== 债券监控结果 ===
监控日期: 2024-06-28
监控时间: 14:30:15
市场总结: 债券收益率整体上行。中短债上涨5.2bp; 十年期长债上涨8.1bp; 30年期长债上涨6.7bp; 高等级信用债上涨4.3bp。

各类债券涨跌幅:
  中短债: 上涨 5.2 bp
  十年期长债: 上涨 8.1 bp
  30年期长债: 上涨 6.7 bp
  高等级信用债: 上涨 4.3 bp
==================
```

## 数据存储

所有监控数据都存储在SQLite数据库中：
- **位置**: `data/quantitative_trading.db`
- **表结构**: 已自动创建债券相关表
- **数据保留**: 自动维护30天历史数据

## 重要说明

### AkShare接口
- 系统使用AkShare获取中国债券市场数据
- 需要稳定的网络连接
- 首次运行时会自动安装依赖

### 交易日判断
- 目前基于工作日（周一至周五）判断
- 可根据需要添加节假日逻辑

### 数据准确性
- 建议定期检查AkShare接口状态
- 系统包含完善的错误处理机制

## 故障排除

### 如果启动失败
1. 检查Python环境和依赖安装
2. 确认数据库文件权限
3. 查看日志文件：`logs/qm_YYYYMMDD.log`

### 如果数据获取失败
1. 检查网络连接
2. 验证AkShare接口状态
3. 查看错误日志获取详细信息

## 系统集成

债券监控系统已完全集成到您的量化交易系统中：
- 使用现有的数据库管理器
- 集成到现有的调度系统
- 使用统一的日志系统
- 遵循现有的配置管理

## 下一步

1. **测试运行**: 先手动执行几次监控，确认系统正常
2. **启动调度**: 启动自动调度器，让系统在14:30自动运行
3. **监控数据**: 定期查看历史数据，确认数据质量
4. **功能扩展**: 根据需要添加更多监控功能

## 联系支持

如有任何问题，请查看：
- `bond_monitor/README.md` - 详细使用说明
- `bond_monitor/IMPLEMENTATION_SUMMARY.md` - 技术实现总结
- 系统日志文件 - 运行状态和错误信息

---

🎉 **债券监控系统已准备就绪，开始监控中国债券市场！**
