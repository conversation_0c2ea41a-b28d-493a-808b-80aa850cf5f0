# 债券监控系统

专注于中国债券市场的监控系统，每个交易日14:30自动执行监控任务。

## 功能特性

- **监控范围**: 中短债、十年期长债、30年期长债、高等级信用债
- **监控频率**: 每个交易日14:30执行
- **数据维护**: 自动维护30天历史涨跌幅队列
- **数据源**: 使用AkShare接口获取中国债券市场数据

## 监控内容

### 债券类别
1. **中短债**: 1-3年期国债、政策性金融债
2. **十年期长债**: 10年期国债和企业债
3. **30年期长债**: 30年期国债和长期债券
4. **高等级信用债**: AAA级企业债、公司债、中期票据

### 监控指标
- 当日收益率变化（基点）
- 各类别债券涨跌幅对比
- 市场整体趋势分析
- 30天历史涨跌幅队列

## 使用方法

### 启动调度器
```bash
cd workspace/qm
python3 start_bond_monitor.py start
```

### 手动执行监控
```bash
python3 start_bond_monitor.py monitor
```

### 查看系统状态
```bash
python3 start_bond_monitor.py status
```

### 查看历史数据
```bash
# 查看最近7天数据
python3 start_bond_monitor.py history

# 查看最近30天数据
python3 start_bond_monitor.py history --days 30
```

## 系统架构

### 核心模块
- `bond_monitor_scheduler.py`: 债券监控调度器
- `bond_data_fetcher.py`: 债券数据获取器
- `bond_database.py`: 债券数据库管理器
- `bond_monitor.py`: 债券监控核心逻辑

### 数据库表结构
- `bond_monitor_data`: 债券监控数据表
- `bond_change_history`: 债券历史涨跌幅表（30天队列）
- `bond_monitor_logs`: 债券监控日志表

### 配置文件
- `config.yaml`: 债券监控配置
- `config/database.yaml`: 数据库配置（已集成）

## 监控时间安排

- **监控时间**: 每个交易日14:30
- **数据清理**: 每日凌晨2:00清理30天前的数据
- **交易日判断**: 周一至周五（不包含节假日）

## 输出示例

### 监控结果
```
=== 债券监控结果 ===
监控日期: 2024-06-28
监控时间: 14:30:15
市场总结: 债券收益率整体上行。中短债上涨5.2bp; 十年期长债上涨8.1bp; 30年期长债上涨6.7bp; 高等级信用债上涨4.3bp。

各类债券涨跌幅:
  中短债: 上涨 5.2 bp
  十年期长债: 上涨 8.1 bp
  30年期长债: 上涨 6.7 bp
  高等级信用债: 上涨 4.3 bp
==================
```

### 历史数据
```
日期         中短债    十年期    30年期    信用债    市场总结
--------------------------------------------------------------------------------
2024-06-28    5.2bp    8.1bp    6.7bp    4.3bp   债券收益率整体上行...
2024-06-27   -2.1bp   -3.5bp   -1.8bp   -2.7bp   债券收益率整体下行...
2024-06-26    0.5bp    1.2bp    0.8bp    0.9bp   债券收益率整体平稳...
```

## 注意事项

1. **网络连接**: 需要稳定的网络连接以获取AkShare数据
2. **交易日判断**: 目前仅基于工作日判断，未包含节假日逻辑
3. **数据准确性**: 建议定期检查数据源的可用性
4. **存储空间**: 系统会自动清理30天前的数据

## 故障排除

### 常见问题
1. **数据获取失败**: 检查网络连接和AkShare接口状态
2. **数据库错误**: 检查数据库文件权限和磁盘空间
3. **调度器未启动**: 检查系统时间和调度器配置

### 日志查看
系统日志保存在 `logs/` 目录下，可以通过日志文件查看详细的运行信息和错误信息。

## 扩展功能

系统设计支持以下扩展：
- 添加更多债券类别监控
- 集成更多数据源
- 添加预警功能
- 生成监控报告
- Web界面展示

## 技术依赖

- Python 3.7+
- AkShare >= 1.17.0
- pandas >= 1.5.0
- sqlite3 (内置)
- schedule >= 1.2.0
