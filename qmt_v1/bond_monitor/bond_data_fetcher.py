"""
债券数据获取器

使用AkShare接口获取中国债券市场数据
专注于中短债、十年期长债、30年期长债、高等级信用债的监控
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
import sys
import os

# 设置简单日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BondDataFetcher:
    """债券数据获取器"""
    
    def __init__(self):
        """初始化债券数据获取器"""
        self.request_interval = 1.0  # 请求间隔，避免频繁调用
        
    def _safe_request(self, func, *args, **kwargs) -> Optional[pd.DataFrame]:
        """安全的API请求，包含重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                time.sleep(self.request_interval)  # 请求间隔
                result = func(*args, **kwargs)
                logger.info(f"成功获取数据，形状: {result.shape}")
                return result
            except Exception as e:
                logger.warning(f"第{attempt + 1}次请求失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    logger.error(f"所有重试失败: {e}")
                    return None
    
    def get_china_bond_yield_curve(self, days_back: int = 7) -> Optional[pd.DataFrame]:
        """
        获取中国债券收益率曲线数据
        
        Args:
            days_back: 获取过去几天的数据
            
        Returns:
            包含收益率曲线数据的DataFrame
        """
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')
            
            logger.info(f"获取债券收益率曲线数据: {start_date} 到 {end_date}")
            
            # 使用AkShare获取国债收益率曲线
            df = self._safe_request(ak.bond_china_yield, start_date=start_date, end_date=end_date)
            
            if df is not None and not df.empty:
                logger.info(f"成功获取收益率曲线数据: {df.shape}")
                return df
            else:
                logger.warning("收益率曲线数据为空")
                return None
                
        except Exception as e:
            logger.error(f"获取收益率曲线数据失败: {e}")
            return None
    
    def get_bond_spot_market_data(self) -> Optional[pd.DataFrame]:
        """
        获取现券市场成交行情数据
        
        Returns:
            包含现券市场数据的DataFrame
        """
        try:
            logger.info("获取现券市场成交行情数据")
            
            # 使用AkShare获取现券市场成交行情
            df = self._safe_request(ak.bond_spot_deal)
            
            if df is not None and not df.empty:
                logger.info(f"成功获取现券市场数据: {df.shape}")
                return df
            else:
                logger.warning("现券市场数据为空")
                return None
                
        except Exception as e:
            logger.error(f"获取现券市场数据失败: {e}")
            return None
    
    def get_china_treasury_rates(self, days_back: int = 30) -> Optional[pd.DataFrame]:
        """
        获取中国国债收益率历史数据
        
        Args:
            days_back: 获取过去几天的数据
            
        Returns:
            包含国债收益率历史数据的DataFrame
        """
        try:
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')
            
            logger.info(f"获取中国国债收益率历史数据，起始日期: {start_date}")
            
            # 使用AkShare获取中美国债收益率数据（只关注中国部分）
            df = self._safe_request(ak.bond_zh_us_rate, start_date=start_date)
            
            if df is not None and not df.empty:
                # 只保留中国国债相关列
                china_cols = ['日期'] + [col for col in df.columns if '中国国债收益率' in col]
                df_china = df[china_cols].copy()
                logger.info(f"成功获取中国国债收益率数据: {df_china.shape}")
                return df_china
            else:
                logger.warning("中国国债收益率数据为空")
                return None
                
        except Exception as e:
            logger.error(f"获取中国国债收益率数据失败: {e}")
            return None
    
    def extract_bond_categories(self, spot_data: pd.DataFrame) -> Dict[str, List[str]]:
        """
        从现券市场数据中提取不同类别的债券
        
        Args:
            spot_data: 现券市场数据
            
        Returns:
            按类别分组的债券字典
        """
        categories = {
            'short_medium_term': [],  # 中短债
            'ten_year': [],           # 十年期
            'thirty_year': [],        # 30年期
            'high_grade_credit': []   # 高等级信用债
        }
        
        if spot_data is None or spot_data.empty:
            return categories
        
        try:
            # 假设债券简称列存在
            bond_names = spot_data.get('债券简称', pd.Series()).tolist()
            
            for bond_name in bond_names:
                if pd.isna(bond_name):
                    continue
                    
                bond_name_str = str(bond_name)
                
                # 识别中短债（1-3年期）
                if any(term in bond_name_str for term in ['1年', '2年', '3年', '短期', '中期']):
                    categories['short_medium_term'].append(bond_name_str)
                
                # 识别十年期债券
                if '10年' in bond_name_str or '十年' in bond_name_str:
                    categories['ten_year'].append(bond_name_str)
                
                # 识别30年期债券
                if '30年' in bond_name_str or '三十年' in bond_name_str:
                    categories['thirty_year'].append(bond_name_str)
                
                # 识别高等级信用债（AAA级）
                if any(grade in bond_name_str for grade in ['AAA', 'AA+', '高等级']):
                    categories['high_grade_credit'].append(bond_name_str)
            
            # 记录分类结果
            for category, bonds in categories.items():
                logger.info(f"{category}: 找到 {len(bonds)} 只债券")
            
            return categories
            
        except Exception as e:
            logger.error(f"债券分类失败: {e}")
            return categories
    
    def calculate_daily_changes(self, current_data: pd.DataFrame, 
                              historical_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算当日利率涨跌幅
        
        Args:
            current_data: 当前数据
            historical_data: 历史数据
            
        Returns:
            各类别债券的涨跌幅字典
        """
        changes = {
            'short_medium_term_change': 0.0,
            'ten_year_change': 0.0,
            'thirty_year_change': 0.0,
            'high_grade_credit_change': 0.0
        }
        
        try:
            # 这里需要根据实际数据结构来实现具体的计算逻辑
            # 暂时返回模拟数据，实际实现时会根据测试结果调整
            logger.info("计算当日利率涨跌幅")
            
            # TODO: 根据实际数据结构实现涨跌幅计算
            
            return changes
            
        except Exception as e:
            logger.error(f"计算涨跌幅失败: {e}")
            return changes
    
    def get_comprehensive_bond_data(self) -> Dict:
        """
        获取综合债券数据
        
        Returns:
            包含所有债券数据的字典
        """
        logger.info("开始获取综合债券数据")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'yield_curve': None,
            'spot_market': None,
            'treasury_rates': None,
            'bond_categories': {},
            'daily_changes': {},
            'success': False
        }
        
        try:
            # 获取收益率曲线数据
            result['yield_curve'] = self.get_china_bond_yield_curve()
            
            # 获取现券市场数据
            result['spot_market'] = self.get_bond_spot_market_data()
            
            # 获取国债收益率历史数据
            result['treasury_rates'] = self.get_china_treasury_rates()
            
            # 提取债券分类
            if result['spot_market'] is not None:
                result['bond_categories'] = self.extract_bond_categories(result['spot_market'])
            
            # 计算涨跌幅
            if result['spot_market'] is not None and result['treasury_rates'] is not None:
                result['daily_changes'] = self.calculate_daily_changes(
                    result['spot_market'], result['treasury_rates']
                )
            
            # 检查是否成功获取到数据
            result['success'] = any([
                result['yield_curve'] is not None,
                result['spot_market'] is not None,
                result['treasury_rates'] is not None
            ])
            
            logger.info(f"综合债券数据获取完成，成功: {result['success']}")
            return result
            
        except Exception as e:
            logger.error(f"获取综合债券数据失败: {e}")
            result['error'] = str(e)
            return result
