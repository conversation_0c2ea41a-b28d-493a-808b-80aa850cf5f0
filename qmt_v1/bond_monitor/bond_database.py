"""
债券数据库管理器

管理债券监控数据的存储和查询
维护30天的历史涨跌幅队列
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workspace', 'qm'))

from data.database import DatabaseManager
from utils.logger import setup_logger

logger = setup_logger()


class BondDatabaseManager:
    """债券数据库管理器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化债券数据库管理器"""
        self.db_manager = DatabaseManager(config_path)
        self._init_bond_tables()
    
    def _init_bond_tables(self):
        """初始化债券相关数据表"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建债券监控数据表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_monitor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    bond_category TEXT NOT NULL,
                    yield_rate REAL,
                    daily_change REAL,
                    data_source TEXT,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, bond_category)
                )
                """)
                
                # 创建债券历史涨跌幅表（30天队列）
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_change_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    short_medium_term_change REAL DEFAULT 0.0,
                    ten_year_change REAL DEFAULT 0.0,
                    thirty_year_change REAL DEFAULT 0.0,
                    high_grade_credit_change REAL DEFAULT 0.0,
                    market_summary TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date)
                )
                """)
                
                # 创建债券监控日志表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_monitor_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    monitor_time TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    data_count INTEGER DEFAULT 0,
                    error_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                conn.commit()
                logger.info("债券数据表初始化完成")
                
        except Exception as e:
            logger.error(f"初始化债券数据表失败: {e}")
            raise
    
    def save_bond_monitor_data(self, date: str, bond_data: Dict) -> bool:
        """
        保存债券监控数据
        
        Args:
            date: 日期 (YYYY-MM-DD)
            bond_data: 债券数据字典
            
        Returns:
            是否保存成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 保存各类别债券数据
                categories = ['short_medium_term', 'ten_year', 'thirty_year', 'high_grade_credit']
                
                for category in categories:
                    # 从bond_data中提取相应数据
                    yield_rate = bond_data.get('daily_changes', {}).get(f'{category}_yield', 0.0)
                    daily_change = bond_data.get('daily_changes', {}).get(f'{category}_change', 0.0)
                    
                    cursor.execute("""
                    INSERT OR REPLACE INTO bond_monitor_data 
                    (date, bond_category, yield_rate, daily_change, data_source, raw_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        date,
                        category,
                        yield_rate,
                        daily_change,
                        'akshare',
                        json.dumps(bond_data.get('bond_categories', {}).get(category, []))
                    ))
                
                conn.commit()
                logger.info(f"保存债券监控数据成功: {date}")
                return True
                
        except Exception as e:
            logger.error(f"保存债券监控数据失败: {e}")
            return False
    
    def save_daily_changes(self, date: str, changes: Dict[str, float], 
                          market_summary: str = "") -> bool:
        """
        保存当日涨跌幅数据
        
        Args:
            date: 日期 (YYYY-MM-DD)
            changes: 涨跌幅数据字典
            market_summary: 市场总结
            
        Returns:
            是否保存成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT OR REPLACE INTO bond_change_history 
                (date, short_medium_term_change, ten_year_change, 
                 thirty_year_change, high_grade_credit_change, market_summary)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    date,
                    changes.get('short_medium_term_change', 0.0),
                    changes.get('ten_year_change', 0.0),
                    changes.get('thirty_year_change', 0.0),
                    changes.get('high_grade_credit_change', 0.0),
                    market_summary
                ))
                
                conn.commit()
                logger.info(f"保存当日涨跌幅数据成功: {date}")
                return True
                
        except Exception as e:
            logger.error(f"保存当日涨跌幅数据失败: {e}")
            return False
    
    def get_30day_change_history(self) -> Optional[pd.DataFrame]:
        """
        获取30天的涨跌幅历史数据
        
        Returns:
            包含30天历史数据的DataFrame
        """
        try:
            # 计算30天前的日期
            thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            with self.db_manager.get_connection() as conn:
                query = """
                SELECT * FROM bond_change_history 
                WHERE date >= ? 
                ORDER BY date DESC
                LIMIT 30
                """
                
                df = pd.read_sql_query(query, conn, params=(thirty_days_ago,))
                
                if not df.empty:
                    logger.info(f"获取30天历史数据成功: {len(df)} 条记录")
                    return df
                else:
                    logger.warning("30天历史数据为空")
                    return None
                    
        except Exception as e:
            logger.error(f"获取30天历史数据失败: {e}")
            return None
    
    def cleanup_old_data(self, keep_days: int = 30):
        """
        清理超过指定天数的旧数据
        
        Args:
            keep_days: 保留天数
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=keep_days)).strftime('%Y-%m-%d')
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 清理旧的监控数据
                cursor.execute("DELETE FROM bond_monitor_data WHERE date < ?", (cutoff_date,))
                deleted_monitor = cursor.rowcount
                
                # 清理旧的涨跌幅历史数据
                cursor.execute("DELETE FROM bond_change_history WHERE date < ?", (cutoff_date,))
                deleted_history = cursor.rowcount
                
                # 清理旧的日志数据
                cursor.execute("DELETE FROM bond_monitor_logs WHERE date < ?", (cutoff_date,))
                deleted_logs = cursor.rowcount
                
                conn.commit()
                
                logger.info(f"数据清理完成: 监控数据 {deleted_monitor} 条, "
                          f"历史数据 {deleted_history} 条, 日志 {deleted_logs} 条")
                
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
    
    def log_monitor_activity(self, date: str, monitor_time: str, status: str, 
                           message: str = "", data_count: int = 0, 
                           error_info: str = "") -> bool:
        """
        记录监控活动日志
        
        Args:
            date: 日期
            monitor_time: 监控时间
            status: 状态 (success/error/warning)
            message: 消息
            data_count: 数据条数
            error_info: 错误信息
            
        Returns:
            是否记录成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO bond_monitor_logs 
                (date, monitor_time, status, message, data_count, error_info)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (date, monitor_time, status, message, data_count, error_info))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"记录监控日志失败: {e}")
            return False
    
    def get_latest_bond_data(self, category: str = None) -> Optional[pd.DataFrame]:
        """
        获取最新的债券数据
        
        Args:
            category: 债券类别，None表示获取所有类别
            
        Returns:
            最新债券数据的DataFrame
        """
        try:
            with self.db_manager.get_connection() as conn:
                if category:
                    query = """
                    SELECT * FROM bond_monitor_data 
                    WHERE bond_category = ? 
                    ORDER BY date DESC, created_at DESC 
                    LIMIT 10
                    """
                    df = pd.read_sql_query(query, conn, params=(category,))
                else:
                    query = """
                    SELECT * FROM bond_monitor_data 
                    ORDER BY date DESC, created_at DESC 
                    LIMIT 50
                    """
                    df = pd.read_sql_query(query, conn)
                
                if not df.empty:
                    logger.info(f"获取最新债券数据成功: {len(df)} 条记录")
                    return df
                else:
                    logger.warning("最新债券数据为空")
                    return None
                    
        except Exception as e:
            logger.error(f"获取最新债券数据失败: {e}")
            return None
    
    def get_monitor_summary(self, days: int = 7) -> Dict:
        """
        获取监控摘要信息
        
        Args:
            days: 查询天数
            
        Returns:
            监控摘要字典
        """
        try:
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            with self.db_manager.get_connection() as conn:
                # 获取监控日志统计
                log_query = """
                SELECT status, COUNT(*) as count 
                FROM bond_monitor_logs 
                WHERE date >= ? 
                GROUP BY status
                """
                log_df = pd.read_sql_query(log_query, conn, params=(start_date,))
                
                # 获取数据统计
                data_query = """
                SELECT bond_category, COUNT(*) as count, 
                       AVG(daily_change) as avg_change
                FROM bond_monitor_data 
                WHERE date >= ? 
                GROUP BY bond_category
                """
                data_df = pd.read_sql_query(data_query, conn, params=(start_date,))
                
                summary = {
                    'period': f"最近{days}天",
                    'log_summary': log_df.to_dict('records') if not log_df.empty else [],
                    'data_summary': data_df.to_dict('records') if not data_df.empty else [],
                    'generated_at': datetime.now().isoformat()
                }
                
                logger.info(f"生成监控摘要成功: {days}天")
                return summary
                
        except Exception as e:
            logger.error(f"生成监控摘要失败: {e}")
            return {}
