"""
债券监控核心模块

每个交易日14:30执行债券市场监控
监控中短债、十年期长债、30年期长债、高等级信用债的当日利率涨跌幅
维护30天历史涨跌幅队列
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workspace', 'qm'))

from utils.logger import setup_logger
from bond_monitor.bond_data_fetcher import BondDataFetcher
from bond_monitor.bond_database import BondDatabaseManager

logger = setup_logger()


class BondMonitor:
    """债券监控器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化债券监控器"""
        self.data_fetcher = BondDataFetcher()
        self.db_manager = BondDatabaseManager(config_path)
        self.monitor_categories = [
            'short_medium_term',  # 中短债
            'ten_year',           # 十年期长债
            'thirty_year',        # 30年期长债
            'high_grade_credit'   # 高等级信用债
        ]
        
    def is_trading_day(self, date: datetime = None) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 要判断的日期，默认为今天
            
        Returns:
            是否为交易日
        """
        if date is None:
            date = datetime.now()
        
        # 简单判断：周一到周五为交易日
        # 实际应用中可以加入节假日判断
        weekday = date.weekday()
        is_weekday = weekday < 5  # 0-4 表示周一到周五
        
        logger.info(f"日期 {date.strftime('%Y-%m-%d')} ({'周一二三四五六日'[weekday]}) "
                   f"{'是' if is_weekday else '不是'}交易日")
        
        return is_weekday
    
    def calculate_yield_changes(self, current_data: Dict, 
                              historical_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算各类债券的收益率涨跌幅
        
        Args:
            current_data: 当前债券数据
            historical_data: 历史数据
            
        Returns:
            各类别债券的涨跌幅字典
        """
        changes = {
            'short_medium_term_change': 0.0,
            'ten_year_change': 0.0,
            'thirty_year_change': 0.0,
            'high_grade_credit_change': 0.0
        }
        
        try:
            # 获取昨日数据作为基准
            if historical_data is not None and not historical_data.empty:
                yesterday_data = historical_data.iloc[0] if len(historical_data) > 0 else None
                
                if yesterday_data is not None:
                    # 计算各类别的涨跌幅
                    for category in self.monitor_categories:
                        change_key = f'{category}_change'
                        
                        # 从当前数据中获取今日收益率（这里需要根据实际数据结构调整）
                        current_yield = self._extract_current_yield(current_data, category)
                        
                        # 从历史数据中获取昨日收益率
                        yesterday_yield = yesterday_data.get(change_key, 0.0)
                        
                        # 计算涨跌幅（基点）
                        if current_yield is not None and yesterday_yield != 0:
                            change_bp = (current_yield - yesterday_yield) * 100  # 转换为基点
                            changes[change_key] = round(change_bp, 2)
                        
                        logger.info(f"{category}: 当前收益率 {current_yield}, "
                                  f"昨日收益率 {yesterday_yield}, "
                                  f"涨跌幅 {changes[change_key]} bp")
            
            return changes
            
        except Exception as e:
            logger.error(f"计算收益率涨跌幅失败: {e}")
            return changes
    
    def _extract_current_yield(self, current_data: Dict, category: str) -> Optional[float]:
        """
        从当前数据中提取指定类别的收益率
        
        Args:
            current_data: 当前数据
            category: 债券类别
            
        Returns:
            收益率值
        """
        try:
            # 根据不同类别提取相应的收益率数据
            # 这里需要根据实际的AkShare数据结构来实现
            
            if category == 'short_medium_term':
                # 提取1-3年期收益率的平均值
                return self._get_average_yield_for_terms(current_data, ['1年', '2年', '3年'])
            
            elif category == 'ten_year':
                # 提取10年期收益率
                return self._get_yield_for_term(current_data, '10年')
            
            elif category == 'thirty_year':
                # 提取30年期收益率
                return self._get_yield_for_term(current_data, '30年')
            
            elif category == 'high_grade_credit':
                # 提取高等级信用债收益率（需要从现券市场数据中计算）
                return self._get_credit_bond_yield(current_data)
            
            return None
            
        except Exception as e:
            logger.error(f"提取{category}收益率失败: {e}")
            return None
    
    def _get_yield_for_term(self, data: Dict, term: str) -> Optional[float]:
        """获取指定期限的收益率"""
        try:
            yield_curve = data.get('yield_curve')
            if yield_curve is not None and not yield_curve.empty:
                # 查找包含指定期限的最新数据
                latest_data = yield_curve.iloc[-1] if len(yield_curve) > 0 else None
                if latest_data is not None and term in yield_curve.columns:
                    return float(latest_data[term])
            return None
        except Exception as e:
            logger.error(f"获取{term}收益率失败: {e}")
            return None
    
    def _get_average_yield_for_terms(self, data: Dict, terms: List[str]) -> Optional[float]:
        """获取多个期限收益率的平均值"""
        try:
            yields = []
            for term in terms:
                yield_val = self._get_yield_for_term(data, term)
                if yield_val is not None:
                    yields.append(yield_val)
            
            if yields:
                return sum(yields) / len(yields)
            return None
        except Exception as e:
            logger.error(f"计算平均收益率失败: {e}")
            return None
    
    def _get_credit_bond_yield(self, data: Dict) -> Optional[float]:
        """获取高等级信用债收益率"""
        try:
            spot_market = data.get('spot_market')
            if spot_market is not None and not spot_market.empty:
                # 筛选高等级信用债
                high_grade_bonds = spot_market[
                    spot_market['债券简称'].str.contains('AAA|AA\\+', na=False, regex=True)
                ]
                
                if not high_grade_bonds.empty and '最新收益率' in high_grade_bonds.columns:
                    # 计算平均收益率
                    avg_yield = high_grade_bonds['最新收益率'].mean()
                    return float(avg_yield)
            
            return None
        except Exception as e:
            logger.error(f"获取信用债收益率失败: {e}")
            return None
    
    def generate_market_summary(self, changes: Dict[str, float]) -> str:
        """
        生成市场总结
        
        Args:
            changes: 涨跌幅数据
            
        Returns:
            市场总结文本
        """
        try:
            summary_parts = []
            
            # 分析各类别债券表现
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                if change > 0:
                    summary_parts.append(f"{name}上涨{change}bp")
                elif change < 0:
                    summary_parts.append(f"{name}下跌{abs(change)}bp")
                else:
                    summary_parts.append(f"{name}持平")
            
            # 整体市场判断
            total_change = sum(changes.values())
            if total_change > 5:
                market_trend = "债券收益率整体上行"
            elif total_change < -5:
                market_trend = "债券收益率整体下行"
            else:
                market_trend = "债券收益率整体平稳"
            
            summary = f"{market_trend}。{'; '.join(summary_parts)}。"
            
            logger.info(f"生成市场总结: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"生成市场总结失败: {e}")
            return "市场数据获取异常"
    
    def run_daily_monitor(self) -> Dict:
        """
        执行每日债券监控
        
        Returns:
            监控结果字典
        """
        monitor_time = datetime.now()
        date_str = monitor_time.strftime('%Y-%m-%d')
        time_str = monitor_time.strftime('%H:%M:%S')
        
        logger.info(f"开始执行债券监控: {date_str} {time_str}")
        
        result = {
            'date': date_str,
            'time': time_str,
            'success': False,
            'message': '',
            'data': {},
            'changes': {},
            'summary': ''
        }
        
        try:
            # 检查是否为交易日
            if not self.is_trading_day(monitor_time):
                result['message'] = '今日非交易日，跳过监控'
                logger.info(result['message'])
                return result
            
            # 获取当前债券数据
            logger.info("获取当前债券数据...")
            current_data = self.data_fetcher.get_comprehensive_bond_data()
            
            if not current_data.get('success', False):
                result['message'] = '获取债券数据失败'
                logger.error(result['message'])
                self.db_manager.log_monitor_activity(
                    date_str, time_str, 'error', result['message'], 0, 
                    current_data.get('error', '')
                )
                return result
            
            # 获取历史数据
            logger.info("获取历史数据...")
            historical_data = self.db_manager.get_30day_change_history()
            
            # 计算涨跌幅
            logger.info("计算当日涨跌幅...")
            changes = self.calculate_yield_changes(current_data, historical_data)
            
            # 生成市场总结
            market_summary = self.generate_market_summary(changes)
            
            # 保存数据
            logger.info("保存监控数据...")
            save_success = self.db_manager.save_bond_monitor_data(date_str, current_data)
            save_changes_success = self.db_manager.save_daily_changes(
                date_str, changes, market_summary
            )
            
            if save_success and save_changes_success:
                result['success'] = True
                result['message'] = '债券监控完成'
                result['data'] = current_data
                result['changes'] = changes
                result['summary'] = market_summary
                
                # 记录成功日志
                self.db_manager.log_monitor_activity(
                    date_str, time_str, 'success', result['message'], 
                    len(current_data.get('bond_categories', {}))
                )
                
                # 清理旧数据
                self.db_manager.cleanup_old_data(30)
                
                logger.info(f"债券监控完成: {market_summary}")
            else:
                result['message'] = '保存监控数据失败'
                logger.error(result['message'])
                self.db_manager.log_monitor_activity(
                    date_str, time_str, 'error', result['message']
                )
            
            return result
            
        except Exception as e:
            error_msg = f"债券监控执行失败: {e}"
            result['message'] = error_msg
            logger.error(error_msg)
            
            self.db_manager.log_monitor_activity(
                date_str, time_str, 'error', error_msg, 0, str(e)
            )
            
            return result
    
    def get_monitor_status(self) -> Dict:
        """
        获取监控状态
        
        Returns:
            监控状态字典
        """
        try:
            # 获取最近7天的监控摘要
            summary = self.db_manager.get_monitor_summary(7)
            
            # 获取30天历史数据
            history = self.db_manager.get_30day_change_history()
            
            status = {
                'last_update': datetime.now().isoformat(),
                'is_trading_day': self.is_trading_day(),
                'summary': summary,
                'history_count': len(history) if history is not None else 0,
                'categories': self.monitor_categories
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取监控状态失败: {e}")
            return {'error': str(e)}
