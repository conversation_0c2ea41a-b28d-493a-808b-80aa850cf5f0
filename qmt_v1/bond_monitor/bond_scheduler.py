"""
债券监控调度器

每个交易日14:30自动执行债券监控任务
集成到现有的调度系统中
"""

import schedule
import time
import threading
from datetime import datetime
from typing import Optional
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workspace', 'qm'))

from utils.logger import setup_logger
from bond_monitor.bond_monitor import BondMonitor

logger = setup_logger()


class BondScheduler:
    """债券监控调度器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化债券监控调度器"""
        self.bond_monitor = BondMonitor(config_path)
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.monitor_time = "14:30"  # 每个交易日14:30执行
        
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置债券监控定时任务...")
        
        # 清除现有任务
        schedule.clear()
        
        # 设置每个交易日14:30的监控任务
        schedule.every().monday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().tuesday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().wednesday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().thursday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().friday.at(self.monitor_time).do(self.run_bond_monitor_job)
        
        logger.info(f"已设置债券监控定时任务: 每个交易日 {self.monitor_time}")
        
        # 设置每日数据清理任务（凌晨2点）
        schedule.every().day.at("02:00").do(self.cleanup_old_data_job)
        
        logger.info("债券监控定时任务设置完成")
    
    def run_bond_monitor_job(self):
        """执行债券监控任务"""
        try:
            current_time = datetime.now()
            logger.info(f"开始执行债券监控任务: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 检查是否为交易日
            if not self.bond_monitor.is_trading_day(current_time):
                logger.info("今日非交易日，跳过债券监控任务")
                return
            
            # 执行债券监控
            result = self.bond_monitor.run_daily_monitor()
            
            if result['success']:
                logger.info(f"债券监控任务完成: {result['summary']}")
                
                # 输出监控结果
                self._log_monitor_result(result)
            else:
                logger.error(f"债券监控任务失败: {result['message']}")
            
        except Exception as e:
            logger.error(f"执行债券监控任务异常: {e}")
    
    def cleanup_old_data_job(self):
        """清理旧数据任务"""
        try:
            logger.info("开始执行数据清理任务")
            self.bond_monitor.db_manager.cleanup_old_data(30)
            logger.info("数据清理任务完成")
        except Exception as e:
            logger.error(f"数据清理任务失败: {e}")
    
    def _log_monitor_result(self, result: dict):
        """记录监控结果"""
        try:
            changes = result.get('changes', {})
            
            logger.info("=== 债券监控结果 ===")
            logger.info(f"监控日期: {result['date']}")
            logger.info(f"监控时间: {result['time']}")
            logger.info(f"市场总结: {result['summary']}")
            
            logger.info("各类债券涨跌幅:")
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                direction = "上涨" if change > 0 else "下跌" if change < 0 else "持平"
                logger.info(f"  {name}: {direction} {abs(change)} bp")
            
            logger.info("==================")
            
        except Exception as e:
            logger.error(f"记录监控结果失败: {e}")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("债券监控调度器已在运行中")
            return
        
        logger.info("启动债券监控调度器...")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("债券监控调度器启动成功")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("债券监控调度器未在运行")
            return
        
        logger.info("停止债券监控调度器...")
        
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("债券监控调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("债券监控调度器主循环开始...")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
        
        logger.info("债券监控调度器主循环结束")
    
    def run_manual_monitor(self):
        """手动执行一次债券监控"""
        logger.info("手动执行债券监控任务...")
        self.run_bond_monitor_job()
    
    def get_next_run_time(self) -> Optional[str]:
        """获取下次运行时间"""
        try:
            jobs = schedule.get_jobs()
            if jobs:
                next_run = min(job.next_run for job in jobs if job.next_run)
                return next_run.strftime('%Y-%m-%d %H:%M:%S')
            return None
        except Exception as e:
            logger.error(f"获取下次运行时间失败: {e}")
            return None
    
    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        try:
            status = {
                'is_running': self.is_running,
                'monitor_time': self.monitor_time,
                'next_run_time': self.get_next_run_time(),
                'scheduled_jobs': len(schedule.get_jobs()),
                'bond_monitor_status': self.bond_monitor.get_monitor_status()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取调度器状态失败: {e}")
            return {'error': str(e)}


def main():
    """主函数，用于独立运行债券监控调度器"""
    logger.info("启动债券监控调度器...")
    
    scheduler = BondScheduler()
    
    try:
        # 启动调度器
        scheduler.start()
        
        logger.info("债券监控调度器运行中，按 Ctrl+C 停止...")
        
        # 保持程序运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭债券监控调度器...")
        scheduler.stop()
        logger.info("债券监控调度器已关闭")
    
    except Exception as e:
        logger.error(f"债券监控调度器运行异常: {e}")
        scheduler.stop()


if __name__ == "__main__":
    main()
