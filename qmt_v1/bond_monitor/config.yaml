# 债券监控配置文件

# 监控设置
monitor:
  # 监控时间（每个交易日）
  schedule_time: "14:30"
  
  # 监控的债券类别
  categories:
    - short_medium_term  # 中短债（1-3年期）
    - ten_year          # 十年期长债
    - thirty_year       # 30年期长债
    - high_grade_credit # 高等级信用债（AAA级）
  
  # 历史数据保留天数
  history_days: 30
  
  # 数据清理设置
  cleanup:
    # 是否启用自动清理
    enabled: true
    # 清理时间
    schedule_time: "02:00"
    # 保留天数
    keep_days: 30

# 数据源配置
data_source:
  # AkShare配置
  akshare:
    # 请求间隔（秒）
    request_interval: 1.0
    # 重试次数
    max_retries: 3
    # 超时时间（秒）
    timeout: 30
    
  # 数据获取配置
  fetch:
    # 收益率曲线历史天数
    yield_curve_days: 7
    # 国债收益率历史天数
    treasury_rate_days: 30

# 债券分类规则
classification:
  # 中短债识别关键词
  short_medium_term:
    keywords: ["1年", "2年", "3年", "短期", "中期"]
    max_maturity: 3  # 最大期限（年）
  
  # 十年期债券识别关键词
  ten_year:
    keywords: ["10年", "十年"]
    target_maturity: 10
  
  # 30年期债券识别关键词
  thirty_year:
    keywords: ["30年", "三十年"]
    target_maturity: 30
  
  # 高等级信用债识别规则
  high_grade_credit:
    rating_keywords: ["AAA", "AA+"]
    bond_types: ["企业债", "公司债", "中期票据", "短期融资券"]

# 预警设置
alerts:
  # 收益率变动预警阈值（基点）
  yield_change_threshold:
    warning: 10   # 警告阈值
    critical: 20  # 严重阈值
  
  # 是否启用预警
  enabled: true
  
  # 预警类型
  types:
    - yield_spike      # 收益率异常波动
    - spread_widening  # 利差扩大
    - volume_anomaly   # 成交量异常

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 是否记录详细的API调用日志
  log_api_calls: true
  
  # 是否记录数据变化日志
  log_data_changes: true

# 数据库配置
database:
  # 使用现有的数据库配置文件
  config_file: "config/database.yaml"
  
  # 债券监控专用表前缀
  table_prefix: "bond_"

# 报告配置
reporting:
  # 是否生成日报
  daily_report: true
  
  # 报告格式
  format: "text"  # text, json, html
  
  # 报告内容
  include:
    - market_summary    # 市场总结
    - category_changes  # 各类别变化
    - historical_trend  # 历史趋势
    - alert_summary     # 预警摘要
