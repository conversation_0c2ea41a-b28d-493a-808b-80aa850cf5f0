"""
债券监控主程序

提供债券监控的命令行接口和Web API接口
支持手动执行监控、查看状态、启动调度器等功能
"""

import argparse
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workspace', 'qm'))

from utils.logger import setup_logger
from bond_monitor.bond_monitor import BondMonitor
from bond_monitor.bond_scheduler import BondScheduler
from bond_monitor.bond_database import BondDatabaseManager

logger = setup_logger()


class BondMonitorCLI:
    """债券监控命令行接口"""
    
    def __init__(self):
        """初始化CLI"""
        self.bond_monitor = BondMonitor()
        self.bond_scheduler = BondScheduler()
        self.db_manager = BondDatabaseManager()
    
    def run_monitor(self):
        """执行一次债券监控"""
        print("开始执行债券监控...")
        print("=" * 50)
        
        result = self.bond_monitor.run_daily_monitor()
        
        if result['success']:
            print(f"✓ 监控完成: {result['date']} {result['time']}")
            print(f"市场总结: {result['summary']}")
            print()
            print("各类债券涨跌幅:")
            
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            changes = result.get('changes', {})
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                direction = "↑" if change > 0 else "↓" if change < 0 else "→"
                print(f"  {name}: {direction} {abs(change)} bp")
        else:
            print(f"✗ 监控失败: {result['message']}")
        
        print("=" * 50)
    
    def show_status(self):
        """显示监控状态"""
        print("债券监控状态")
        print("=" * 50)
        
        # 获取监控状态
        status = self.bond_monitor.get_monitor_status()
        
        print(f"最后更新: {status.get('last_update', 'N/A')}")
        print(f"今日是否交易日: {'是' if status.get('is_trading_day', False) else '否'}")
        print(f"历史数据条数: {status.get('history_count', 0)}")
        print()
        
        # 获取调度器状态
        scheduler_status = self.bond_scheduler.get_scheduler_status()
        print(f"调度器运行状态: {'运行中' if scheduler_status.get('is_running', False) else '已停止'}")
        print(f"监控时间: {scheduler_status.get('monitor_time', 'N/A')}")
        print(f"下次运行时间: {scheduler_status.get('next_run_time', 'N/A')}")
        print()
        
        # 显示最近7天监控摘要
        summary = status.get('summary', {})
        if summary:
            print("最近7天监控摘要:")
            log_summary = summary.get('log_summary', [])
            for log in log_summary:
                print(f"  {log.get('status', 'unknown')}: {log.get('count', 0)} 次")
        
        print("=" * 50)
    
    def show_history(self, days: int = 7):
        """显示历史数据"""
        print(f"最近{days}天债券涨跌幅历史")
        print("=" * 80)
        
        # 获取历史数据
        history = self.db_manager.get_30day_change_history()
        
        if history is not None and not history.empty:
            # 只显示最近指定天数的数据
            recent_history = history.head(days)
            
            print(f"{'日期':<12} {'中短债':<8} {'十年期':<8} {'30年期':<8} {'信用债':<8} {'市场总结'}")
            print("-" * 80)
            
            for _, row in recent_history.iterrows():
                date = row.get('date', 'N/A')
                short_change = row.get('short_medium_term_change', 0.0)
                ten_change = row.get('ten_year_change', 0.0)
                thirty_change = row.get('thirty_year_change', 0.0)
                credit_change = row.get('high_grade_credit_change', 0.0)
                summary = row.get('market_summary', '')[:20] + '...' if len(row.get('market_summary', '')) > 20 else row.get('market_summary', '')
                
                print(f"{date:<12} {short_change:>6.1f}bp {ten_change:>6.1f}bp {thirty_change:>6.1f}bp {credit_change:>6.1f}bp {summary}")
        else:
            print("暂无历史数据")
        
        print("=" * 80)
    
    def start_scheduler(self):
        """启动调度器"""
        print("启动债券监控调度器...")
        
        try:
            self.bond_scheduler.start()
            print("✓ 调度器启动成功")
            print(f"监控时间: 每个交易日 {self.bond_scheduler.monitor_time}")
            print("按 Ctrl+C 停止调度器")
            
            # 保持程序运行
            import time
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n正在停止调度器...")
            self.bond_scheduler.stop()
            print("✓ 调度器已停止")
        except Exception as e:
            print(f"✗ 调度器启动失败: {e}")
    
    def test_apis(self):
        """测试AkShare接口"""
        print("测试AkShare债券接口...")
        print("=" * 50)
        
        # 导入测试模块
        from bond_monitor.test_akshare_apis import main as test_main
        
        try:
            test_main()
        except Exception as e:
            print(f"接口测试失败: {e}")
        
        print("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='债券监控系统')
    
    parser.add_argument('command', choices=[
        'monitor', 'status', 'history', 'scheduler', 'test'
    ], help='执行的命令')
    
    parser.add_argument('--days', type=int, default=7, 
                       help='历史数据显示天数（默认7天）')
    
    args = parser.parse_args()
    
    cli = BondMonitorCLI()
    
    try:
        if args.command == 'monitor':
            cli.run_monitor()
        elif args.command == 'status':
            cli.show_status()
        elif args.command == 'history':
            cli.show_history(args.days)
        elif args.command == 'scheduler':
            cli.start_scheduler()
        elif args.command == 'test':
            cli.test_apis()
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"执行命令失败: {e}")
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
