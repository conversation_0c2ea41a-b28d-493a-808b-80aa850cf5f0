#!/usr/bin/env python3
"""
独立的债券监控系统启动文件

直接运行此文件即可启动债券监控系统
包含邮件转发功能，每日14:30自动监控中国债券市场

使用方法：
cd bond_monitor
python3 run_bond_monitor.py          # 启动监控系统
python3 run_bond_monitor.py test     # 测试运行
"""

import sys
import os
import time
import json
import smtplib
import sqlite3
import random
import threading
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from contextlib import contextmanager
import logging

# 尝试导入schedule，如果没有则提示安装
try:
    import schedule
except ImportError:
    print("错误: 缺少schedule模块")
    print("请运行: pip3 install schedule")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bond_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 邮件配置（从stock.py中获取）
EMAIL_CONFIG = {
    'to_email': '<EMAIL>',
    'from_email': '<EMAIL>',
    'smtp_server': 'smtp.163.com',
    'smtp_port': 465,
    'smtp_user': '<EMAIL>',
    'smtp_password': 'TTbthQhMd6vhizUn'
}

# 数据库文件路径
DATABASE_PATH = "../workspace/qm/data/quantitative_trading.db"
EMAIL_RECORD_FILE = "bond_email_records.json"


class StandaloneBondMonitor:
    """独立的债券监控系统"""
    
    def __init__(self):
        """初始化债券监控系统"""
        self.is_running = False
        self.scheduler_thread = None
        self._init_database()
        
    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            db_dir = os.path.dirname(DATABASE_PATH)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
            
            with sqlite3.connect(DATABASE_PATH) as conn:
                cursor = conn.cursor()
                
                # 创建债券历史涨跌幅表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_change_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    short_medium_term_change REAL DEFAULT 0.0,
                    ten_year_change REAL DEFAULT 0.0,
                    thirty_year_change REAL DEFAULT 0.0,
                    high_grade_credit_change REAL DEFAULT 0.0,
                    market_summary TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date)
                )
                """)
                
                # 创建债券监控日志表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_monitor_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    monitor_time TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    data_count INTEGER DEFAULT 0,
                    error_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                conn.commit()
                logger.info("债券数据库初始化完成")
                
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(DATABASE_PATH)
        try:
            yield conn
        finally:
            conn.close()
    
    def is_trading_day(self, date=None):
        """判断是否为交易日"""
        if date is None:
            date = datetime.now()
        
        # 简单判断：周一到周五为交易日
        weekday = date.weekday()
        is_weekday = weekday < 5  # 0-4 表示周一到周五
        
        logger.info(f"日期 {date.strftime('%Y-%m-%d')} ({'周一二三四五六日'[weekday]}) "
                   f"{'是' if is_weekday else '不是'}交易日")
        
        return is_weekday
    
    def get_bond_data_simulation(self):
        """模拟获取债券数据"""
        # 模拟债券收益率数据
        base_yields = {
            'short_medium_term': 2.5,  # 中短债基准收益率
            'ten_year': 3.2,           # 十年期基准收益率
            'thirty_year': 3.8,        # 30年期基准收益率
            'high_grade_credit': 3.5   # 高等级信用债基准收益率
        }
        
        # 模拟当日涨跌幅（基点）
        changes = {}
        for category, base_yield in base_yields.items():
            # 随机生成-20到+20基点的变化
            change = round(random.uniform(-20, 20), 2)
            changes[f'{category}_change'] = change
            changes[f'{category}_yield'] = base_yield + change / 100
        
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'yields': {k: v for k, v in changes.items() if '_yield' in k},
            'changes': {k: v for k, v in changes.items() if '_change' in k},
            'data_source': 'simulation'
        }
    
    def calculate_yield_changes(self, current_data):
        """计算各类债券的收益率涨跌幅"""
        try:
            # 从模拟数据中提取涨跌幅
            changes = current_data.get('changes', {})
            
            result = {
                'short_medium_term_change': changes.get('short_medium_term_change', 0.0),
                'ten_year_change': changes.get('ten_year_change', 0.0),
                'thirty_year_change': changes.get('thirty_year_change', 0.0),
                'high_grade_credit_change': changes.get('high_grade_credit_change', 0.0)
            }
            
            logger.info("计算得到的涨跌幅:")
            for category, change in result.items():
                logger.info(f"  {category}: {change} bp")
            
            return result
            
        except Exception as e:
            logger.error(f"计算收益率涨跌幅失败: {e}")
            return {
                'short_medium_term_change': 0.0,
                'ten_year_change': 0.0,
                'thirty_year_change': 0.0,
                'high_grade_credit_change': 0.0
            }
    
    def generate_market_summary(self, changes):
        """生成市场总结"""
        try:
            summary_parts = []
            
            # 分析各类别债券表现
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                if change > 0:
                    summary_parts.append(f"{name}上涨{change}bp")
                elif change < 0:
                    summary_parts.append(f"{name}下跌{abs(change)}bp")
                else:
                    summary_parts.append(f"{name}持平")
            
            # 整体市场判断
            total_change = sum(changes.values())
            if total_change > 5:
                market_trend = "债券收益率整体上行"
            elif total_change < -5:
                market_trend = "债券收益率整体下行"
            else:
                market_trend = "债券收益率整体平稳"
            
            summary = f"{market_trend}。{'; '.join(summary_parts)}。"
            
            logger.info(f"生成市场总结: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"生成市场总结失败: {e}")
            return "市场数据获取异常"
    
    def save_bond_data(self, date, changes, market_summary):
        """保存债券监控数据"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 保存当日涨跌幅数据
                cursor.execute("""
                INSERT OR REPLACE INTO bond_change_history 
                (date, short_medium_term_change, ten_year_change, 
                 thirty_year_change, high_grade_credit_change, market_summary)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    date,
                    changes.get('short_medium_term_change', 0.0),
                    changes.get('ten_year_change', 0.0),
                    changes.get('thirty_year_change', 0.0),
                    changes.get('high_grade_credit_change', 0.0),
                    market_summary
                ))
                
                conn.commit()
                logger.info(f"保存债券数据成功: {date}")
                return True
                
        except Exception as e:
            logger.error(f"保存债券数据失败: {e}")
            return False
    
    def cleanup_old_data(self, keep_days=30):
        """清理超过指定天数的旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=keep_days)).strftime('%Y-%m-%d')
            
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 清理旧的涨跌幅历史数据
                cursor.execute("DELETE FROM bond_change_history WHERE date < ?", (cutoff_date,))
                deleted_history = cursor.rowcount
                
                # 清理旧的日志数据
                cursor.execute("DELETE FROM bond_monitor_logs WHERE date < ?", (cutoff_date,))
                deleted_logs = cursor.rowcount
                
                conn.commit()
                
                logger.info(f"数据清理完成: 历史数据 {deleted_history} 条, 日志 {deleted_logs} 条")
                
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
    
    def log_monitor_activity(self, date, monitor_time, status, message="", data_count=0, error_info=""):
        """记录监控活动日志"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO bond_monitor_logs 
                (date, monitor_time, status, message, data_count, error_info)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (date, monitor_time, status, message, data_count, error_info))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"记录监控日志失败: {e}")
            return False

    def load_email_records(self):
        """加载邮件发送记录"""
        if os.path.exists(EMAIL_RECORD_FILE):
            try:
                with open(EMAIL_RECORD_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载邮件记录失败: {e}")
                return {}
        return {}

    def save_email_records(self, records):
        """保存邮件发送记录"""
        try:
            with open(EMAIL_RECORD_FILE, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存邮件记录失败: {e}")

    def should_send_daily_email(self, records):
        """判断是否应该发送每日邮件"""
        today = datetime.now().strftime('%Y-%m-%d')
        daily_key = 'daily_bond_report'

        # 如果今天没有发送过，返回True
        if daily_key not in records:
            return True

        # 如果记录日期不是今天，返回True
        if records[daily_key] != today:
            return True

        # 已经发送过今天的报告，返回False
        return False

    def update_daily_email_record(self, records):
        """更新每日邮件发送记录"""
        today = datetime.now().strftime('%Y-%m-%d')
        records['daily_bond_report'] = today
        return records

    def send_email(self, subject, body):
        """发送邮件"""
        try:
            msg = MIMEMultipart()
            msg['From'] = EMAIL_CONFIG['from_email']
            msg['To'] = EMAIL_CONFIG['to_email']
            msg['Subject'] = subject

            # 添加债券投资策略提示
            strategy_tips = """

债券投资策略提示：
- 收益率上行时，债券价格下跌，可考虑逢低买入优质债券
- 收益率下行时，债券价格上涨，可考虑适当获利了结
- 关注信用利差变化，高等级信用债相对安全
- 长期债券对利率变化更敏感，需谨慎操作
- 建议分散投资，不要集中持有单一期限债券

------
此邮件由债券监控系统自动发送，请勿直接回复。
"""

            body = body + strategy_tips
            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
            server.login(EMAIL_CONFIG['smtp_user'], EMAIL_CONFIG['smtp_password'])
            server.sendmail(EMAIL_CONFIG['from_email'], EMAIL_CONFIG['to_email'], msg.as_string())
            server.quit()

            logger.info("债券监控邮件发送成功")
            return True

        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
            return False

    def run_bond_monitor_force(self):
        """强制执行债券监控（测试用）"""
        try:
            # 强制执行监控，忽略交易日判断
            logger.info("强制执行债券监控（测试模式）...")

            # 获取债券数据
            bond_data = self.get_bond_data_simulation()

            if not bond_data.get('success', False):
                return {
                    'success': False,
                    'message': '获取债券数据失败',
                    'changes': {},
                    'summary': '数据获取异常'
                }

            # 计算涨跌幅
            changes = self.calculate_yield_changes(bond_data)

            # 生成市场总结
            market_summary = self.generate_market_summary(changes)

            # 保存数据
            date_str = datetime.now().strftime('%Y-%m-%d')
            save_success = self.save_bond_data(date_str, changes, market_summary)

            if save_success:
                return {
                    'success': True,
                    'message': '债券监控完成（测试模式）',
                    'changes': changes,
                    'summary': market_summary
                }
            else:
                return {
                    'success': False,
                    'message': '保存监控数据失败',
                    'changes': changes,
                    'summary': market_summary
                }

        except Exception as e:
            logger.error(f"强制执行债券监控失败: {e}")
            return {
                'success': False,
                'message': f'监控执行异常: {str(e)}',
                'changes': {},
                'summary': '系统异常'
            }

    def generate_bond_report_email(self, monitor_result):
        """生成债券监控报告邮件"""
        now = datetime.now()

        # 邮件主题
        if monitor_result.get('success', False):
            subject = f"【债券监控报告】{now.strftime('%Y-%m-%d')} 14:30 债券市场监控结果"
        else:
            subject = f"【债券监控异常】{now.strftime('%Y-%m-%d')} 14:30 债券监控失败"

        # 邮件正文
        if monitor_result.get('success', False):
            changes = monitor_result.get('changes', {})
            summary = monitor_result.get('summary', '无市场总结')

            body = f"""尊敬的用户，

今天是 {now.strftime('%Y年%m月%d日 %A')}，14:30债券市场监控完成。

=== 债券市场监控结果 ===

市场总结：{summary}

各类债券当日涨跌幅：
• 中短债（1-3年期）：{changes.get('short_medium_term_change', 0.0):.2f} bp
• 十年期长债：{changes.get('ten_year_change', 0.0):.2f} bp
• 30年期长债：{changes.get('thirty_year_change', 0.0):.2f} bp
• 高等级信用债：{changes.get('high_grade_credit_change', 0.0):.2f} bp

说明：bp = 基点，1个基点 = 0.01%

=== 市场分析 ===
"""

            # 添加市场分析
            total_change = sum(changes.values())
            if total_change > 10:
                body += "整体收益率大幅上行，债券价格下跌，可关注优质债券的买入机会。\n"
            elif total_change < -10:
                body += "整体收益率大幅下行，债券价格上涨，持有债券的投资者可考虑获利了结。\n"
            elif total_change > 5:
                body += "整体收益率温和上行，债券价格小幅下跌，市场情绪偏谨慎。\n"
            elif total_change < -5:
                body += "整体收益率温和下行，债券价格小幅上涨，市场情绪相对乐观。\n"
            else:
                body += "债券收益率整体平稳，市场波动较小，可继续观察。\n"

            # 添加各类别分析
            body += "\n各类别分析：\n"
            categories = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }

            for key, name in categories.items():
                change = changes.get(key, 0.0)
                if abs(change) > 5:
                    direction = "大幅上涨" if change > 0 else "大幅下跌"
                    body += f"• {name}：{direction} {abs(change):.2f}bp，需重点关注\n"
                elif abs(change) > 2:
                    direction = "上涨" if change > 0 else "下跌"
                    body += f"• {name}：{direction} {abs(change):.2f}bp，温和波动\n"
                else:
                    body += f"• {name}：基本持平，波动 {abs(change):.2f}bp\n"

        else:
            # 监控失败的邮件
            error_msg = monitor_result.get('message', '未知错误')
            body = f"""尊敬的用户，

今天是 {now.strftime('%Y年%m月%d日 %A')}，14:30债券市场监控执行失败。

错误信息：{error_msg}

请检查：
1. 网络连接是否正常
2. AkShare接口是否可用
3. 系统日志获取详细错误信息

建议手动执行一次监控进行排查。
"""

        return subject, body

    def run_bond_monitor(self):
        """执行债券监控"""
        try:
            # 检查是否为交易日
            if not self.is_trading_day():
                return {
                    'success': False,
                    'message': '今日非交易日，跳过监控',
                    'changes': {},
                    'summary': '今日非交易日'
                }

            # 执行监控
            logger.info("执行债券监控...")

            # 获取债券数据
            bond_data = self.get_bond_data_simulation()

            if not bond_data.get('success', False):
                return {
                    'success': False,
                    'message': '获取债券数据失败',
                    'changes': {},
                    'summary': '数据获取异常'
                }

            # 计算涨跌幅
            changes = self.calculate_yield_changes(bond_data)

            # 生成市场总结
            market_summary = self.generate_market_summary(changes)

            # 保存数据
            date_str = datetime.now().strftime('%Y-%m-%d')
            save_success = self.save_bond_data(date_str, changes, market_summary)

            if save_success:
                # 清理旧数据
                self.cleanup_old_data(30)

                return {
                    'success': True,
                    'message': '债券监控完成',
                    'changes': changes,
                    'summary': market_summary
                }
            else:
                return {
                    'success': False,
                    'message': '保存监控数据失败',
                    'changes': changes,
                    'summary': market_summary
                }

        except Exception as e:
            logger.error(f"执行债券监控失败: {e}")
            return {
                'success': False,
                'message': f'监控执行异常: {str(e)}',
                'changes': {},
                'summary': '系统异常'
            }

    def send_daily_bond_report(self):
        """发送每日债券监控报告"""
        try:
            # 检查邮件发送记录
            email_records = self.load_email_records()

            if not self.should_send_daily_email(email_records):
                logger.info("今日债券监控邮件已发送，跳过")
                return

            # 执行债券监控
            logger.info("开始执行债券监控并准备发送邮件...")
            monitor_result = self.run_bond_monitor()

            # 生成邮件内容
            subject, body = self.generate_bond_report_email(monitor_result)

            # 发送邮件
            if self.send_email(subject, body):
                # 更新邮件发送记录
                email_records = self.update_daily_email_record(email_records)
                self.save_email_records(email_records)
                logger.info("债券监控邮件发送完成")
            else:
                logger.error("债券监控邮件发送失败")

        except Exception as e:
            logger.error(f"发送每日债券报告失败: {e}")

    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置债券监控定时任务...")

        # 清除现有任务
        schedule.clear()

        # 设置每个交易日14:30的监控任务
        schedule.every().monday.at("14:30").do(self.send_daily_bond_report)
        schedule.every().tuesday.at("14:30").do(self.send_daily_bond_report)
        schedule.every().wednesday.at("14:30").do(self.send_daily_bond_report)
        schedule.every().thursday.at("14:30").do(self.send_daily_bond_report)
        schedule.every().friday.at("14:30").do(self.send_daily_bond_report)

        logger.info("已设置债券监控定时任务: 每个交易日 14:30")

        # 设置每日数据清理任务（凌晨2点）
        schedule.every().day.at("02:00").do(self.cleanup_old_data, 30)

        logger.info("债券监控定时任务设置完成")

    def start(self):
        """启动债券监控系统"""
        if self.is_running:
            logger.warning("债券监控系统已在运行中")
            return

        logger.info("启动债券监控系统...")

        # 设置定时任务
        self.setup_schedule()

        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info("债券监控系统启动成功")
        logger.info("监控时间: 每个交易日 14:30")
        logger.info("邮件接收: <EMAIL>")

    def stop(self):
        """停止债券监控系统"""
        if not self.is_running:
            logger.warning("债券监控系统未在运行")
            return

        logger.info("停止债券监控系统...")

        self.is_running = False
        schedule.clear()

        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)

        logger.info("债券监控系统已停止")

    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("债券监控调度器主循环开始...")

        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)

        logger.info("债券监控调度器主循环结束")

    def run_manual_test(self, force_test=False):
        """手动执行一次测试"""
        logger.info("手动执行债券监控测试...")
        print("=" * 60)
        print("债券监控系统测试")
        print("=" * 60)

        # 执行监控（测试模式强制执行）
        if force_test:
            result = self.run_bond_monitor_force()
        else:
            result = self.run_bond_monitor()

        if result['success']:
            print(f"✓ 监控成功: {result['summary']}")
            print("\n各类债券涨跌幅:")

            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }

            changes = result.get('changes', {})
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                direction = "↑" if change > 0 else "↓" if change < 0 else "→"
                print(f"  {name}: {direction} {abs(change):.2f} bp")

            # 发送测试邮件
            print("\n发送测试邮件...")
            subject, body = self.generate_bond_report_email(result)
            if self.send_email(f"[测试] {subject}", body):
                print("✓ 测试邮件发送成功")
            else:
                print("✗ 测试邮件发送失败")
        else:
            print(f"✗ 监控失败: {result['message']}")

        print("=" * 60)


def main():
    """主函数"""
    print("债券监控系统")
    print("=" * 60)
    print("功能: 每个交易日14:30监控中国债券市场")
    print("监控: 中短债、十年期长债、30年期长债、高等级信用债")
    print("邮件: 自动发送监控结果到 <EMAIL>")
    print("=" * 60)

    # 创建债券监控系统
    bond_monitor = StandaloneBondMonitor()

    try:
        # 检查命令行参数
        if len(sys.argv) > 1 and sys.argv[1] == 'test':
            # 测试模式
            bond_monitor.run_manual_test(force_test=True)
        else:
            # 正常运行模式
            print("启动债券监控系统...")
            print("按 Ctrl+C 停止系统")
            print()

            bond_monitor.start()

            # 保持程序运行
            while True:
                time.sleep(1)

    except KeyboardInterrupt:
        print("\n收到停止信号，正在关闭债券监控系统...")
        bond_monitor.stop()
        print("债券监控系统已关闭")

    except Exception as e:
        logger.error(f"债券监控系统运行异常: {e}")
        bond_monitor.stop()


if __name__ == "__main__":
    main()
