import akshare as ak
import pandas as pd
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
from datetime import datetime
import os
import json
import random

# 邮件发送记录文件
EMAIL_RECORD_FILE = "email_records.json"

# 添加日报发送记录字段
DAILY_REPORT_KEY = 'daily_report'

# 加载邮件发送记录
def load_email_records():
    if os.path.exists(EMAIL_RECORD_FILE):
        with open(EMAIL_RECORD_FILE, 'r') as f:
            return json.load(f)
    return {}

# 保存邮件发送记录
def save_email_records(records):
    with open(EMAIL_RECORD_FILE, 'w') as f:
        json.dump(records, f)

# 检查今天是否已经发送过邮件
def can_send_email(stock_code, records):
    today = datetime.now().strftime('%Y-%m-%d')
    
    # 如果记录不存在，返回True
    if stock_code not in records:
        return True
    
    # 如果记录日期不是今天，重置计数后返回True
    if isinstance(records[stock_code], str) or records[stock_code]['date'] != today:
        return True
    
    # 检查今天的发送次数
    if records[stock_code]['count'] >= 5:
        print(f"{stock_code} 今天已发送了 {records[stock_code]['count']} 次邮件，已达到上限")
        return False
    
    # 未达到5次限制，可以发送
    return True

# 更新邮件发送记录
def update_email_record(stock_code, records):
    today = datetime.now().strftime('%Y-%m-%d')
    
    # 如果记录不存在或者格式为旧版本（只有日期字符串）
    if stock_code not in records or isinstance(records[stock_code], str):
        records[stock_code] = {'date': today, 'count': 1}
    else:
        # 如果日期不是今天，重置计数
        if records[stock_code]['date'] != today:
            records[stock_code] = {'date': today, 'count': 1}
        else:
            # 增加今天的发送计数
            records[stock_code]['count'] += 1
    
    return records

# 获得每分钟频率的数据
def get_stock_price(stock_code):
    stock_zh_a_minute_df = ak.stock_zh_a_minute(symbol=stock_code, period='1')
    current_price = stock_zh_a_minute_df['low'].iloc[-1]
    return current_price

# 获取股票估值数据
def get_stock_valuation(stock_code):
    # 添加随机延时，避免请求过于频繁
    time.sleep(random.uniform(0.5, 1.5))
    
    # 获取 A 股个股估值数据
    stock_valuation_df = ak.stock_a_indicator_lg(symbol=stock_code)
    if stock_valuation_df is None or stock_valuation_df.empty:
        print(f"未找到股票代码 {stock_code} 的估值数据")
        return None
        
    financials = ak.stock_financial_analysis_indicator(symbol=stock_code)
    if financials is None or financials.empty:
        financials = pd.DataFrame()

    # 提取所需数据
    valuation_data = {
        '股票代码': stock_code,
        '市盈率': None,
        '市净率': None,
        '股息率': None,
        '总市值（亿）': None,
        '资产负债率': None
    }
    
    # 获取估值数据
    if 'pe_ttm' in stock_valuation_df.columns and len(stock_valuation_df['pe_ttm'].values) > 0:
        valuation_data['市盈率'] = float(stock_valuation_df['pe_ttm'].values[-1])
            
    if 'pb' in stock_valuation_df.columns and len(stock_valuation_df['pb'].values) > 0:
        valuation_data['市净率'] = float(stock_valuation_df['pb'].values[-1])
            
    if 'dv_ttm' in stock_valuation_df.columns and len(stock_valuation_df['dv_ttm'].values) > 0:
        valuation_data['股息率'] = float(stock_valuation_df['dv_ttm'].values[-1])
            
    if 'total_mv' in stock_valuation_df.columns and len(stock_valuation_df['total_mv'].values) > 0:
        valuation_data['总市值（亿）'] = float(stock_valuation_df['total_mv'].values[-1])/10000
            
    # 获取财务数据
    if not financials.empty and '资产负债率(%)' in financials.columns and len(financials['资产负债率(%)'].values) > 0:
        valuation_data['资产负债率'] = float(financials['资产负债率(%)'].values[-1])
        
    return valuation_data

# 添加标准策略文本
def get_strategy_tips():
    return """
- 加仓策略
  - 三天以上阴跌6%以上可以考虑加仓
- 抛售策略
  - 跌幅8%以上，考虑市场是否有实质性变化+安全边际；考虑割肉
  - 比我加仓的价格高3.5%左右以上就可以考虑抛掉一部分。
"""

def send_email(subject, body, to_email, from_email, smtp_server, smtp_port, smtp_user, smtp_password):
    msg = MIMEMultipart()
    msg['From'] = from_email
    msg['To'] = to_email
    msg['Subject'] = subject
    
    # 添加通用投资策略到邮件正文末尾
    body = body + get_strategy_tips()
    
    msg.attach(MIMEText(body, 'plain', 'utf-8'))
    
    server = smtplib.SMTP_SSL(smtp_server, smtp_port)
    server.login(smtp_user, smtp_password)
    server.sendmail(from_email, to_email, msg.as_string())
    server.quit()
    print("邮件发送成功")
    return True

def is_trading_day():
    """判断当前是否为交易日（工作日且非法定节假日）"""
    # 获取当前时间
    now = datetime.now()
    weekday = now.weekday()
    
    # 检查是否是周末 (5=周六, 6=周日)
    if weekday >= 5:
        print("当前是周末，股市休市")
        return False
    
    # 检查是否是法定节假日
    try:
        # 使用akshare获取当前日期是否为交易日
        today_str = now.strftime("%Y%m%d")
        trading_days = ak.tool_trade_date_hist_sina()
        
        if trading_days is None or trading_days.empty:
            print(f"无法获取交易日历数据，默认工作日为交易日")
            return weekday < 5
        
        if 'trade_date' in trading_days.columns:
            # 确保日期格式一致
            trading_days_list = trading_days['trade_date'].astype(str).tolist()
            
            # 调试信息
            print(f"当前日期: {today_str}, 数据类型: {type(today_str)}")
            print(f"获取到的最近交易日: {trading_days_list[:5]}, 数据类型: {type(trading_days_list[0]) if trading_days_list else 'N/A'}")
            
            # 检查当前日期是否在交易日列表中
            if today_str not in trading_days_list:
                # 输出更详细的调试信息
                try:
                    closest_dates = [d for d in trading_days_list if abs(int(d) - int(today_str)) < 5]
                    print(f"当前日期 {today_str} 不在交易日列表中。最接近的日期: {closest_dates}")
                except:
                    print("比较最近日期出错")
                
                print("API返回数据表明今天不是交易日，但将使用工作日判断作为备选")
                # 由于API可能不可靠，使用工作日判断作为备选
                return weekday < 5
            
            print(f"当前日期 {today_str} 是交易日")
            return True
        else:
            print(f"交易日历数据格式异常，未找到trade_date列，使用工作日判断")
            return weekday < 5
    except Exception as e:
        print(f"检查交易日期出错: {e}")
        # 如果API调用失败，只根据是否为工作日判断
        return weekday < 5

def is_trading_hours():
    """判断当前是否在交易时段内（9:30-11:30, 13:00-15:00）"""
    now = datetime.now()
    current_time = now.time()
    
    # 早上交易时间：9:30 - 11:30
    morning_start = datetime.strptime("9:30", "%H:%M").time()
    morning_end = datetime.strptime("11:30", "%H:%M").time()
    
    # 下午交易时间：13:00 - 15:00
    afternoon_start = datetime.strptime("13:00", "%H:%M").time()
    afternoon_end = datetime.strptime("15:00", "%H:%M").time()
    
    # 判断是否在交易时间内
    is_trading = (morning_start <= current_time <= morning_end) or \
                 (afternoon_start <= current_time <= afternoon_end)
    
    if not is_trading:
        print("当前时间不在交易时段内")
        
    return is_trading

# 判断是否在收盘前特殊时段（14:50-15:00）
def is_pre_closing_time():
    """判断当前是否在收盘前的特殊时段（14:50-15:00）"""
    now = datetime.now()
    current_time = now.time()
    
    # 收盘前特殊时段：14:50 - 15:00
    pre_close_start = datetime.strptime("14:50", "%H:%M").time()
    pre_close_end = datetime.strptime("15:00", "%H:%M").time()
    
    # 判断是否在收盘前特殊时段内
    is_pre_closing = pre_close_start <= current_time <= pre_close_end
    
    return is_pre_closing

# 用于跟踪每只股票的价格历史和连续上涨次数
def track_continuous_price_rise(stocks_list, price_history=None, rise_counts=None):
    """
    跟踪股票的价格历史和连续上涨次数
    
    参数:
        stocks_list: 股票列表（已购买和待买入的股票）
        price_history: 存储每只股票最近价格的字典
        rise_counts: 存储每只股票连续上涨次数的字典
    
    返回:
        price_history: 更新后的价格历史字典
        rise_counts: 更新后的连续上涨次数字典
        stocks_to_notify: 需要发送通知的股票列表
    """
    # 初始化数据结构
    if price_history is None:
        price_history = {}
    if rise_counts is None:
        rise_counts = {}
    
    stocks_to_notify = []
    
    # 合并已购买和待买入的股票列表
    all_stocks = stocks_list
    
    # 遍历所有股票
    for stock in all_stocks:
        code = stock['code']
        name = stock['name']
        
        # 获取当前价格
        current_price = get_stock_price(code)
        if current_price is None:
            print(f"无法获取 {name}({code}) 的当前价格，跳过连续上涨检测")
            continue
        
        current_price_float = float(current_price)
        
        # 如果是新股票，初始化历史记录
        if code not in price_history:
            price_history[code] = [current_price_float]
            rise_counts[code] = 0
            continue
        
        # 获取上一次价格
        last_price = price_history[code][-1]
        
        # 添加当前价格到历史记录
        price_history[code].append(current_price_float)
        
        # 只保留最近5次的价格记录
        if len(price_history[code]) > 5:
            price_history[code] = price_history[code][-5:]
        
        # 检查是否上涨
        if current_price_float > last_price:
            rise_counts[code] += 1
            print(f"{name}({code}) 价格从 {last_price:.2f} 上涨到 {current_price_float:.2f}，连续上涨次数: {rise_counts[code]}")
            
            # 如果连续上涨两次，添加到通知列表
            if rise_counts[code] >= 2:
                stocks_to_notify.append(stock)
        else:
            # 重置连续上涨计数
            rise_counts[code] = 0
            print(f"{name}({code}) 价格从 {last_price:.2f} 变为 {current_price_float:.2f}，连续上涨重置")
    
    return price_history, rise_counts, stocks_to_notify

# 发送收盘前连续上涨通知
def send_pre_closing_rise_notification(stocks, email_configs):
    """发送收盘前连续上涨通知"""
    if not stocks:
        return False
    
    now = datetime.now()
    subject = f"【收盘前股价连续上涨提醒】{now.strftime('%Y-%m-%d %H:%M')}"
    
    body = f"""尊敬的用户，

以下股票在收盘前（{now.strftime('%H:%M')}）出现连续上涨趋势，请关注：

"""
    
    for stock in stocks:
        name = stock['name']
        code = stock['code']
        
        # 获取当前价格
        current_price = get_stock_price(code)
        if current_price is None:
            continue
            
        current_price_float = float(current_price)
        
        # 获取股票估值数据（基本信息）
        valuation_info = get_stock_valuation(code[2:])
        if valuation_info is None:
            stock_info = "无法获取股票基本信息"
        else:
            stock_info = f"市盈率: {valuation_info.get('市盈率', 'N/A')}，股息率: {valuation_info.get('股息率', 'N/A')}%"
        
        body += f"- {name}({code}): 当前价格 {current_price_float:.2f}，{stock_info}\n"
    
    body += """
收盘前连续上涨可能预示着市场情绪积极，可考虑根据个股基本面决定是否适时获利了结。

------
此邮件由自动股票监控系统发送，请勿直接回复。
"""

    # 发送邮件
    send_email(subject, body, **email_configs)
    print(f"已发送收盘前连续上涨提醒邮件")
    return True

def get_next_check_interval():
    """获取下一次检查的时间间隔（秒）"""
    now = datetime.now()
    current_time = now.time()
    
    # 如果不是交易日，一小时检查一次
    if not is_trading_day():
        return 3600  # 1小时
        
    # 如果是交易日但还未到交易时间（上午9点前）
    market_open = datetime.strptime("9:00", "%H:%M").time()
    if current_time < market_open:
        # 计算到上午9点的秒数
        target_time = datetime.combine(now.date(), market_open)
        delta_seconds = (target_time - now).total_seconds()
        # 如果时间差小于5分钟，则返回300秒，否则返回计算出的秒数
        return max(300, min(delta_seconds, 3600))
    
    # 如果是收盘前特殊时段（14:50-15:00），每分钟检查一次
    if is_pre_closing_time():
        return 60  # 1分钟
    
    # 如果是交易日且已经过了收盘时间（下午3点后）
    market_close = datetime.strptime("15:00", "%H:%M").time()
    if current_time > market_close:
        return 3600  # 1小时
    
    # 交易时段内，每100秒检查一次
    return 100

def is_trading_time():
    """判断当前是否为交易时间（交易日+交易时段）"""
    # 这个函数保留用于兼容性，实际逻辑已经拆分到其他函数
    return is_trading_day() and is_trading_hours()

# 判断是否需要发送日报
def should_send_daily_report(records):
    today = datetime.now().strftime('%Y-%m-%d')
    
    # 如果今天没有发送过日报，返回True
    if DAILY_REPORT_KEY not in records:
        return True
        
    # 如果记录日期不是今天，返回True
    if records[DAILY_REPORT_KEY] != today:
        return True
        
    # 已经发送过今天的日报，返回False
    return False

# 更新日报发送记录
def update_daily_report_record(records):
    today = datetime.now().strftime('%Y-%m-%d')
    records[DAILY_REPORT_KEY] = today
    return records

# 生成日报内容并发送
def send_daily_report(purchased_stocks, watchlist_stocks, email_configs):
    now = datetime.now()
    is_trade_day = is_trading_day()
    
    # 生成日报标题
    if is_trade_day:
        subject = f"【股票监控日报】今天({now.strftime('%Y-%m-%d')})是交易日，以下是您监控的股票信息"
    else:
        subject = f"【股票监控日报】今天({now.strftime('%Y-%m-%d')})不是交易日，股市休市"
    
    # 生成日报内容
    body = f"""尊敬的用户，早上好！

今天是 {now.strftime('%Y年%m月%d日 %A')}
{"今天是交易日，股市开市" if is_trade_day else "今天不是交易日，股市休市"}

以下是您监控的所有股票最新价格信息：

"""
    
    # 添加已购买股票信息
    body += "【已购买股票】\n"
    for stock in purchased_stocks:
        name = stock['name']
        code = stock['code']
        buy_price = stock['buy_price']
        
        try:
            current_price = get_stock_price(code)
            if current_price is not None:
                current_price_float = float(current_price)
                price_change_percent = (current_price_float - buy_price) / buy_price * 100
                price_difference = current_price_float - buy_price
                
                body += f"- {name}({code}): 当前价格 {current_price_float:.2f}, 买入价 {buy_price:.2f}, "
                body += f"差额 {price_difference:.2f} ({price_change_percent:.2f}%)"
                
                # 添加涨幅超过5%的特别标记
                if price_change_percent >= 5:
                    body += f" 🔥【涨幅已超5%，可考虑获利了结】"
                    
                body += "\n"
            else:
                body += f"- {name}({code}): 无法获取当前价格, 买入价 {buy_price:.2f}\n"
        except Exception as e:
            body += f"- {name}({code}): 获取价格出错 ({str(e)}), 买入价 {buy_price:.2f}\n"
        
        # 添加短暂延时，避免请求过于频繁
        time.sleep(random.uniform(0.5, 1.0))
    
    # 添加待买入股票信息
    body += "\n【待买入股票】\n"
    for stock in watchlist_stocks:
        name = stock['name']
        code = stock['code']
        
        # 确定目标价格
        if stock['type'] == '目标价':
            target_price = stock['target_price']
        elif stock['type'] == '价格区间':
            target_price = stock['buy_price']
        else:
            target_price = stock.get('target_price')
        
        if target_price is None:
            continue
            
        try:
            current_price = get_stock_price(code)
            if current_price is not None:
                current_price_float = float(current_price)
                price_gap = current_price_float - target_price
                price_gap_percent = price_gap / target_price * 100
                
                status = "👉 已达到买入条件" if current_price_float <= target_price else f"距离买入条件还差 {price_gap:.2f} ({price_gap_percent:.2f}%)"
                
                # 获取自定义消息（如果有）
                custom_info = ""
                if stock.get('custom_message'):
                    custom_info = f"\n    💡 特别提示: {stock.get('custom_message')}"
                
                body += f"- {name}({code}): 当前价格 {current_price_float:.2f}, 目标价 {target_price:.2f}, {status}{custom_info}\n"
            else:
                body += f"- {name}({code}): 无法获取当前价格, 目标价 {target_price:.2f}\n"
        except Exception as e:
            body += f"- {name}({code}): 获取价格出错 ({str(e)}), 目标价 {target_price:.2f}\n"
        
        # 添加短暂延时，避免请求过于频繁
        time.sleep(random.uniform(0.5, 1.0))
    
    body += """
祝您投资顺利!!!
市场和人都容易因为暂时的消息而过激！！！ 类似自动控制原理。
保持冷静，保持稳健！！！
------
此邮件由自动股票监控系统发送，请勿直接回复。

"""

    # 发送邮件
    send_email(subject, body, **email_configs)
    print(f"已发送每日股票监控报告")
    return True

def main(test_mode=False):
    # 已购买股票信息：股票名称、代码、买入价格
    purchased_stocks = [
        {'name': '中远海控', 'code': 'sh601919', 'buy_price': 13.88, 'type': '范围', 'upper_limit': 15.8, 'lower_limit': 12},
        {'name': '纳斯达克100ETF', 'code': 'sh513390', 'buy_price': 1.552, 'type': '跌幅', 'threshold_percent': 6},
        {'name': '招商证券', 'code': 'sh600999', 'buy_price': 16.887, 'type': '跌幅', 'threshold_percent': 7}
    ]
    
    # 待买入股票信息：股票名称、代码、目标价格
    watchlist_stocks = [
        {'name': '申能股份', 'code': 'sh600642', 'target_price': 8.26, 'dividend_percent': 5.48, 'type': '目标价'},
        {'name': '郑煤机', 'code': 'sh601717', 'target_price': 13.8, 'dividend_percent': 8.2, 'type': '目标价'},
        {'name': '紫金矿业', 'code': 'sh601899', 'watch_price': 14, 'buy_price': 13.8, 'sell_price': 18, 'type': '价格区间'},
        {'name': '中国神华', 'code': 'sh601088', 'target_price': 37.3, 'dividend_percent': 5.9, 'type': '目标价', 'custom_message': '没有国家队护盘，神华价值回归在36左右，最好37开始建仓,38.3才5.9%'},
        {'name': '中国平安', 'code': 'sh601318', 'target_price': 48, 'type': '目标价', 'custom_message': '入手，或者48以下，44元今年股息才5.96%'},
        {'name': '中海油', 'code': 'sh600938', 'target_price': 24, 'type': '目标价', 'custom_message': '24以下开始建仓入手，油价跌都不怕，23块钱时5.6%'}
    ]
    
    # 邮件配置
    email_configs = {
        'to_email': '<EMAIL>',
        'from_email': '<EMAIL>',
        'smtp_server': 'smtp.163.com',
        'smtp_port': 465,
        'smtp_user': '<EMAIL>',
        'smtp_password': 'TTbthQhMd6vhizUn'
    }
    
    # 加载邮件发送记录
    email_records = load_email_records()
    
    # 初始化价格历史记录和连续上涨计数
    price_history = {}
    rise_counts = {}
    
    print("开始监控指定股票价格...")
    
    while True:
        current_time = datetime.now()
        print(f"\n当前时间: {current_time}")
        
        # 检查是否需要发送每日报告（上午9点左右）
        if current_time.hour == 9 and should_send_daily_report(email_records):
            print("准备发送每日股票监控报告...")
            if send_daily_report(purchased_stocks, watchlist_stocks, email_configs):
                # 更新日报发送记录
                email_records = update_daily_report_record(email_records)
                save_email_records(email_records)
                print("每日报告发送完成")
        
        # 检查是否是交易日
        trading_day = is_trading_day()
        
        # 只有在交易日的交易时段内才进行股票价格监控
        if trading_day and is_trading_hours():
            print("当前是交易时段，开始检查股票价格...")
            
            # 收盘前特殊时段（14:50-15:00）的连续上涨检测
            if is_pre_closing_time():
                print("当前处于收盘前特殊时段（14:50-15:00），检查连续上涨...")
                
                # 合并所有需要监控的股票
                all_stocks = purchased_stocks + watchlist_stocks
                
                # 跟踪价格连续上涨
                price_history, rise_counts, stocks_to_notify = track_continuous_price_rise(all_stocks, price_history, rise_counts)
                
                # 如果有需要通知的股票，发送邮件
                if stocks_to_notify:
                    print(f"发现 {len(stocks_to_notify)} 只股票连续上涨，准备发送提醒...")
                    send_pre_closing_rise_notification(stocks_to_notify, email_configs)
            
            # 监控已购买股票
            for stock in purchased_stocks:
                name = stock['name']
                code = stock['code']
                buy_price = stock['buy_price']
                stock_type = stock['type']
                
                # 检查是否达到邮件发送次数限制
                if not can_send_email(code, email_records):
                    print(f"{name}({code}) 今天邮件发送次数已达上限，跳过")
                    continue
                
                # 获取当前价格
                current_price = get_stock_price(code)
                if current_price is None:
                    print(f"无法获取 {name}({code}) 的当前价格，跳过")
                    continue
                
                # 转换价格为浮点数
                current_price_float = float(current_price)
                print(f"股票 {name}({code}) 当前价格: {current_price_float}, 买入价: {buy_price}")
                
                # 计算涨跌幅和价格差额
                price_change_percent = (current_price_float - buy_price) / buy_price * 100
                price_difference = current_price_float - buy_price
                
                # 获取股票估值数据（基本信息）
                valuation_info = get_stock_valuation(code[2:])
                if valuation_info is None:
                    stock_info_text = "无法获取股票基本信息"
                else:
                    stock_info_text = f"""
股票基本信息:
市盈率: {valuation_info.get('市盈率', 'N/A')}
市净率: {valuation_info.get('市净率', 'N/A')}
股息率: {valuation_info.get('股息率', 'N/A')}%
总市值: {valuation_info.get('总市值（亿）', 'N/A')}亿元
资产负债率: {valuation_info.get('资产负债率', 'N/A')}%
"""
                
                # 标准邮件内容
                standard_content = f"""
股票: {name}({code})
当前价格: {current_price_float}
买入价格: {buy_price}
价格差额: {price_difference:.2f}
相比买入价波动: {price_change_percent:.2f}%

{stock_info_text}
提醒：请参考iwencai.com的结果，价格较高时不要买入，除非财务信息非常量化。
"""
                
                # 根据不同股票类型进行判断
                should_send_email = False
                email_subject = ""
                email_body = ""
                
                # 检查是否涨幅超过5%
                if price_change_percent >= 5:
                    should_send_email = True
                    email_subject = f"股票 {name}({code}) 涨幅提醒"
                    email_body = f"""
{standard_content}
预设涨幅提醒阈值: 5%

建议：
股价已经超过买入价格5%，可以考虑获利了结一部分仓位。
请结合个股基本面和市场情况，做出最终决策。
"""
                # 跌幅类型（中国神华、纳斯达克100ETF）
                elif stock_type == '跌幅':
                    threshold_percent = stock['threshold_percent']
                    if price_change_percent <= -threshold_percent:
                        should_send_email = True
                        
                        if name == '中国神华':
                            suggestion = "已达到加仓条件，但神华加仓需等待Q1业绩明朗化，4-26号！之后再考虑加仓。"
                        elif name == '纳斯达克100ETF':
                            suggestion = "已达到预设跌幅条件，加仓策略如下：\n第一次跌6%加仓1/2，第二次跌36%1/2（有反弹就跑），第三次10%！"
                        else:
                            suggestion = "已达到预设跌幅条件，请关注。"
                            
                        email_subject = f"股票 {name}({code}) 跌幅提醒"
                        email_body = f"""
{standard_content}
预设跌幅提醒阈值: {threshold_percent}%

建议：
{suggestion}
"""
                
                # 范围类型（中远海控）
                elif stock_type == '范围':
                    upper_limit = stock['upper_limit']
                    lower_limit = stock['lower_limit']
                    
                    if current_price_float >= upper_limit:
                        should_send_email = True
                        email_subject = f"股票 {name}({code}) 价格超过上限提醒"
                        email_body = f"""
{standard_content}
上限价格: {upper_limit}

建议：
千万暂时不进行任何操作，中国的贸易量减少，预计利润下滑30%，股价在12以下才足够安全！
目前预计在这个区间内15.8（6.6%）-12（8.75%）。
可以考虑适当减仓获利。
"""
                    elif current_price_float <= lower_limit:
                        should_send_email = True
                        email_subject = f"股票 {name}({code}) 价格低于下限提醒"
                        email_body = f"""
{standard_content}
下限价格: {lower_limit}

建议：
千万暂时不进行任何操作，中国的贸易量减少，预计利润下滑30%，股价在12以下才足够安全！
目前预计在这个区间内15.8（6.6%）-12（8.75%）。
已经达到相对安全价格，可以考虑适当加仓。
"""
                
                # 发送邮件
                if should_send_email:
                    if send_email(email_subject, email_body, **email_configs):
                        # 更新邮件发送记录
                        email_records = update_email_record(code, email_records)
                        print(f"已发送 {name} 价格提醒邮件")
                
                # 股票检查后添加短暂延时，避免请求过于频繁
                time.sleep(random.uniform(1, 2))
            
            # 监控待买入股票
            print("正在检查待买入股票的价格...")
            for stock in watchlist_stocks:
                name = stock['name']
                code = stock['code']
                
                # 确定目标价格
                if stock['type'] == '目标价':
                    target_price = stock['target_price']
                elif stock['type'] == '价格区间':
                    target_price = stock['buy_price']
                else:
                    target_price = stock.get('target_price')
                    
                # 如果没有目标价格，跳过
                if target_price is None:
                    print(f"无法确定 {name}({code}) 的目标价格，跳过")
                    continue
                
                # 检查是否达到邮件发送次数限制
                record_key = f"watchlist_{code}"
                if not can_send_email(record_key, email_records):
                    print(f"{name}({code}) 今天邮件发送次数已达上限，跳过")
                    continue
                
                # 获取当前价格
                current_price = get_stock_price(code)
                if current_price is None:
                    print(f"无法获取 {name}({code}) 的当前价格，跳过")
                    continue
                
                # 转换价格为浮点数
                current_price_float = float(current_price)
                print(f"待买入股票 {name}({code}) 当前价格: {current_price_float}, 目标价: {target_price}")
                
                # 获取股票估值数据（基本信息）
                valuation_info = get_stock_valuation(code[2:])
                if valuation_info is None:
                    stock_info_text = "无法获取股票基本信息"
                else:
                    stock_info_text = f"""
股票基本信息:
市盈率: {valuation_info.get('市盈率', 'N/A')}
市净率: {valuation_info.get('市净率', 'N/A')}
股息率: {valuation_info.get('股息率', 'N/A')}%
总市值: {valuation_info.get('总市值（亿）', 'N/A')}亿元
资产负债率: {valuation_info.get('资产负债率', 'N/A')}%
"""
                
                # 只关注目标价格
                if current_price_float <= target_price:
                    email_subject = f"股票 {name}({code}) 已达到买入价格"
                    
                    # 检查是否有自定义消息
                    custom_suggestion = stock.get('custom_message', "当前价格已达到或低于设定的目标买入价格，可以考虑买入。")
                    
                    email_body = f"""
股票: {name}({code})
当前价格: {current_price_float}
目标买入价格: {target_price}

{stock_info_text}
建议：
{custom_suggestion}

提醒：请参考iwencai.com的结果再做最终决定，确保财务信息符合预期。
"""
                    # 发送邮件
                    if send_email(email_subject, email_body, **email_configs):
                        # 更新邮件发送记录
                        email_records = update_email_record(record_key, email_records)
                        print(f"已发送 {name} 价格提醒邮件")
                
                # 股票检查后添加短暂延时，避免请求过于频繁
                time.sleep(random.uniform(1, 2))
            
            # 保存邮件发送记录
            save_email_records(email_records)
        else:
            if trading_day:
                print("当前是交易日，但不在交易时段内")
            else:
                print("当前不是交易日")
        
        # 如果是测试模式，只运行一次就退出
        if test_mode:
            print("测试模式，完成一次检查后退出")
            break
        
        # 计算下一次检查的时间间隔
        next_interval = get_next_check_interval()
        
        if trading_day and is_trading_hours():
            print(f"当前在交易时段内，{next_interval}秒后再次检查")
        elif trading_day:
            # 显示当前时间和下一个交易时段的开始时间
            now_time = current_time.time()
            if now_time < datetime.strptime("9:30", "%H:%M").time():
                next_time = "9:30"
            elif now_time < datetime.strptime("13:00", "%H:%M").time():
                next_time = "13:00"
            else:
                next_time = "明天 9:30"
            print(f"当前是交易日但不在交易时段内，等待下一个交易时段（{next_time}）")
        else:
            print(f"当前不是交易日，将在1小时后再次检查")
            
        print(f"休眠 {next_interval} 秒后进行下一轮检查...")
        time.sleep(next_interval)

if __name__ == "__main__":
    # 添加命令行参数解析，方便测试
    import sys
    test_mode = False
    test_daily_report = False
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            test_mode = True
            print("启动测试模式，只运行一次检查")
        elif sys.argv[1] == "--test-daily":
            test_daily_report = True
            print("测试日报功能")
    
    if test_daily_report:
        # 已购买股票信息
        purchased_stocks = [
            {'name': '中远海控', 'code': 'sh601919', 'buy_price': 13.88, 'type': '范围', 'upper_limit': 15.8, 'lower_limit': 12},
            {'name': '纳斯达克100ETF', 'code': 'sh513390', 'buy_price': 1.552, 'type': '跌幅', 'threshold_percent': 6}
        ]
        
        # 待买入股票信息
        watchlist_stocks = [
            {'name': '申能股份', 'code': 'sh600642', 'target_price': 8.26, 'dividend_percent': 5.48, 'type': '目标价'},
            {'name': '郑煤机', 'code': 'sh601717', 'target_price': 13.5, 'dividend_percent': 8.2, 'type': '目标价'},
            {'name': '紫金矿业', 'code': 'sh601899', 'watch_price': 14, 'buy_price': 13.8, 'sell_price': 18, 'type': '价格区间'},
            {'name': '中国神华', 'code': 'sh601088', 'target_price': 38.4, 'dividend_percent': 5.9, 'type': '目标价', 'custom_message': '谨慎购买，5.9%的年化值得购入，股息率=22.6/神华股价，参考iwencai和煤炭行业情况做出决定。'}
        ]
        
        # 邮件配置
        email_configs = {
            'to_email': '<EMAIL>',
            'from_email': '<EMAIL>',
            'smtp_server': 'smtp.163.com',
            'smtp_port': 465,
            'smtp_user': '<EMAIL>',
            'smtp_password': 'TTbthQhMd6vhizUn'
        }
        
        send_daily_report(purchased_stocks, watchlist_stocks, email_configs)
    else:
        main(test_mode)
