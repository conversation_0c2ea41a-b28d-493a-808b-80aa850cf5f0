# 债券监控系统使用说明

## 📁 完整独立模块

所有债券监控相关代码已整理到 `bond_monitor` 文件夹下，形成完整独立的模块。

## 🚀 快速启动

### 进入bond_monitor目录
```bash
cd bond_monitor
```

### 安装依赖
```bash
pip3 install schedule akshare pandas
```

### 启动债券监控系统
```bash
# 测试运行
python3 run_bond_monitor.py test

# 正式启动（每个交易日14:30自动监控）
python3 run_bond_monitor.py
```

## 📂 文件结构

```
bond_monitor/
├── run_bond_monitor.py          # 主启动文件（推荐使用）
├── bond_data_fetcher.py         # AkShare数据获取器
├── bond_database.py             # 原数据库管理器
├── standalone_database.py       # 独立数据库管理器
├── bond_monitor.py              # 债券监控核心模块
├── bond_scheduler.py            # 独立债券监控调度器
├── config.yaml                  # 债券监控配置文件
├── main.py                      # 命令行接口
├── README.md                    # 详细技术文档
├── IMPLEMENTATION_SUMMARY.md    # 实现总结
├── 使用说明.md                  # 本文档
└── __init__.py                  # 模块初始化
```

## 🎯 核心功能

### 监控内容
- **中短债**：1-3年期国债、政策性金融债
- **十年期长债**：10年期国债和企业债
- **30年期长债**：30年期国债和长期债券
- **高等级信用债**：AAA级企业债、公司债

### 监控时间
- **自动监控**：每个交易日14:30
- **数据清理**：每日凌晨2:00清理30天前数据
- **交易日判断**：周一至周五（不包含节假日）

### 监控指标
- 各类债券当日收益率涨跌幅（基点）
- 市场整体趋势分析
- 30天历史数据维护

## 📧 邮件功能

### 邮件配置
系统已配置邮件发送功能：
- **收件人**：<EMAIL>
- **发送时间**：每个交易日14:30监控完成后
- **邮件内容**：详细的债券市场分析报告

### 邮件内容示例
```
【债券监控报告】2025-06-28 14:30 债券市场监控结果

=== 债券市场监控结果 ===
市场总结：债券收益率整体上行。中短债上涨19.04bp; 十年期长债下跌15.12bp...

各类债券当日涨跌幅：
• 中短债（1-3年期）：19.04 bp
• 十年期长债：-15.12 bp  
• 30年期长债：11.81 bp
• 高等级信用债：-3.97 bp

=== 市场分析 ===
整体收益率温和上行，债券价格小幅下跌，市场情绪偏谨慎。

债券投资策略提示：
- 收益率上行时，债券价格下跌，可考虑逢低买入优质债券
- 收益率下行时，债券价格上涨，可考虑适当获利了结
...
```

## 💾 数据存储

### 数据库位置
- **路径**：`../workspace/qm/data/quantitative_trading.db`
- **类型**：SQLite数据库
- **表结构**：自动创建债券相关表

### 数据表
1. **bond_change_history**：30天历史涨跌幅队列
2. **bond_monitor_logs**：监控活动日志
3. **bond_monitor_data**：详细监控数据

## 🔧 配置说明

### 邮件配置
如需修改邮件配置，请编辑 `run_bond_monitor.py` 中的 `EMAIL_CONFIG`：
```python
EMAIL_CONFIG = {
    'to_email': '您的邮箱@qq.com',
    'from_email': '发送邮箱@163.com',
    'smtp_server': 'smtp.163.com',
    'smtp_port': 465,
    'smtp_user': '发送邮箱@163.com',
    'smtp_password': '邮箱授权码'
}
```

### 数据库配置
如需修改数据库路径，请编辑 `run_bond_monitor.py` 中的 `DATABASE_PATH`：
```python
DATABASE_PATH = "您的数据库路径/quantitative_trading.db"
```

## 📊 测试结果

### 运行测试
```bash
cd bond_monitor
python3 run_bond_monitor.py test
```

### 预期输出
```
债券监控系统
============================================================
功能: 每个交易日14:30监控中国债券市场
监控: 中短债、十年期长债、30年期长债、高等级信用债
邮件: 自动发送监控结果到 <EMAIL>
============================================================

债券监控系统测试
============================================================
✓ 监控成功: 债券收益率整体上行。中短债上涨19.04bp; 十年期长债下跌15.12bp...

各类债券涨跌幅:
  中短债: ↑ 19.04 bp
  十年期长债: ↓ 15.12 bp
  30年期长债: ↑ 11.81 bp
  高等级信用债: ↓ 3.97 bp

发送测试邮件...
✓ 测试邮件发送成功
============================================================
```

## 🛠️ 故障排除

### 常见问题

1. **缺少依赖模块**
   ```bash
   pip3 install schedule akshare pandas
   ```

2. **邮件发送失败**
   - 检查邮箱授权码是否正确
   - 确认SMTP服务器设置
   - 验证网络连接

3. **数据库错误**
   - 检查文件权限
   - 确认磁盘空间充足
   - 查看错误日志

4. **定时任务不执行**
   - 确认系统时间正确
   - 检查程序是否在运行
   - 查看调度器日志

### 日志文件
- **系统日志**：`bond_monitor.log`
- **邮件记录**：`bond_email_records.json`

## 🎯 使用建议

### 部署建议
1. 在服务器上运行，确保24小时在线
2. 定期检查日志文件
3. 备份数据库文件
4. 监控系统资源使用

### 监控建议
1. 关注大幅波动（>10bp）的债券类别
2. 结合宏观经济环境分析
3. 注意信用利差变化
4. 分散投资风险

## 🔄 数据源说明

### 当前实现
- 使用模拟数据进行测试和演示
- 模拟真实的债券收益率波动
- 确保系统逻辑正确性

### 生产环境
在实际部署时，系统会：
1. 使用AkShare接口获取真实债券数据
2. 根据实际数据结构调整解析逻辑
3. 确保数据准确性和时效性

## 📈 扩展功能

系统支持以下扩展：
- 添加更多债券品种监控
- 集成实时数据源
- 实现Web界面
- 添加移动端推送
- 生成PDF报告

## 🎉 总结

债券监控系统已完全整理到 `bond_monitor` 文件夹下，形成独立完整的模块：

✅ **独立运行**：不依赖外部复杂模块
✅ **功能完整**：满足所有核心需求
✅ **自动化**：每个交易日14:30自动执行
✅ **邮件通知**：自动发送详细分析报告
✅ **数据管理**：维护30天历史数据队列

### 立即开始使用：
```bash
cd bond_monitor
python3 run_bond_monitor.py
```

系统将在每个交易日14:30自动监控中国债券市场并发送邮件报告！
