# 🕐 量化交易系统 (QMT - Quantitative Trading System)

基于机器学习的股票量化交易系统，包含量化因子工程、策略回测、时间感知预测和现代化Web界面。



## 🎯 核心功能

### 1. **三个独立预测模型**
- **XGBoost分类模型** - 预测未来10日拉升概率
- **XGBoost二分类模型** - 预测明日开盘涨跌方向
- **时序模型** - 预测明日价格（支持LSTM/Transformer）

### 2. **时间感知预测系统** (v2.1新增) 🕐
- **早盘预测（9:35）**：基于前一交易日数据的预测模型
- **尾盘预测（14:50）**：基于当日盘中数据的预测模型
- **智能交易日历**：自动跳过非交易日和节假日
- **灵活训练配置**：按交易日数量或日期范围配置训练数据
- **自动定时调度**：早盘和尾盘分别执行预测任务

### 3. **量化因子工程系统** (v2.0) 🔧
- **20+个专业量化因子**：动量、均值回归、成交量、波动率等
- **8大因子分类**：系统化的因子组织结构
- **高度可扩展**：支持动态注册新因子
- **因子分析**：重要性分析和组合优化

### 4. **完整策略回测引擎** (v2.0) 📊
- **多种回测指标**：年化收益率、最大回撤、夏普比率、索提诺比率
- **交易成本模拟**：手续费、滑点等真实交易成本
- **风险控制**：仓位管理和风险指标监控
- **回测历史管理**：保存和查询历史回测结果

### 5. **现代化Web界面** (v2.0) 🎨
- **深色主题设计**：参考fellou.ai的现代化风格
- **动态渐变背景**：20秒循环的渐变动画效果
- **玻璃拟态卡片**：半透明背景和模糊效果
- **响应式布局**：适配桌面、平板、手机等设备

## 🏗️ 项目架构

```
workspace/qm/
├── config/                          # 配置文件
│   ├── features.yaml               # 特征配置
│   ├── models.yaml                 # 模型配置
│   ├── database.yaml               # 数据库配置
│   └── time_aware_training.yaml    # 时间感知配置 (v2.1)
├── data/                           # 数据层
│   ├── data_source.py              # 数据源接口（adata/akshare等）
│   ├── database.py                 # SQLite数据库管理
│   ├── stock_data_manager.py       # A股数据管理器 (v2.0)
│   └── time_aware_data_manager.py  # 时间感知数据管理器 (v2.1)
├── features/                       # 特征工程
│   ├── base_features.py            # 基础特征（价格、成交量、收益率）
│   ├── technical_indicators.py     # 技术指标（SMA、MACD、RSI、布林带等）
│   ├── time_series_features.py     # 时序特征（滞后、滚动统计）
│   ├── quantitative_factors.py     # 量化因子（20+个专业因子）(v2.0)
│   └── feature_engine.py           # 特征引擎（统一管理，高扩展性）
├── models/                         # 模型层
│   ├── xgboost_classifier.py       # XGBoost分类模型（10日拉升概率）
│   ├── xgboost_binary.py           # XGBoost二分类模型（明日涨跌）
│   ├── time_series_model.py        # 时序模型（LSTM/Transformer）
│   ├── model_manager.py            # 模型管理器
│   └── time_aware_model_manager.py # 时间感知模型管理器 (v2.1)
├── backtest/                       # 回测引擎 (v2.0)
│   └── backtest_engine.py          # 策略回测（多种指标、成本模拟）
├── scheduler/                      # 调度器
│   ├── prediction_scheduler.py     # 基础预测调度
│   └── time_aware_scheduler.py     # 时间感知调度器 (v2.1)
├── web/                           # Web界面
│   ├── app.py                     # Flask应用（包含时间感知API v2.1）
│   └── templates/
│       ├── index.html             # 经典界面
│       └── index_modern.html      # 现代化界面 (v2.0)
├── utils/                         # 工具模块
│   ├── logger.py                  # 日志配置
│   └── helpers.py                 # 辅助函数（数据生成、验证等）
├── main.py                        # 主程序入口
├── start.sh                       # 启动脚本
├── test_time_aware_system.py      # 时间感知系统测试 (v2.1)
├── memory.py                      # 开发记录和系统文档
├── TIME_AWARE_SYSTEM_SUMMARY.md   # 时间感知系统总结 (v2.1)
└── README.md                      # 项目文档
```

## 🔧 环境配置

### 1. 创建Conda环境

```bash
conda create -n qmt python=3.9 -y
conda activate qmt
```

### 2. 安装依赖包

```bash
# 使用conda安装主要包
conda install pandas numpy scikit-learn pyyaml loguru xgboost -y

# 使用pip安装其他包
pip install adata joblib schedule flask flask-cors
```

## 🚀 快速开始

### 1. 基本功能测试

```bash
cd workspace/qm
python main.py --mode test
```

### 2. 训练模型

```bash
python main.py --mode train
```

### 3. 时间感知系统测试 (v2.1新增)

```bash
# 测试时间感知功能
python test_time_aware_system.py
```

### 4. 启动Web界面

```bash
# 启动现代化Web界面
python web/app.py

# 访问界面
# 现代化界面: http://127.0.0.1:5001
# 经典界面: http://127.0.0.1:5001/classic
```

### 3. 执行预测

```bash
python main.py --mode predict
```

### 4. 启动Web界面

```bash
python web/app.py
```

然后访问：http://127.0.0.1:5001

## 📊 模型说明

### 1. XGBoost模型1：10日拉升概率预测

- **目标**：预测未来10个交易日出现"明显拉升"的概率
- **定义**：日均涨幅 > 1% 且至少4个交易日单日涨幅 > 2%
- **输出**：概率值（0-1）

### 2. XGBoost模型2：明日开盘涨跌预测

- **目标**：预测明日开盘价相对今日收盘价的涨跌
- **定义**：上涨(1) vs 下跌(-1)
- **输出**：涨跌方向和概率

### 3. 时序模型：明日价格预测

- **目标**：预测明日开盘价、收盘价、最高价
- **算法**：LSTM/Transformer（需要TensorFlow/PyTorch）
- **输出**：三个价格预测值

## 📋 详细文件说明

### 🔧 配置文件 (config/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `features.yaml` | 特征配置 | 定义所有特征类型和参数，控制特征启用/禁用 |
| `models.yaml` | 模型配置 | 定义模型参数和训练配置 |
| `database.yaml` | 数据库配置 | 定义表结构和数据管理策略 |
| `time_aware_training.yaml` | 时间感知配置 | 时间感知训练和预测的详细配置 (v2.1) |

### 💾 数据层 (data/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `data_source.py` | 数据源接口 | 封装adata、akshare等数据源的统一接口 |
| `database.py` | 数据库管理 | SQLite数据库的创建、查询、更新等操作 |
| `stock_data_manager.py` | A股数据管理 | 专门的A股数据获取和管理 (v2.0) |
| `time_aware_data_manager.py` | 时间感知数据管理 | 按交易日分割数据，智能交易日历 (v2.1) |

### 🔍 特征工程 (features/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `base_features.py` | 基础特征 | 价格、成交量、收益率、波动率等基础特征 |
| `technical_indicators.py` | 技术指标 | SMA、MACD、RSI、布林带、KDJ、ATR、OBV等 |
| `time_series_features.py` | 时序特征 | 滞后特征、滚动统计、季节性特征、趋势特征 |
| `quantitative_factors.py` | 量化因子 | 20+个专业量化因子，支持动态注册 (v2.0) |
| `feature_engine.py` | 特征引擎 | 统一管理所有特征计算，高度可扩展 |

### 🤖 模型层 (models/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `xgboost_classifier.py` | XGBoost分类 | 预测未来10日拉升概率的分类模型 |
| `xgboost_binary.py` | XGBoost二分类 | 预测明日开盘涨跌方向的二分类模型 |
| `time_series_model.py` | 时序模型 | LSTM/Transformer价格预测模型 |
| `model_manager.py` | 模型管理器 | 统一管理所有模型的训练、预测、保存 |
| `time_aware_model_manager.py` | 时间感知模型管理 | 早盘和尾盘模型的分离管理 (v2.1) |

### 📊 回测引擎 (backtest/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `backtest_engine.py` | 策略回测 | 完整的回测引擎，支持多种指标和成本模拟 (v2.0) |

### ⏰ 调度器 (scheduler/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `prediction_scheduler.py` | 基础预测调度 | 定时执行预测任务的调度器 |
| `time_aware_scheduler.py` | 时间感知调度 | 早盘9:35和尾盘14:50的智能调度 (v2.1) |

### 🌐 Web界面 (web/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `app.py` | Flask应用 | Web服务器，包含所有API接口和时间感知API (v2.1) |
| `templates/index.html` | 经典界面 | 传统的Web界面模板 |
| `templates/index_modern.html` | 现代化界面 | 深色主题的现代化界面 (v2.0) |

### 🛠️ 工具模块 (utils/)

| 文件 | 作用 | 说明 |
|------|------|------|
| `logger.py` | 日志配置 | 统一的日志配置和管理 |
| `helpers.py` | 辅助函数 | 数据生成、验证、格式化等辅助功能 |

### 📝 主要文件

| 文件 | 作用 | 说明 |
|------|------|------|
| `main.py` | 主程序入口 | 支持test、train、predict、data等模式 |
| `start.sh` | 启动脚本 | 交互式菜单启动脚本 |
| `test_time_aware_system.py` | 时间感知测试 | 完整的时间感知系统测试脚本 (v2.1) |
| `memory.py` | 开发记录 | 详细的开发过程和系统文档 |
| `TIME_AWARE_SYSTEM_SUMMARY.md` | 时间感知总结 | v2.1版本的开发总结 |

## 🕐 时间感知功能详解 (v2.1新增)

### 数据配置方式

**按交易日数量配置**:
```python
config = {
    'train_days': 252,      # 前252个交易日用于训练（约1年）
    'backtest_days': 63,    # 接下来63个交易日用于回测（约1季度）
    'prediction_days': 20   # 最近20个交易日用于特征计算
}
```

**按日期范围配置**:
```python
config = {
    'train_days': ('2023-01-02', '2024-03-08'),  # 指定日期范围训练
    'backtest_days': 60,    # 训练结束后60个交易日回测
    'prediction_days': 20   # 预测特征计算天数
}
```

### 时间感知预测

**早盘预测（9:35）**:
- 基于前一交易日收盘数据
- 适合隔夜策略和开盘前决策
- 特征：隔夜跳空、前日强度、前日成交量比率

**尾盘预测（14:50）**:
- 基于当日盘中数据
- 适合日内策略和尾盘决策
- 特征：当日涨跌幅、当日振幅、当日成交量比率

## 🔧 特征工程

### 基础特征
- 价格特征：开盘价、收盘价、最高价、最低价
- 成交量特征：成交量、成交额
- 收益率特征：1日、3日、5日收益率
- 波动率特征：5日、10日、20日波动率

### 技术指标
- 移动平均线：SMA_5, SMA_10, SMA_20, SMA_60
- MACD：主线、信号线、柱状图
- RSI：6日、14日RSI
- 布林带：上轨、中轨、下轨、宽度

### 量化因子 (v2.0)
- **动量因子**：momentum_5d, momentum_10d, momentum_20d
- **均值回归因子**：bollinger_position, price_deviation
- **成交量因子**：volume_ratio, volume_price_trend
- **波动率因子**：volatility_ratio, atr_ratio
- **趋势因子**：trend_strength, ma_trend

### 时序特征
- 滞后特征：1期、3期、5期滞后
- 滚动统计：均值、标准差、最值
- 季节性特征：星期、月份、季度

## ⏰ 定时任务

系统支持定时预测任务：

- **9:35**：开盘后预测
- **14:50**：收盘前预测
- **8:00**：每日数据更新

## 🌐 Web界面功能

### 系统状态
- 模型训练状态
- 调度器运行状态
- 系统时间戳

### 模型管理
- 选择模型进行训练
- 查看训练结果和指标
- 模型状态监控

### 预测管理
- 手动执行预测
- 查看预测结果
- 预测历史记录

### 调度器管理
- 启动/停止定时任务
- 手动执行预测任务
- 查看任务状态

## 📝 使用说明

### 1. 数据获取

系统设计支持adata数据源，但为了测试目的，当前使用模拟数据：

```python
from utils.helpers import create_sample_data

# 生成测试数据
sample_data = create_sample_data(num_days=100)
```

### 2. 特征计算

```python
from features.feature_engine import FeatureEngine

fe = FeatureEngine()
features_data = fe.calculate_features(data, feature_types=['basic', 'technical'])
```

### 3. 模型训练

```python
from models.model_manager import ModelManager

manager = ModelManager()
result = manager.train_model('xgboost_next_day_direction', training_data)
```

### 4. 预测

```python
predictions = manager.predict('xgboost_next_day_direction', prediction_data)
```

## 🔍 配置说明

### 特征配置 (config/features.yaml)

可以通过配置文件启用/禁用特征：

```yaml
features:
  basic_features:
    - name: "close"
      enabled: true
  technical_indicators:
    sma:
      - name: "sma_5"
        window: 5
        enabled: true
```

### 模型配置 (config/models.yaml)

配置模型参数：

```yaml
models:
  xgboost_next_day_direction:
    parameters:
      n_estimators: 150
      max_depth: 5
      learning_rate: 0.05
```

## 📈 性能监控

系统提供多种评估指标：

- **分类模型**：准确率、精确率、召回率、F1分数、AUC
- **回归模型**：MSE、RMSE、MAE、R²、MAPE

## 🚨 注意事项

1. **数据量控制**：系统设计为轻量级，不存储大量历史数据
2. **测试环境**：当前使用模拟数据进行测试，实际使用需要配置adata
3. **依赖包**：时序模型需要TensorFlow或PyTorch，可选安装
4. **端口配置**：Web界面默认使用5001端口

## 🔧 故障排除

### 常见问题

1. **配置文件找不到**：确保在项目根目录运行程序
2. **端口被占用**：修改web/app.py中的端口号
3. **模型训练失败**：检查数据格式和特征计算
4. **Web界面无法访问**：确认Flask应用已启动

### 日志查看

系统日志保存在 `logs/` 目录下：

```bash
tail -f logs/qm_20250616.log
```

## 📞 技术支持

如有问题，请查看日志文件或联系开发团队。

## 📚 版本历史

### v2.1 - 时间感知版本 (2025-06-17)
- ✅ **时间感知预测系统**：早盘9:35和尾盘14:50分别预测
- ✅ **灵活数据配置**：按交易日数量或日期范围配置训练数据
- ✅ **智能交易日历**：自动跳过非交易日和节假日
- ✅ **时间感知调度器**：智能定时预测调度
- ✅ **完整Web API**：9个新增时间感知API接口

### v2.0 - 现代化界面版本 (2025-06-17)
- ✅ **量化因子工程**：20+个专业量化因子
- ✅ **策略回测引擎**：完整的回测系统
- ✅ **现代化Web界面**：深色主题，玻璃拟态设计
- ✅ **A股数据管理器**：专门的A股数据处理

### v1.0 - 基础版本 (2025-06-16)
- ✅ **三个预测模型**：XGBoost分类、二分类、时序模型
- ✅ **特征工程系统**：基础特征、技术指标、时序特征
- ✅ **Web管理界面**：模型训练、预测管理
- ✅ **定时调度系统**：9:35和14:50定时预测

