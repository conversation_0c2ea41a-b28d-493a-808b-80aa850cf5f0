# 🎨 量化交易系统 v2.0 (QMT - Quantitative Trading System)

基于机器学习的股票量化交易系统，集成量化因子工程、策略回测和现代化Web界面。

## ✨ v2.0 版本亮点

- **🔧 量化因子工程**：20+个专业量化因子，支持动态组合
- **📊 完整回测系统**：多种回测指标和可视化分析
- **🎨 现代化界面**：参考fellou.ai设计风格的深色主题界面
- **🤖 增强模型管理**：支持因子组合训练和性能对比
- **📱 响应式设计**：完美适配各种设备尺寸

## 🎯 系统特色

### 核心功能
- **三个独立预测模型**：10日拉升概率、明日涨跌方向、明日价格预测
- **量化因子工程**：6大类20+个专业量化因子（动量、均值回归、成交量、波动率、趋势、技术信号）
- **策略回测引擎**：完整的回测指标（年化收益率、最大回撤、夏普比率等）
- **高度可扩展特征工程**：基础特征、技术指标、时序特征，支持配置化管理
- **轻量级数据策略**：基于adata数据源，不存储大量历史数据
- **定时预测任务**：每日9:35和14:50自动执行预测
- **现代化Web界面**：深色主题、动态背景、玻璃拟态设计
- **模块化设计**：高度解耦，易于扩展和维护

### 技术架构
- **数据层**：adata数据源 + SQLite存储
- **特征层**：可扩展的特征工程 + 量化因子计算
- **模型层**：XGBoost + 时序模型 + 因子组合管理
- **回测层**：完整的策略回测引擎
- **界面层**：现代化Web界面 + RESTful API
- **调度层**：定时任务调度器

## 🚀 快速开始

### 环境准备
```bash
# 激活conda环境
conda activate qmt

# 进入项目目录
cd /Users/<USER>/Desktop/cursor_temp/qmt_v1/workspace/qm
```

### 启动方式

#### 方法1：完整演示（推荐）
```bash
python demo_modern_system.py
```

#### 方法2：现代化界面
```bash
python start_modern_ui.py
# 或
python launch.py  # 选择1
```

#### 方法3：传统方式
```bash
# 基础功能测试
python main.py --mode test

# 训练模型
python main.py --mode train

# 执行预测
python main.py --mode predict

# 启动Web界面
python web/app.py
```

### 访问界面
- **现代化界面**：http://127.0.0.1:5001
- **经典界面**：http://127.0.0.1:5001/classic

## 📁 项目结构详解

### 🗂️ 目录结构
```
workspace/qm/
├── 📁 config/                    # 配置文件目录
│   ├── features.yaml            # 特征配置 - 定义所有特征类型和参数
│   ├── models.yaml              # 模型配置 - 定义模型参数和训练配置
│   └── database.yaml            # 数据库配置 - 定义表结构和数据管理策略
├── 📁 data/                      # 数据层
│   ├── data_source.py           # adata数据源接口封装
│   ├── stock_data_manager.py    # A股数据管理器 (v2.0新增)
│   └── database.py              # SQLite数据库管理器
├── 📁 features/                  # 特征工程层
│   ├── base_features.py         # 基础特征计算 - 价格、成交量、收益率、波动率
│   ├── technical_indicators.py  # 技术指标计算 - SMA、MACD、RSI、布林带等
│   ├── time_series_features.py  # 时序特征计算 - 滞后、滚动统计、季节性
│   ├── quantitative_factors.py  # 量化因子计算器 (v2.0新增)
│   └── feature_engine.py        # 特征引擎 - 统一管理所有特征计算和预处理
├── 📁 models/                    # 模型层
│   ├── xgboost_classifier.py    # XGBoost分类模型 - 10日拉升概率预测
│   ├── xgboost_binary.py        # XGBoost二分类模型 - 明日涨跌预测
│   ├── time_series_model.py     # 时序模型 - 价格预测
│   └── model_manager.py         # 模型管理器 - 统一管理所有模型
├── 📁 backtest/                  # 回测层 (v2.0新增)
│   └── backtest_engine.py       # 策略回测引擎 - 完整回测系统
├── 📁 scheduler/                 # 调度层
│   └── prediction_scheduler.py  # 定时任务调度器 - 管理预测任务
├── 📁 web/                       # Web界面层
│   ├── app.py                   # Flask Web应用 - 增强API接口
│   └── templates/               # HTML模板
│       ├── index.html           # 经典界面
│       └── index_modern.html    # 现代化界面 (v2.0新增)
├── 📁 utils/                     # 工具层
│   ├── logger.py                # 日志配置
│   └── helpers.py               # 辅助函数 - 数据生成、验证、格式化等
├── 📁 docs/                      # 文档目录 (v2.0新增)
│   └── UI_Design_Guide.md       # 界面设计指南
├── 📄 main.py                    # 主程序入口 - 支持test、train、predict、data模式
├── 📄 start.sh                   # 启动脚本 - 交互式菜单
├── 📄 memory.py                  # 开发记录 - 详细的项目开发历史
├── 📄 requirements.txt           # 依赖包列表
└── 📄 启动脚本 (v2.0新增)
    ├── start_modern_ui.py        # 现代化界面启动脚本
    ├── demo_modern_system.py     # 完整系统演示脚本
    ├── launch.py                 # 统一启动器
    └── test_enhanced_system.py   # 增强系统测试脚本
```

### 🔧 核心文件详解

#### 数据层 (Data Layer)
- **`data_source.py`** - adata数据源接口封装，支持多数据源故障转移
- **`stock_data_manager.py`** - 专门的A股数据管理器，支持最近2年数据
- **`database.py`** - SQLite数据库管理器，轻量级数据存储

#### 特征工程层 (Feature Engineering Layer)
- **`base_features.py`** - 基础特征计算（价格、成交量、收益率、波动率）
- **`technical_indicators.py`** - 技术指标计算（SMA、MACD、RSI、布林带、KDJ等）
- **`time_series_features.py`** - 时序特征计算（滞后、滚动统计、季节性）
- **`quantitative_factors.py`** - 量化因子计算器，20+个专业量化因子
- **`feature_engine.py`** - 特征引擎，统一管理和高度可扩展

#### 模型层 (Model Layer)
- **`xgboost_classifier.py`** - XGBoost分类模型，预测10日拉升概率
- **`xgboost_binary.py`** - XGBoost二分类模型，预测明日涨跌方向
- **`time_series_model.py`** - 时序模型，预测明日价格
- **`model_manager.py`** - 模型管理器，支持因子组合训练

#### 回测层 (Backtest Layer)
- **`backtest_engine.py`** - 策略回测引擎，完整的回测指标和可视化

#### Web界面层 (Web Interface Layer)
- **`app.py`** - Flask Web应用，提供RESTful API接口
- **`index.html`** - 经典Web界面
- **`index_modern.html`** - 现代化Web界面，深色主题设计

#### 调度层 (Scheduler Layer)
- **`prediction_scheduler.py`** - 定时任务调度器，管理9:35和14:50预测任务

## 🔧 量化因子详解

### 因子分类 (6大类21个因子)

#### 1. 动量因子 (Momentum)
- `momentum_5d/10d/20d` - 5/10/20日价格动量
- `rsi_divergence` - RSI背离因子

#### 2. 均值回归因子 (Mean Reversion)
- `bollinger_position` - 布林带位置因子
- `price_deviation` - 价格偏离因子
- `rsi_oversold/overbought` - RSI超卖/超买因子

#### 3. 成交量因子 (Volume)
- `volume_ratio` - 成交量比率因子
- `volume_price_trend` - 量价趋势因子
- `obv_trend` - OBV趋势因子
- `volume_momentum` - 成交量动量因子

#### 4. 波动率因子 (Volatility)
- `volatility_ratio` - 波动率比率因子
- `atr_ratio` - ATR比率因子
- `volatility_breakout` - 波动率突破因子

#### 5. 趋势因子 (Trend)
- `trend_strength` - 趋势强度因子
- `ma_trend` - 均线趋势因子
- `macd_trend` - MACD趋势因子

#### 6. 技术信号因子 (Technical)
- `kdj_signal` - KDJ信号因子
- `macd_signal` - MACD信号因子
- `bollinger_signal` - 布林带信号因子

## 📊 回测指标说明

### 收益指标
- **总收益率** - 整个回测期间的总收益
- **年化收益率** - 按年计算的收益率
- **基准超额收益** - 相对于基准的超额收益

### 风险指标
- **最大回撤** - 从峰值到谷值的最大跌幅
- **波动率** - 收益率的标准差（年化）
- **下行波动率** - 负收益的波动率

### 风险调整收益指标
- **夏普比率** - (年化收益率 - 无风险利率) / 波动率
- **索提诺比率** - (年化收益率 - 无风险利率) / 下行波动率
- **卡尔玛比率** - 年化收益率 / 最大回撤
- **信息比率** - 超额收益 / 跟踪误差

### 交易指标
- **总交易次数** - 买入和卖出的总次数
- **胜率** - 盈利交易占总交易的比例
- **平均盈亏** - 每笔交易的平均盈亏
- **平均盈亏率** - 每笔交易的平均盈亏百分比

## 🎨 现代化界面特色

### 设计特点
- **深色主题** - 减少眼部疲劳，适合长时间使用
- **动态背景** - 20秒循环的渐变动画效果
- **玻璃拟态** - 半透明卡片设计，现代感十足
- **流畅动画** - 微交互动画，提升用户体验
- **响应式设计** - 完美适配各种设备尺寸
- **智能通知** - 右上角动态通知系统

### 技术实现
- **CSS Variables** - 统一的设计系统
- **CSS Grid + Flexbox** - 现代布局技术
- **Font Awesome 6.0** - 现代图标系统
- **Inter字体** - 现代化字体设计

## 🔧 开发环境

### 必需依赖
```bash
conda install pandas numpy scikit-learn xgboost pyyaml loguru -y
pip install adata joblib schedule flask flask-cors
```

### 可选依赖
```bash
pip install tensorflow  # LSTM模型
pip install torch       # Transformer模型
pip install talib       # 高级技术指标
```

## 📖 使用指南

### 基本操作流程
1. **选择量化因子** → 因子管理 → 选择分类 → 勾选因子 → 应用选择
2. **训练模型** → 模型管理 → 选择模型 → 开始训练
3. **运行回测** → 策略回测 → 配置参数 → 运行回测
4. **执行预测** → 预测管理 → 输入股票 → 执行预测
5. **管理调度** → 调度管理 → 启动/停止调度

### API接口
- `GET /api/status` - 获取系统状态
- `GET /api/factors/available` - 获取可用因子
- `POST /api/factors/set_combination` - 设置因子组合
- `POST /api/train` - 训练模型
- `POST /api/backtest/run` - 运行回测
- `POST /api/predict` - 执行预测

## 🔮 版本历史

### v1.0 (2025-06-16)
- ✅ 基础量化交易系统
- ✅ 三个预测模型
- ✅ 基础特征工程
- ✅ 简单Web界面

### v2.0 (2025-06-17)
- ✅ 量化因子工程系统
- ✅ 完整策略回测引擎
- ✅ 现代化Web界面
- ✅ 增强模型管理
- ✅ 响应式设计

## 📞 技术支持

如遇问题，请：
1. 查看 `memory.py` 了解详细开发记录
2. 运行 `python demo_modern_system.py` 进行完整测试
3. 检查conda环境是否正确激活
4. 确认在正确的工作目录下运行

---

**开发者**: Augment Agent  
**技术栈**: Python + Flask + XGBoost + adata + SQLite  
**设计参考**: fellou.ai  
**版本**: v2.0  
**更新日期**: 2025-06-17
