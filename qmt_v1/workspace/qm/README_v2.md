# 量化交易系统 v2.0

## 🚀 系统概述

基于机器学习的A股量化交易系统，支持量化因子工程、策略回测和实时预测。本版本在v1.0基础上新增了：

- **量化因子工程**: 20+个常用量化因子，支持动态组合
- **完整回测系统**: 多种回测指标和可视化
- **增强Web界面**: 因子管理、回测分析、模型监控
- **多数据源支持**: adata主要数据源，支持故障转移
- **高度可扩展**: 支持自定义因子和策略

## 📊 核心功能

### 1. 量化因子工程
- **动量因子**: 价格动量、RSI背离等
- **均值回归因子**: 布林带位置、价格偏离等  
- **成交量因子**: 量价关系、OBV趋势等
- **波动率因子**: 波动率比率、ATR等
- **趋势因子**: 趋势强度、均线趋势等
- **技术信号因子**: KDJ、MACD、布林带信号等

### 2. 策略回测
- **回测指标**: 年化收益率、最大回撤、夏普比率、索提诺比率、胜率等
- **交易成本**: 手续费、滑点模拟
- **风险控制**: 单只股票最大仓位限制
- **可视化**: 收益曲线、回撤分析

### 3. 机器学习模型
- **XGBoost分类**: 10日拉升概率预测
- **XGBoost二分类**: 明日开盘涨跌预测  
- **时序模型**: 明日价格预测
- **因子组合**: 支持动态因子选择和组合

### 4. 数据管理
- **数据源**: 基于adata的A股数据获取
- **存储**: SQLite数据库，支持最近2年数据
- **更新**: 自动数据更新和清理
- **质量**: 数据验证和异常处理

## 🏗️ 系统架构

```
qm/
├── data/                    # 数据层
│   ├── stock_data_manager.py    # A股数据管理器
│   ├── data_source.py           # adata数据源
│   └── database.py              # 数据库管理
├── features/                # 特征工程层
│   ├── quantitative_factors.py  # 量化因子计算器
│   ├── feature_engine.py        # 特征引擎
│   ├── base_features.py         # 基础特征
│   └── technical_indicators.py  # 技术指标
├── models/                  # 模型层
│   ├── model_manager.py         # 模型管理器（支持因子组合）
│   ├── xgboost_models.py        # XGBoost模型
│   └── time_series_models.py    # 时序模型
├── backtest/                # 回测层
│   └── backtest_engine.py       # 回测引擎
├── web/                     # Web界面层
│   ├── app.py                   # Flask应用（增强API）
│   └── templates/index.html     # Web界面（支持因子管理）
├── scheduler/               # 调度层
│   └── prediction_scheduler.py  # 预测调度器
└── config/                  # 配置层
    ├── features.yaml            # 特征配置（含量化因子）
    ├── models.yaml              # 模型配置
    └── database.yaml            # 数据库配置
```

## 🔧 安装和配置

### 1. 环境要求
```bash
# Python 3.7+
conda activate qmt

# 安装依赖
pip install adata pandas numpy scikit-learn xgboost
pip install flask loguru pyyaml sqlite3
```

### 2. 数据源配置
```bash
# 安装数据源（推荐）
python install_data_sources.py

# 或手动安装
pip install adata akshare baostock yfinance
```

### 3. 系统测试
```bash
# 运行完整测试
python test_enhanced_system.py

# 测试量化因子
python features/quantitative_factors.py

# 测试回测引擎
python backtest/backtest_engine.py
```

## 🌐 Web界面使用

### 1. 启动服务
```bash
cd workspace/qm
python web/app.py
```

### 2. 访问界面
打开浏览器访问: http://127.0.0.1:5001

### 3. 主要功能
- **系统状态**: 查看模型训练状态和系统运行情况
- **模型管理**: 选择模型、配置因子组合、训练模型
- **因子管理**: 浏览可用因子、选择因子组合、查看因子重要性
- **策略回测**: 配置回测参数、运行回测、查看回测结果
- **预测管理**: 执行预测、查看预测结果
- **调度器管理**: 启动/停止定时预测任务

## 📈 量化因子详解

### 动量因子 (Momentum)
- `momentum_5d/10d/20d`: 5/10/20日价格动量
- `rsi_divergence`: RSI背离因子

### 均值回归因子 (Mean Reversion)  
- `bollinger_position`: 布林带位置因子
- `price_deviation`: 价格偏离因子
- `rsi_oversold/overbought`: RSI超卖/超买因子

### 成交量因子 (Volume)
- `volume_ratio`: 成交量比率因子
- `volume_price_trend`: 量价趋势因子
- `obv_trend`: OBV趋势因子
- `volume_momentum`: 成交量动量因子

### 波动率因子 (Volatility)
- `volatility_ratio`: 波动率比率因子
- `atr_ratio`: ATR比率因子
- `volatility_breakout`: 波动率突破因子

### 趋势因子 (Trend)
- `trend_strength`: 趋势强度因子
- `ma_trend`: 均线趋势因子
- `macd_trend`: MACD趋势因子

### 技术信号因子 (Technical)
- `kdj_signal`: KDJ信号因子
- `macd_signal`: MACD信号因子
- `bollinger_signal`: 布林带信号因子

## 📊 回测指标说明

### 收益指标
- **总收益率**: 整个回测期间的总收益
- **年化收益率**: 按年计算的收益率
- **基准超额收益**: 相对于基准的超额收益

### 风险指标
- **最大回撤**: 从峰值到谷值的最大跌幅
- **波动率**: 收益率的标准差（年化）
- **下行波动率**: 负收益的波动率

### 风险调整收益指标
- **夏普比率**: (年化收益率 - 无风险利率) / 波动率
- **索提诺比率**: (年化收益率 - 无风险利率) / 下行波动率
- **卡尔玛比率**: 年化收益率 / 最大回撤
- **信息比率**: 超额收益 / 跟踪误差

### 交易指标
- **总交易次数**: 买入和卖出的总次数
- **胜率**: 盈利交易占总交易的比例
- **平均盈亏**: 每笔交易的平均盈亏
- **平均盈亏率**: 每笔交易的平均盈亏百分比

## 🔄 因子组合管理

### 1. 预设组合
- **默认组合**: 平衡的因子组合
- **动量策略**: 专注动量和趋势
- **均值回归策略**: 专注均值回归
- **技术分析策略**: 基于技术指标
- **成交量策略**: 专注成交量分析

### 2. 自定义组合
- 在Web界面选择因子分类
- 勾选需要的因子
- 查看因子重要性分析
- 应用因子组合到模型训练

### 3. 因子评估
- **重要性分析**: 基于随机森林和互信息
- **相关性分析**: 避免高相关因子
- **稳定性测试**: 因子在不同时期的表现

## 🚀 快速开始

### 1. 基础使用
```bash
# 1. 启动Web界面
python web/app.py

# 2. 访问 http://127.0.0.1:5001
# 3. 选择因子组合
# 4. 训练模型
# 5. 运行回测
# 6. 查看结果
```

### 2. 程序化使用
```python
from features.quantitative_factors import QuantitativeFactors
from backtest.backtest_engine import BacktestEngine

# 计算量化因子
qf = QuantitativeFactors()
factors_df = qf.calculate_factors(data, categories=['momentum', 'volume'])

# 运行回测
backtest_engine = BacktestEngine()
results = backtest_engine.run_backtest(signals_df, price_data)
```

## 📝 开发指南

### 1. 添加自定义因子
```python
def custom_factor(df, period=10):
    """自定义因子计算函数"""
    return df['close'].rolling(period).apply(lambda x: your_logic)

# 注册因子
qf.register_factor('custom_factor', custom_factor, {'period': 10})
```

### 2. 扩展回测指标
```python
# 在backtest_engine.py中添加新指标
def calculate_custom_metric(self, portfolio_df):
    """计算自定义指标"""
    return your_calculation
```

### 3. 添加新数据源
```python
class CustomDataSource(BaseDataSource):
    """自定义数据源"""
    def get_stock_market_data(self, stock_code, start_date, end_date):
        # 实现数据获取逻辑
        return data_df
```

## 🔍 故障排除

### 1. 常见问题
- **数据源连接失败**: 检查网络连接，尝试其他数据源
- **模型训练失败**: 检查数据质量，调整特征选择
- **回测结果异常**: 检查交易信号和价格数据匹配

### 2. 日志查看
```bash
# 查看系统日志
tail -f logs/system.log

# 查看特征工程日志  
tail -f logs/features.log

# 查看回测日志
tail -f logs/backtest.log
```

### 3. 性能优化
- 减少因子数量以提高计算速度
- 使用数据缓存减少重复计算
- 调整回测参数减少内存使用

## 📞 技术支持

如遇问题，请：
1. 查看日志文件定位问题
2. 运行测试脚本验证功能
3. 检查配置文件设置
4. 参考文档和示例代码

---

**版本**: v2.0  
**更新日期**: 2024-12-16  
**开发者**: Augment Agent  
**基于**: adata数据源 + 机器学习 + 量化因子工程
