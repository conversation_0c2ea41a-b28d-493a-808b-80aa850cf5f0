# 数据库配置文件
database:
  # SQLite配置
  sqlite:
    # 数据库文件路径
    db_path: "data/quantitative_trading.db"
    # 连接池配置
    pool_size: 10
    # 超时设置
    timeout: 30
    
  # 数据表配置
  tables:
    # 股票基本信息表
    stock_info:
      name: "stock_info"
      columns:
        stock_code: "TEXT PRIMARY KEY"
        short_name: "TEXT"
        exchange: "TEXT"
        list_date: "DATE"
        industry: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
        updated_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
        
    # 股票行情数据表
    stock_market:
      name: "stock_market"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        stock_code: "TEXT"
        trade_date: "DATE"
        open: "REAL"
        close: "REAL"
        high: "REAL"
        low: "REAL"
        volume: "BIGINT"
        amount: "REAL"
        change: "REAL"
        change_pct: "REAL"
        turnover_ratio: "REAL"
        pre_close: "REAL"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_stock_market_code_date ON stock_market(stock_code, trade_date)"
        - "CREATE INDEX idx_stock_market_date ON stock_market(trade_date)"
        
    # 特征数据表
    features:
      name: "features"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        stock_code: "TEXT"
        trade_date: "DATE"
        feature_name: "TEXT"
        feature_value: "REAL"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_features_code_date ON features(stock_code, trade_date)"
        - "CREATE INDEX idx_features_name ON features(feature_name)"
        
    # 模型预测结果表
    predictions:
      name: "predictions"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        stock_code: "TEXT"
        model_name: "TEXT"
        prediction_date: "DATE"
        prediction_time: "TIME"
        target_date: "DATE"
        prediction_value: "REAL"
        confidence: "REAL"
        actual_value: "REAL"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_predictions_code_date ON predictions(stock_code, prediction_date)"
        - "CREATE INDEX idx_predictions_model ON predictions(model_name)"
        
    # 交易记录表
    trades:
      name: "trades"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        stock_code: "TEXT"
        trade_type: "TEXT"  # buy, sell
        trade_date: "DATE"
        trade_time: "TIME"
        quantity: "INTEGER"
        price: "REAL"
        amount: "REAL"
        commission: "REAL"
        model_signal: "TEXT"
        notes: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_trades_code_date ON trades(stock_code, trade_date)"
        - "CREATE INDEX idx_trades_type ON trades(trade_type)"
        
    # 回测结果表
    backtest_results:
      name: "backtest_results"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        model_name: "TEXT"
        stock_code: "TEXT"
        start_date: "DATE"
        end_date: "DATE"
        total_return: "REAL"
        annual_return: "REAL"
        sharpe_ratio: "REAL"
        max_drawdown: "REAL"
        win_rate: "REAL"
        total_trades: "INTEGER"
        config: "TEXT"  # JSON格式的配置信息
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_backtest_model ON backtest_results(model_name)"
        - "CREATE INDEX idx_backtest_stock ON backtest_results(stock_code)"
        
    # 模型性能表
    model_performance:
      name: "model_performance"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        model_name: "TEXT"
        metric_name: "TEXT"
        metric_value: "REAL"
        evaluation_date: "DATE"
        data_period: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_performance_model ON model_performance(model_name)"
        - "CREATE INDEX idx_performance_metric ON model_performance(metric_name)"

    # 债券监控数据表
    bond_monitor_data:
      name: "bond_monitor_data"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        date: "TEXT NOT NULL"
        bond_category: "TEXT NOT NULL"
        yield_rate: "REAL"
        daily_change: "REAL"
        data_source: "TEXT"
        raw_data: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE UNIQUE INDEX idx_bond_monitor_date_category ON bond_monitor_data(date, bond_category)"
        - "CREATE INDEX idx_bond_monitor_date ON bond_monitor_data(date)"

    # 债券历史涨跌幅表
    bond_change_history:
      name: "bond_change_history"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        date: "TEXT NOT NULL UNIQUE"
        short_medium_term_change: "REAL DEFAULT 0.0"
        ten_year_change: "REAL DEFAULT 0.0"
        thirty_year_change: "REAL DEFAULT 0.0"
        high_grade_credit_change: "REAL DEFAULT 0.0"
        market_summary: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_bond_change_date ON bond_change_history(date)"

    # 债券监控日志表
    bond_monitor_logs:
      name: "bond_monitor_logs"
      columns:
        id: "INTEGER PRIMARY KEY AUTOINCREMENT"
        date: "TEXT NOT NULL"
        monitor_time: "TEXT NOT NULL"
        status: "TEXT NOT NULL"
        message: "TEXT"
        data_count: "INTEGER DEFAULT 0"
        error_info: "TEXT"
        created_at: "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
      indexes:
        - "CREATE INDEX idx_bond_monitor_logs_date ON bond_monitor_logs(date)"
        - "CREATE INDEX idx_bond_monitor_logs_status ON bond_monitor_logs(status)"

# 数据管理配置
data_management:
  # 数据保留策略
  retention:
    # 行情数据保留天数（-1表示永久保留）
    market_data_days: 365
    # 特征数据保留天数
    feature_data_days: 180
    # 预测结果保留天数
    prediction_data_days: 90
    # 交易记录保留天数（-1表示永久保留）
    trade_data_days: -1
    
  # 数据清理配置
  cleanup:
    # 是否启用自动清理
    auto_cleanup: true
    # 清理频率（天）
    cleanup_frequency: 7
    # 清理时间
    cleanup_time: "02:00"
    
  # 数据备份配置
  backup:
    # 是否启用自动备份
    auto_backup: true
    # 备份频率（天）
    backup_frequency: 1
    # 备份路径
    backup_path: "data/backups"
    # 备份文件保留天数
    backup_retention_days: 30

# 数据源配置
data_source:
  # 数据源优先级配置
  priority:
    # 主数据源
    primary: "adata"
    # 备用数据源列表（按优先级排序）
    fallback: ["akshare", "baostock"]
    # 是否启用自动故障转移
    auto_failover: true

  # adata配置
  adata:
    # 是否启用
    enabled: true
    # 是否使用代理
    use_proxy: false
    # 代理配置
    proxy:
      ip: ""
      port: ""
    # 请求间隔（秒）
    request_interval: 0.1
    # 重试次数
    max_retries: 3
    # 超时时间（秒）
    timeout: 30

  # akshare配置
  akshare:
    # 是否启用
    enabled: true
    # 请求间隔（秒）
    request_interval: 0.2
    # 重试次数
    max_retries: 3
    # 超时时间（秒）
    timeout: 30
    # 是否使用缓存
    use_cache: true
    # 缓存过期时间（分钟）
    cache_expire_minutes: 5

  # baostock配置
  baostock:
    # 是否启用
    enabled: true
    # 请求间隔（秒）
    request_interval: 0.1
    # 重试次数
    max_retries: 3
    # 超时时间（秒）
    timeout: 30
    # 自动登录
    auto_login: true

  # yfinance配置
  yfinance:
    # 是否启用
    enabled: false
    # 请求间隔（秒）
    request_interval: 0.5
    # 重试次数
    max_retries: 3
    # 超时时间（秒）
    timeout: 30
    # 支持的市场
    supported_markets: ["HK", "US"]

  # 数据更新配置
  update:
    # 股票列表更新频率（天）
    stock_list_update_frequency: 7
    # 行情数据更新频率（分钟）
    market_data_update_frequency: 5
    # 特征数据更新频率（分钟）
    feature_data_update_frequency: 10
    # 数据源健康检查频率（分钟）
    health_check_frequency: 30

  # 数据质量配置
  quality:
    # 最小数据量要求
    min_data_points: 10
    # 数据完整性检查
    completeness_check: true
    # 数据一致性检查
    consistency_check: true
    # 异常值检测
    outlier_detection: true
