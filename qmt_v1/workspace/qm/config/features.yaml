# 特征配置文件 - 高度可扩展的特征定义
# 量化因子配置
quantitative_factors:
  # 因子计算参数
  parameters:
    # 动量因子参数
    momentum:
      short_period: 5
      medium_period: 10
      long_period: 20
      rsi_period: 14

    # 均值回归因子参数
    mean_reversion:
      bollinger_period: 20
      bollinger_std: 2
      price_deviation_period: 20
      rsi_oversold_threshold: 30
      rsi_overbought_threshold: 70

    # 成交量因子参数
    volume:
      volume_ratio_period: 20
      volume_price_trend_period: 10
      obv_trend_period: 10
      volume_momentum_period: 5

    # 波动率因子参数
    volatility:
      short_volatility_period: 5
      long_volatility_period: 20
      atr_period: 14
      volatility_breakout_period: 20

    # 趋势因子参数
    trend:
      trend_strength_period: 20
      ma_short_period: 5
      ma_long_period: 20
      macd_fast: 12
      macd_slow: 26
      macd_signal: 9

    # 技术信号因子参数
    technical:
      kdj_period: 9
      macd_fast: 12
      macd_slow: 26
      macd_signal: 9
      bollinger_period: 20
      bollinger_std: 2

  # 因子启用配置
  enabled_factors:
    momentum:
      - momentum_5d
      - momentum_10d
      - momentum_20d
      - rsi_divergence

    mean_reversion:
      - bollinger_position
      - price_deviation
      - rsi_oversold
      - rsi_overbought

    volume:
      - volume_ratio
      - volume_price_trend
      - obv_trend
      - volume_momentum

    volatility:
      - volatility_ratio
      - atr_ratio
      - volatility_breakout

    trend:
      - trend_strength
      - ma_trend
      - macd_trend

    technical:
      - kdj_signal
      - macd_signal
      - bollinger_signal

features:
  # 基础价格特征
  basic_features:
    - name: "open"
      description: "开盘价"
      enabled: true
    - name: "close"
      description: "收盘价"
      enabled: true
    - name: "high"
      description: "最高价"
      enabled: true
    - name: "low"
      description: "最低价"
      enabled: true
    - name: "volume"
      description: "成交量"
      enabled: true
    - name: "amount"
      description: "成交额"
      enabled: true

  # 收益率特征
  return_features:
    - name: "return_1d"
      description: "1日收益率"
      period: 1
      enabled: true
    - name: "return_3d"
      description: "3日收益率"
      period: 3
      enabled: true
    - name: "return_5d"
      description: "5日收益率"
      period: 5
      enabled: true

  # 波动率特征
  volatility_features:
    - name: "volatility_5d"
      description: "5日波动率"
      window: 5
      enabled: true
    - name: "volatility_10d"
      description: "10日波动率"
      window: 10
      enabled: true
    - name: "volatility_20d"
      description: "20日波动率"
      window: 20
      enabled: true

  # 技术指标特征
  technical_indicators:
    # 移动平均线
    sma:
      - name: "sma_5"
        description: "5日简单移动平均"
        window: 5
        enabled: true
      - name: "sma_10"
        description: "10日简单移动平均"
        window: 10
        enabled: true
      - name: "sma_20"
        description: "20日简单移动平均"
        window: 20
        enabled: true
      - name: "sma_60"
        description: "60日简单移动平均"
        window: 60
        enabled: true

    # MACD指标
    macd:
      - name: "macd"
        description: "MACD主线"
        fast_period: 12
        slow_period: 26
        signal_period: 9
        enabled: true
      - name: "macd_signal"
        description: "MACD信号线"
        fast_period: 12
        slow_period: 26
        signal_period: 9
        enabled: true
      - name: "macd_histogram"
        description: "MACD柱状图"
        fast_period: 12
        slow_period: 26
        signal_period: 9
        enabled: true

    # RSI指标
    rsi:
      - name: "rsi_14"
        description: "14日RSI"
        window: 14
        enabled: true
      - name: "rsi_6"
        description: "6日RSI"
        window: 6
        enabled: true

    # 布林带
    bollinger:
      - name: "bb_upper"
        description: "布林带上轨"
        window: 20
        std_dev: 2
        enabled: true
      - name: "bb_middle"
        description: "布林带中轨"
        window: 20
        std_dev: 2
        enabled: true
      - name: "bb_lower"
        description: "布林带下轨"
        window: 20
        std_dev: 2
        enabled: true
      - name: "bb_width"
        description: "布林带宽度"
        window: 20
        std_dev: 2
        enabled: true

  # 时序特征（主要用于时序模型）
  time_series_features:
    # 滞后特征
    lag_features:
      - name: "close_lag_1"
        description: "收盘价滞后1期"
        column: "close"
        lag: 1
        enabled: true
      - name: "close_lag_3"
        description: "收盘价滞后3期"
        column: "close"
        lag: 3
        enabled: true
      - name: "close_lag_5"
        description: "收盘价滞后5期"
        column: "close"
        lag: 5
        enabled: true
      - name: "volume_lag_1"
        description: "成交量滞后1期"
        column: "volume"
        lag: 1
        enabled: true

    # 滚动统计特征
    rolling_features:
      - name: "close_rolling_mean_5"
        description: "收盘价5日滚动均值"
        column: "close"
        window: 5
        function: "mean"
        enabled: true
      - name: "close_rolling_std_5"
        description: "收盘价5日滚动标准差"
        column: "close"
        window: 5
        function: "std"
        enabled: true
      - name: "volume_rolling_mean_5"
        description: "成交量5日滚动均值"
        column: "volume"
        window: 5
        function: "mean"
        enabled: true

    # 季节性特征
    seasonal_features:
      - name: "day_of_week"
        description: "星期几"
        enabled: true
      - name: "day_of_month"
        description: "月份中的第几天"
        enabled: true
      - name: "month"
        description: "月份"
        enabled: true

# 特征选择配置
feature_selection:
  # 是否启用特征选择
  enabled: true
  # 特征选择方法
  method: "mutual_info"  # mutual_info, correlation, importance
  # 选择的特征数量
  max_features: 50
  # 相关性阈值（用于去除高相关性特征）
  correlation_threshold: 0.95

# 数据预处理配置
preprocessing:
  # 是否标准化
  standardize: true
  # 是否填充缺失值
  fill_missing: true
  # 缺失值填充方法
  fill_method: "forward"  # forward, backward, mean, median
  # 异常值处理
  outlier_treatment:
    enabled: true
    method: "iqr"  # iqr, zscore
    threshold: 3
