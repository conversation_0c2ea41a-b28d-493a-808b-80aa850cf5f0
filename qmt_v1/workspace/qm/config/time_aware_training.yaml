# 时间感知训练配置文件
# Time-Aware Training Configuration

# 数据配置
data_config:
  # 训练数据配置
  training:
    # 方式1: 按交易日数量
    by_trading_days:
      train_days: 252  # 一年的交易日数量
      backtest_days: 63  # 一个季度的交易日数量
      prediction_days: 20  # 用于特征计算的历史数据天数
    
    # 方式2: 按日期范围
    by_date_range:
      train_start: "2023-01-03"  # 训练开始日期
      train_end: "2023-12-29"    # 训练结束日期
      backtest_days: 63          # 回测天数
      prediction_days: 20        # 预测天数
    
    # 方式3: 滚动训练
    rolling_training:
      train_window: 252    # 训练窗口（交易日）
      backtest_window: 21  # 回测窗口（交易日）
      step_size: 21        # 滚动步长（交易日）
      prediction_days: 20  # 预测天数

  # 股票池配置
  stock_pool:
    # 默认股票池
    default: ["000001", "000002", "600000", "600036", "000858"]
    
    # 大盘股池
    large_cap: ["000001", "000002", "600000", "600036", "600519", "000858", "002415"]
    
    # 中小盘股池
    small_cap: ["300015", "002230", "300059", "002304", "300144"]
    
    # 测试股池（少量数据）
    test: ["000001", "000002"]

# 时间感知模型配置
time_aware_models:
  # 早盘模型配置（9:35预测）
  morning:
    # 预测时间
    prediction_time: "09:35"
    
    # 模型列表
    models:
      - name: "xgboost_next_day_direction"
        description: "明日涨跌方向预测"
        target: "next_day_direction"
        enabled: true
        
      - name: "xgboost_10d_surge"
        description: "10日拉升概率预测"
        target: "10d_surge_probability"
        enabled: true
        
      - name: "time_series_price_prediction"
        description: "明日价格预测"
        target: "next_day_prices"
        enabled: false  # 需要TensorFlow
    
    # 特征配置
    features:
      # 基础特征
      basic_features: true
      
      # 技术指标
      technical_indicators: true
      
      # 量化因子
      quantitative_factors:
        enabled: true
        categories: ["momentum", "mean_reversion", "volume", "volatility"]
        specific_factors: ["momentum_10d", "bollinger_position", "volume_ratio", "volatility_ratio"]
      
      # 早盘特有特征
      morning_specific:
        overnight_gap: true      # 隔夜跳空
        prev_day_strength: true  # 前一日强度
        prev_volume_ratio: true  # 前一日成交量比率

  # 尾盘模型配置（14:50预测）
  afternoon:
    # 预测时间
    prediction_time: "14:50"
    
    # 模型列表
    models:
      - name: "xgboost_next_day_direction"
        description: "明日涨跌方向预测"
        target: "next_day_direction"
        enabled: true
        
      - name: "xgboost_10d_surge"
        description: "10日拉升概率预测"
        target: "10d_surge_probability"
        enabled: true
        
      - name: "time_series_price_prediction"
        description: "明日价格预测"
        target: "next_day_prices"
        enabled: false  # 需要TensorFlow
    
    # 特征配置
    features:
      # 基础特征
      basic_features: true
      
      # 技术指标
      technical_indicators: true
      
      # 量化因子
      quantitative_factors:
        enabled: true
        categories: ["momentum", "mean_reversion", "volume", "trend"]
        specific_factors: ["momentum_10d", "bollinger_position", "volume_ratio", "trend_strength"]
      
      # 尾盘特有特征
      afternoon_specific:
        intraday_return: true      # 当日涨跌幅
        intraday_amplitude: true   # 当日振幅
        current_volume_ratio: true # 当日成交量比率

# 训练策略配置
training_strategies:
  # 策略1: 快速测试
  quick_test:
    data_config: "by_trading_days"
    train_days: 60
    backtest_days: 20
    prediction_days: 10
    stock_pool: "test"
    models: ["xgboost_next_day_direction"]
    train_both_times: false
    
  # 策略2: 标准训练
  standard:
    data_config: "by_trading_days"
    train_days: 252
    backtest_days: 63
    prediction_days: 20
    stock_pool: "default"
    models: ["xgboost_next_day_direction", "xgboost_10d_surge"]
    train_both_times: true
    
  # 策略3: 完整训练
  comprehensive:
    data_config: "by_date_range"
    train_start: "2023-01-03"
    train_end: "2023-12-29"
    backtest_days: 63
    prediction_days: 20
    stock_pool: "large_cap"
    models: ["xgboost_next_day_direction", "xgboost_10d_surge", "time_series_price_prediction"]
    train_both_times: true
    
  # 策略4: 滚动训练
  rolling:
    data_config: "rolling_training"
    train_window: 252
    backtest_window: 21
    step_size: 21
    prediction_days: 20
    stock_pool: "default"
    models: ["xgboost_next_day_direction"]
    train_both_times: true

# 预测配置
prediction_config:
  # 早盘预测（9:35）
  morning_prediction:
    enabled: true
    time: "09:35"
    models: ["morning_next_day_direction", "morning_10d_surge"]
    prediction_days: 20
    stock_pool: "default"
    
  # 尾盘预测（14:50）
  afternoon_prediction:
    enabled: true
    time: "14:50"
    models: ["afternoon_next_day_direction", "afternoon_10d_surge"]
    prediction_days: 20
    stock_pool: "default"

# 模型评估配置
evaluation_config:
  # 评估指标
  metrics:
    classification:
      - "accuracy"
      - "precision"
      - "recall"
      - "f1_score"
      - "auc"
      - "confusion_matrix"
    
    regression:
      - "mse"
      - "rmse"
      - "mae"
      - "r2_score"
      - "mape"
  
  # 交叉验证
  cross_validation:
    enabled: true
    cv_folds: 5
    time_series_split: true  # 使用时间序列分割
  
  # 模型对比
  model_comparison:
    enabled: true
    compare_times: true      # 对比早盘vs尾盘模型
    compare_periods: true    # 对比不同训练期间
    save_comparison: true    # 保存对比结果

# 系统配置
system_config:
  # 模型存储
  model_storage:
    base_dir: "models/time_aware"
    backup_enabled: true
    max_versions: 5  # 保留最近5个版本
  
  # 日志配置
  logging:
    level: "INFO"
    save_to_file: true
    log_dir: "logs/time_aware"
  
  # 性能配置
  performance:
    parallel_training: false  # 并行训练（需要更多内存）
    batch_size: 1000         # 批处理大小
    memory_limit: "4GB"      # 内存限制
  
  # 数据缓存
  caching:
    enabled: true
    cache_dir: "cache/time_aware"
    cache_features: true     # 缓存特征数据
    cache_models: true       # 缓存模型结果

# 默认配置
default_strategy: "standard"  # 默认使用标准训练策略
