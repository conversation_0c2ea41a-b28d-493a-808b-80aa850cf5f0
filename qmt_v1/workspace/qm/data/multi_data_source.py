"""
多数据源管理器 - 统一管理多个A股数据源
支持自动切换和故障转移
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import yaml
from typing import List, Dict, Optional, Union
from loguru import logger
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入各个数据源
from data.data_source import AdataSource, BaseDataSource, AVAILABLE_SOURCES


class AkshareSource(BaseDataSource):
    """akshare数据源实现"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        if not AVAILABLE_SOURCES.get('akshare', False):
            raise ImportError("akshare 未安装")
        import akshare as ak
        self.ak = ak
        
    def is_available(self) -> bool:
        return AVAILABLE_SOURCES.get('akshare', False)
    
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        try:
            logger.info("akshare: 获取股票列表...")
            # 获取A股股票列表
            df = self.ak.stock_info_a_code_name()
            # 统一列名
            df = df.rename(columns={'code': 'stock_code', 'name': 'short_name'})
            df['exchange'] = df['stock_code'].apply(lambda x: 'SZ' if x.startswith('0') or x.startswith('3') else 'SH')
            logger.info(f"akshare: 获取到 {len(df)} 只股票")
            return df
        except Exception as e:
            logger.error(f"akshare: 获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_stock_market_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票行情数据"""
        try:
            logger.info(f"akshare: 获取股票 {stock_code} 行情数据: {start_date} 到 {end_date}")
            
            # akshare的股票代码格式转换
            symbol = self._convert_stock_code(stock_code)
            
            df = self.ak.stock_zh_a_hist(
                symbol=symbol,
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', ''),
                adjust="qfq"  # 前复权
            )
            
            if not df.empty:
                # 统一列名
                df = self._standardize_market_data(df, stock_code)
                logger.info(f"akshare: 获取到 {len(df)} 条行情数据")
            
            return df
            
        except Exception as e:
            logger.error(f"akshare: 获取股票 {stock_code} 行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_current_market_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据"""
        try:
            logger.info(f"akshare: 获取 {len(stock_codes)} 只股票的实时行情")
            
            # akshare实时行情
            df = self.ak.stock_zh_a_spot_em()
            
            # 过滤指定股票
            if stock_codes:
                df = df[df['代码'].isin(stock_codes)]
            
            # 统一列名
            df = self._standardize_current_data(df)
            
            logger.info(f"akshare: 获取到 {len(df)} 只股票的实时行情")
            return df
            
        except Exception as e:
            logger.error(f"akshare: 获取实时行情失败: {e}")
            return pd.DataFrame()
    
    def _convert_stock_code(self, stock_code: str) -> str:
        """转换股票代码格式"""
        # akshare需要6位数字代码
        return stock_code.zfill(6)
    
    def _standardize_market_data(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """标准化行情数据格式"""
        if df.empty:
            return df
            
        # 重命名列
        column_mapping = {
            '日期': 'trade_date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '涨跌幅': 'change_pct',
            '涨跌额': 'change',
            '换手率': 'turnover_ratio'
        }
        
        df = df.rename(columns=column_mapping)
        df['stock_code'] = stock_code
        
        # 数据类型转换
        numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount', 'change', 'change_pct', 'turnover_ratio']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 日期处理
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        return df
    
    def _standardize_current_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化实时数据格式"""
        if df.empty:
            return df
            
        # 重命名列
        column_mapping = {
            '代码': 'stock_code',
            '名称': 'short_name',
            '最新价': 'current_price',
            '涨跌幅': 'change_pct',
            '涨跌额': 'change',
            '成交量': 'volume',
            '成交额': 'amount',
            '换手率': 'turnover_ratio',
            '开盘': 'open',
            '昨收': 'pre_close',
            '最高': 'high',
            '最低': 'low'
        }
        
        # 只保留存在的列
        existing_mapping = {k: v for k, v in column_mapping.items() if k in df.columns}
        df = df.rename(columns=existing_mapping)
        
        return df


class BaostockSource(BaseDataSource):
    """baostock数据源实现"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        if not AVAILABLE_SOURCES.get('baostock', False):
            raise ImportError("baostock 未安装")
        import baostock as bs
        self.bs = bs
        self._login()
        
    def _login(self):
        """登录baostock"""
        try:
            lg = self.bs.login()
            if lg.error_code != '0':
                logger.error(f"baostock登录失败: {lg.error_msg}")
            else:
                logger.info("baostock登录成功")
        except Exception as e:
            logger.error(f"baostock登录异常: {e}")
    
    def __del__(self):
        """析构函数，登出baostock"""
        try:
            if hasattr(self, 'bs'):
                self.bs.logout()
        except:
            pass
    
    def is_available(self) -> bool:
        return AVAILABLE_SOURCES.get('baostock', False)
    
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        try:
            logger.info("baostock: 获取股票列表...")
            
            # 获取沪深A股列表
            rs = self.bs.query_all_stock(day=datetime.now().strftime('%Y-%m-%d'))
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())
            
            if data_list:
                df = pd.DataFrame(data_list, columns=rs.fields)
                # 只保留A股
                df = df[df['type'] == '1']  # type=1表示A股
                # 统一列名
                df = df.rename(columns={'code': 'stock_code', 'code_name': 'short_name'})
                df['exchange'] = df['stock_code'].apply(lambda x: x.split('.')[0])
                logger.info(f"baostock: 获取到 {len(df)} 只股票")
                return df
            else:
                logger.warning("baostock: 未获取到股票列表")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"baostock: 获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_stock_market_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票行情数据"""
        try:
            logger.info(f"baostock: 获取股票 {stock_code} 行情数据: {start_date} 到 {end_date}")
            
            # baostock的股票代码格式转换
            bs_code = self._convert_stock_code(stock_code)
            
            rs = self.bs.query_history_k_data_plus(
                bs_code,
                "date,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
                start_date=start_date,
                end_date=end_date,
                frequency="d",
                adjustflag="3"  # 前复权
            )
            
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())
            
            if data_list:
                df = pd.DataFrame(data_list, columns=rs.fields)
                df = self._standardize_market_data(df, stock_code)
                logger.info(f"baostock: 获取到 {len(df)} 条行情数据")
                return df
            else:
                logger.warning(f"baostock: 股票 {stock_code} 未获取到行情数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"baostock: 获取股票 {stock_code} 行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_current_market_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据"""
        # baostock不支持实时数据，返回空DataFrame
        logger.warning("baostock: 不支持实时行情数据")
        return pd.DataFrame()
    
    def _convert_stock_code(self, stock_code: str) -> str:
        """转换股票代码格式"""
        # baostock需要交易所前缀
        if stock_code.startswith('0') or stock_code.startswith('3'):
            return f"sz.{stock_code}"
        else:
            return f"sh.{stock_code}"
    
    def _standardize_market_data(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """标准化行情数据格式"""
        if df.empty:
            return df
            
        # 重命名列
        column_mapping = {
            'date': 'trade_date',
            'open': 'open',
            'close': 'close',
            'high': 'high',
            'low': 'low',
            'volume': 'volume',
            'amount': 'amount',
            'pctChg': 'change_pct',
            'turn': 'turnover_ratio',
            'preclose': 'pre_close'
        }
        
        df = df.rename(columns=column_mapping)
        df['stock_code'] = stock_code
        
        # 计算涨跌额
        if 'close' in df.columns and 'pre_close' in df.columns:
            df['change'] = pd.to_numeric(df['close'], errors='coerce') - pd.to_numeric(df['pre_close'], errors='coerce')
        
        # 数据类型转换
        numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount', 'change', 'change_pct', 'turnover_ratio', 'pre_close']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 日期处理
        df['trade_date'] = pd.to_datetime(df['trade_date'])

        return df


class MultiDataSourceManager:
    """多数据源管理器"""

    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化多数据源管理器"""
        self.config = self._load_config(config_path)
        self.data_sources = {}
        self.primary_source = None
        self.fallback_sources = []
        self._initialize_sources()

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def _initialize_sources(self):
        """初始化所有可用的数据源"""
        source_config = self.config.get('data_source', {})

        # 初始化adata数据源
        if AVAILABLE_SOURCES.get('adata', False):
            try:
                from data.data_source import AdataSource
                adata_config = source_config.get('adata', {})
                self.data_sources['adata'] = AdataSource(adata_config)
                logger.info("adata数据源初始化成功")
            except Exception as e:
                logger.error(f"adata数据源初始化失败: {e}")

        # 初始化akshare数据源
        if AVAILABLE_SOURCES.get('akshare', False):
            try:
                akshare_config = source_config.get('akshare', {})
                self.data_sources['akshare'] = AkshareSource(akshare_config)
                logger.info("akshare数据源初始化成功")
            except Exception as e:
                logger.error(f"akshare数据源初始化失败: {e}")

        # 初始化baostock数据源
        if AVAILABLE_SOURCES.get('baostock', False):
            try:
                baostock_config = source_config.get('baostock', {})
                self.data_sources['baostock'] = BaostockSource(baostock_config)
                logger.info("baostock数据源初始化成功")
            except Exception as e:
                logger.error(f"baostock数据源初始化失败: {e}")

        # 设置主数据源和备用数据源
        self._setup_source_priority()

    def _setup_source_priority(self):
        """设置数据源优先级"""
        # 默认优先级：adata > akshare > baostock
        priority_order = ['adata', 'akshare', 'baostock']

        available_sources = [name for name in priority_order if name in self.data_sources]

        if available_sources:
            self.primary_source = available_sources[0]
            self.fallback_sources = available_sources[1:]
            logger.info(f"主数据源: {self.primary_source}")
            logger.info(f"备用数据源: {self.fallback_sources}")
        else:
            logger.error("没有可用的数据源")

    def get_available_sources(self) -> List[str]:
        """获取可用的数据源列表"""
        return list(self.data_sources.keys())

    def get_stock_list(self, source_name: str = None) -> pd.DataFrame:
        """获取股票列表，支持自动故障转移"""
        sources_to_try = [source_name] if source_name else [self.primary_source] + self.fallback_sources

        for source_name in sources_to_try:
            if source_name not in self.data_sources:
                continue

            try:
                source = self.data_sources[source_name]
                df = source.get_stock_list()
                if not df.empty:
                    logger.info(f"使用 {source_name} 成功获取股票列表")
                    return df
                else:
                    logger.warning(f"{source_name} 返回空的股票列表")
            except Exception as e:
                logger.error(f"{source_name} 获取股票列表失败: {e}")
                continue

        logger.error("所有数据源都无法获取股票列表")
        return pd.DataFrame()

    def get_stock_market_data(self, stock_code: str, start_date: str = None,
                            end_date: str = None, source_name: str = None) -> pd.DataFrame:
        """获取股票行情数据，支持自动故障转移"""
        # 设置默认日期
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

        sources_to_try = [source_name] if source_name else [self.primary_source] + self.fallback_sources

        for source_name in sources_to_try:
            if source_name not in self.data_sources:
                continue

            try:
                source = self.data_sources[source_name]
                df = source.get_stock_market_data(stock_code, start_date, end_date)
                if not df.empty:
                    logger.info(f"使用 {source_name} 成功获取股票 {stock_code} 行情数据")
                    return df
                else:
                    logger.warning(f"{source_name} 返回空的行情数据")
            except Exception as e:
                logger.error(f"{source_name} 获取股票 {stock_code} 行情数据失败: {e}")
                continue

        logger.error(f"所有数据源都无法获取股票 {stock_code} 行情数据")
        return pd.DataFrame()

    def get_current_market_data(self, stock_codes: List[str],
                              source_name: str = None) -> pd.DataFrame:
        """获取实时行情数据，支持自动故障转移"""
        # 过滤掉不支持实时数据的数据源
        sources_to_try = []
        if source_name:
            sources_to_try = [source_name]
        else:
            # baostock不支持实时数据，所以排除
            available_sources = [name for name in [self.primary_source] + self.fallback_sources
                               if name != 'baostock']
            sources_to_try = available_sources

        for source_name in sources_to_try:
            if source_name not in self.data_sources:
                continue

            try:
                source = self.data_sources[source_name]
                df = source.get_current_market_data(stock_codes)
                if not df.empty:
                    logger.info(f"使用 {source_name} 成功获取实时行情数据")
                    return df
                else:
                    logger.warning(f"{source_name} 返回空的实时行情数据")
            except Exception as e:
                logger.error(f"{source_name} 获取实时行情数据失败: {e}")
                continue

        logger.error("所有数据源都无法获取实时行情数据")
        return pd.DataFrame()

    def test_all_sources(self) -> Dict[str, bool]:
        """测试所有数据源的可用性"""
        results = {}

        for source_name, source in self.data_sources.items():
            try:
                logger.info(f"测试数据源: {source_name}")

                # 测试获取股票列表
                stock_list = source.get_stock_list()
                if stock_list.empty:
                    results[source_name] = False
                    logger.error(f"{source_name} 测试失败: 无法获取股票列表")
                    continue

                # 测试获取行情数据（使用第一只股票）
                test_stock = stock_list.iloc[0]['stock_code']
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                market_data = source.get_stock_market_data(test_stock, start_date)

                if market_data.empty:
                    results[source_name] = False
                    logger.error(f"{source_name} 测试失败: 无法获取行情数据")
                    continue

                results[source_name] = True
                logger.info(f"{source_name} 测试成功")

            except Exception as e:
                results[source_name] = False
                logger.error(f"{source_name} 测试失败: {e}")

        return results


# 测试函数
def test_multi_data_source():
    """测试多数据源功能"""
    logger.info("开始测试多数据源管理器...")

    manager = MultiDataSourceManager()

    # 显示可用数据源
    available_sources = manager.get_available_sources()
    logger.info(f"可用数据源: {available_sources}")

    # 测试所有数据源
    test_results = manager.test_all_sources()
    logger.info(f"数据源测试结果: {test_results}")

    # 测试获取股票列表（自动故障转移）
    stock_list = manager.get_stock_list()
    if not stock_list.empty:
        logger.info(f"股票列表测试成功，共 {len(stock_list)} 只股票")
        test_stocks = stock_list.head(3)['stock_code'].tolist()
    else:
        logger.error("股票列表测试失败")
        test_stocks = ['000001', '000002']

    # 测试获取行情数据（自动故障转移）
    test_stock = test_stocks[0]
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    market_data = manager.get_stock_market_data(test_stock, start_date)

    if not market_data.empty:
        logger.info(f"行情数据测试成功，股票 {test_stock} 获取到 {len(market_data)} 条数据")
        print(market_data.head())
    else:
        logger.error(f"行情数据测试失败，股票 {test_stock}")

    # 测试实时行情（自动故障转移）
    current_data = manager.get_current_market_data(test_stocks[:2])
    if not current_data.empty:
        logger.info(f"实时行情测试成功，获取到 {len(current_data)} 只股票数据")
        print(current_data)
    else:
        logger.error("实时行情测试失败")

    logger.info("多数据源管理器测试完成")


if __name__ == "__main__":
    test_multi_data_source()
