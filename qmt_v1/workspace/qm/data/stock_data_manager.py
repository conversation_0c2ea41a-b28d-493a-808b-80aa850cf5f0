"""
A股数据管理器 - 基于adata的数据获取和管理
专门处理A股市场数据的获取、存储和管理
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import yaml
from typing import List, Dict, Optional, Union
from loguru import logger
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.data_source import AdataSource
from data.database import DatabaseManager


class StockDataManager:
    """
    A股数据管理器 (Stock Data Manager)
    ================================

    专门用于管理A股市场数据的核心组件，基于adata数据源。

    🔧 主要功能:
    - A股股票列表获取和管理
    - 历史行情数据获取（支持最近2年）
    - 实时行情数据获取
    - 数据质量验证和清理
    - 自动数据更新机制

    📊 数据源:
    - 主要数据源: adata (https://github.com/1nchaos/adata)
    - 备用数据源: 支持多数据源故障转移
    - 数据存储: SQLite轻量级存储

    💡 设计理念:
    - 轻量级存储，不保存大量历史数据
    - 实时获取，按需计算特征
    - 高可靠性，支持数据源故障转移
    - 易于扩展，支持新数据源接入
    """

    def __init__(self, config_path: str = "config/database.yaml"):
        """
        初始化数据管理器

        Args:
            config_path (str, optional): 配置文件路径，默认使用内置配置
        """
        self.config = self._load_config(config_path)
        self.data_source = AdataSource(self.config.get('data_source', {}).get('adata', {}))
        self.db_manager = DatabaseManager(config_path)
        
        # 数据获取配置
        self.data_config = self.config.get('data_management', {})
        self.retention_days = self.data_config.get('retention', {}).get('market_data_days', 730)  # 2年
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def get_stock_universe(self, limit: int = None) -> List[str]:
        """
        获取股票池
        
        Args:
            limit: 限制股票数量，用于测试
            
        Returns:
            股票代码列表
        """
        try:
            logger.info("获取A股股票池...")
            
            # 获取所有股票代码
            stock_list_df = self.data_source.get_stock_list()
            
            if stock_list_df.empty:
                logger.error("获取股票列表失败")
                return []
            
            # 过滤掉ST、退市等股票
            filtered_stocks = stock_list_df[
                ~stock_list_df['short_name'].str.contains('ST|退市|*ST', na=False)
            ]
            
            # 只保留主板、中小板、创业板
            main_stocks = filtered_stocks[
                (filtered_stocks['stock_code'].str.startswith('000')) |  # 深圳主板
                (filtered_stocks['stock_code'].str.startswith('001')) |  # 深圳主板
                (filtered_stocks['stock_code'].str.startswith('002')) |  # 中小板
                (filtered_stocks['stock_code'].str.startswith('300')) |  # 创业板
                (filtered_stocks['stock_code'].str.startswith('600')) |  # 上海主板
                (filtered_stocks['stock_code'].str.startswith('601')) |  # 上海主板
                (filtered_stocks['stock_code'].str.startswith('603')) |  # 上海主板
                (filtered_stocks['stock_code'].str.startswith('688'))    # 科创板
            ]
            
            stock_codes = main_stocks['stock_code'].tolist()
            
            # 如果设置了限制，随机选择
            if limit and len(stock_codes) > limit:
                import random
                stock_codes = random.sample(stock_codes, limit)
                logger.info(f"随机选择 {limit} 只股票进行测试")
            
            logger.info(f"获取到 {len(stock_codes)} 只股票")
            return stock_codes
            
        except Exception as e:
            logger.error(f"获取股票池失败: {e}")
            return []
    
    def download_stock_data(self, stock_codes: List[str], 
                          start_date: str = None, 
                          end_date: str = None,
                          save_to_db: bool = True) -> Dict[str, pd.DataFrame]:
        """
        批量下载股票数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            save_to_db: 是否保存到数据库
            
        Returns:
            股票数据字典
        """
        try:
            # 设置默认日期范围（最近2年）
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=self.retention_days)).strftime('%Y-%m-%d')
            
            logger.info(f"开始下载 {len(stock_codes)} 只股票的数据: {start_date} 到 {end_date}")
            
            stock_data = {}
            success_count = 0
            failed_stocks = []
            
            for i, stock_code in enumerate(stock_codes):
                try:
                    logger.info(f"下载进度: {i+1}/{len(stock_codes)} - {stock_code}")
                    
                    # 获取行情数据
                    market_data = self.data_source.get_stock_market_data(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not market_data.empty:
                        # 获取股票基本信息
                        stock_info = self._get_stock_basic_info(stock_code)
                        
                        # 合并信息
                        market_data['stock_code'] = stock_code
                        if stock_info:
                            for key, value in stock_info.items():
                                if key not in market_data.columns:
                                    market_data[key] = value
                        
                        stock_data[stock_code] = market_data
                        success_count += 1
                        
                        # 保存到数据库
                        if save_to_db:
                            self._save_stock_data_to_db(stock_code, market_data, stock_info)
                        
                    else:
                        failed_stocks.append(stock_code)
                        logger.warning(f"股票 {stock_code} 数据为空")
                    
                    # 控制请求频率
                    time.sleep(self.data_source.config.get('request_interval', 0.1))
                    
                except Exception as e:
                    failed_stocks.append(stock_code)
                    logger.error(f"下载股票 {stock_code} 数据失败: {e}")
                    continue
            
            logger.info(f"数据下载完成: 成功 {success_count} 只，失败 {len(failed_stocks)} 只")
            if failed_stocks:
                logger.warning(f"失败的股票: {failed_stocks[:10]}...")  # 只显示前10个
            
            return stock_data
            
        except Exception as e:
            logger.error(f"批量下载股票数据失败: {e}")
            return {}
    
    def _get_stock_basic_info(self, stock_code: str) -> Dict:
        """获取股票基本信息"""
        try:
            info = {}
            
            # 获取行业信息
            industry_df = self.data_source.get_stock_industry(stock_code)
            if not industry_df.empty:
                info['industry'] = industry_df.iloc[0]['industry_name']
                info['sw_code'] = industry_df.iloc[0]['sw_code']
            
            # 获取股本信息（最新）
            shares_df = self.data_source.get_stock_shares(stock_code, is_history=False)
            if not shares_df.empty:
                latest_shares = shares_df.iloc[0]
                info['total_shares'] = latest_shares.get('total_shares', 0)
                info['list_a_shares'] = latest_shares.get('list_a_shares', 0)
            
            return info
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基本信息失败: {e}")
            return {}
    
    def _save_stock_data_to_db(self, stock_code: str, market_data: pd.DataFrame, stock_info: Dict):
        """保存股票数据到数据库"""
        try:
            # 保存股票基本信息
            if stock_info:
                stock_info_data = {
                    'stock_code': stock_code,
                    'short_name': stock_info.get('short_name', ''),
                    'exchange': stock_info.get('exchange', ''),
                    'industry': stock_info.get('industry', ''),
                    'total_shares': stock_info.get('total_shares', 0),
                    'list_a_shares': stock_info.get('list_a_shares', 0)
                }
                self.db_manager.insert_or_update('stock_info', stock_info_data, ['stock_code'])
            
            # 保存行情数据
            market_records = market_data.to_dict('records')
            for record in market_records:
                # 确保数据类型正确
                record['stock_code'] = stock_code
                record['trade_date'] = pd.to_datetime(record['trade_date']).date()
                
                # 插入或更新
                self.db_manager.insert_or_update(
                    'stock_market', 
                    record, 
                    ['stock_code', 'trade_date']
                )
            
            logger.debug(f"股票 {stock_code} 数据已保存到数据库")
            
        except Exception as e:
            logger.error(f"保存股票 {stock_code} 数据到数据库失败: {e}")
    
    def get_stock_data_from_db(self, stock_codes: List[str], 
                             start_date: str = None, 
                             end_date: str = None) -> Dict[str, pd.DataFrame]:
        """从数据库获取股票数据"""
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=self.retention_days)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            stock_data = {}
            
            for stock_code in stock_codes:
                query = """
                SELECT * FROM stock_market 
                WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date
                """
                
                df = self.db_manager.query_to_dataframe(query, (stock_code, start_date, end_date))
                
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                    stock_data[stock_code] = df
            
            logger.info(f"从数据库获取到 {len(stock_data)} 只股票的数据")
            return stock_data
            
        except Exception as e:
            logger.error(f"从数据库获取股票数据失败: {e}")
            return {}
    
    def update_stock_data(self, stock_codes: List[str] = None, 
                         force_update: bool = False) -> bool:
        """更新股票数据"""
        try:
            if stock_codes is None:
                stock_codes = self.get_stock_universe(limit=50)  # 测试时限制50只
            
            logger.info(f"开始更新 {len(stock_codes)} 只股票的数据")
            
            # 检查哪些股票需要更新
            stocks_to_update = []
            
            if force_update:
                stocks_to_update = stock_codes
            else:
                # 检查数据库中的最新日期
                for stock_code in stock_codes:
                    query = """
                    SELECT MAX(trade_date) as latest_date 
                    FROM stock_market 
                    WHERE stock_code = ?
                    """
                    result = self.db_manager.execute_query(query, (stock_code,))
                    
                    if not result or not result[0][0]:
                        stocks_to_update.append(stock_code)
                    else:
                        latest_date = datetime.strptime(result[0][0], '%Y-%m-%d')
                        if (datetime.now() - latest_date).days > 1:  # 超过1天未更新
                            stocks_to_update.append(stock_code)
            
            if stocks_to_update:
                logger.info(f"需要更新 {len(stocks_to_update)} 只股票")
                self.download_stock_data(stocks_to_update)
            else:
                logger.info("所有股票数据都是最新的")
            
            return True
            
        except Exception as e:
            logger.error(f"更新股票数据失败: {e}")
            return False
    
    def get_data_summary(self) -> Dict:
        """获取数据概览"""
        try:
            summary = {}
            
            # 股票数量
            query = "SELECT COUNT(DISTINCT stock_code) as stock_count FROM stock_market"
            result = self.db_manager.execute_query(query)
            summary['stock_count'] = result[0][0] if result else 0
            
            # 数据日期范围
            query = "SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_market"
            result = self.db_manager.execute_query(query)
            if result and result[0][0]:
                summary['date_range'] = {
                    'start': result[0][0],
                    'end': result[0][1]
                }
            
            # 总记录数
            query = "SELECT COUNT(*) as total_records FROM stock_market"
            result = self.db_manager.execute_query(query)
            summary['total_records'] = result[0][0] if result else 0
            
            return summary
            
        except Exception as e:
            logger.error(f"获取数据概览失败: {e}")
            return {}


def test_stock_data_manager():
    """测试股票数据管理器"""
    logger.info("开始测试股票数据管理器...")
    
    manager = StockDataManager()
    
    # 获取股票池（测试用，只获取5只）
    stock_codes = manager.get_stock_universe(limit=5)
    logger.info(f"测试股票池: {stock_codes}")
    
    # 下载数据（测试用，只下载最近30天）
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    stock_data = manager.download_stock_data(
        stock_codes=stock_codes,
        start_date=start_date,
        save_to_db=True
    )
    
    # 显示数据概览
    summary = manager.get_data_summary()
    logger.info(f"数据概览: {summary}")
    
    # 从数据库读取数据
    db_data = manager.get_stock_data_from_db(stock_codes, start_date)
    logger.info(f"从数据库读取到 {len(db_data)} 只股票的数据")
    
    logger.info("股票数据管理器测试完成")


if __name__ == "__main__":
    test_stock_data_manager()
