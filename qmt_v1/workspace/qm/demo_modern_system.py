#!/usr/bin/env python3
"""
现代化量化交易系统演示脚本
展示系统的完整功能和现代化界面
"""
import sys
import os
import time
import webbrowser
import threading
from datetime import datetime
from loguru import logger

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎨 现代化量化交易系统 v2.0 - 完整演示                      ║
║                                                              ║
║    ✨ 基于 fellou.ai 设计风格的现代化界面                     ║
║    🔧 20+ 量化因子工程                                        ║
║    📊 完整策略回测系统                                        ║
║    🤖 AI驱动的智能预测                                        ║
║    ⚡ 实时数据和定时调度                                      ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'flask', 'pandas', 'numpy', 'scikit-learn', 
        'xgboost', 'loguru', 'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def demo_quantitative_factors():
    """演示量化因子功能"""
    print("\n🔧 演示量化因子工程...")
    
    try:
        from features.quantitative_factors import QuantitativeFactors
        from utils.helpers import create_sample_data
        
        # 创建测试数据
        test_data = create_sample_data(num_days=100)
        
        # 创建量化因子计算器
        qf = QuantitativeFactors()
        
        # 显示可用因子分类
        categories = qf.get_factor_categories()
        print(f"  📊 可用因子分类: {list(categories.keys())}")
        
        # 计算动量因子
        factors_df = qf.calculate_factors(test_data, categories=['momentum'])
        factor_columns = [col for col in factors_df.columns if col.startswith('factor_')]
        print(f"  ⚡ 计算动量因子: {len(factor_columns)} 个")
        
        # 计算因子重要性
        factors_df['target'] = factors_df['close'].shift(-1)
        importance_df = qf.get_factor_importance(factors_df.dropna(), 'target')
        
        if not importance_df.empty:
            top_factors = importance_df.head(3)['factor'].tolist()
            print(f"  🏆 重要因子TOP3: {top_factors}")
        
        print("  ✅ 量化因子演示完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 量化因子演示失败: {e}")
        return False


def demo_backtest_engine():
    """演示回测引擎功能"""
    print("\n📊 演示策略回测...")
    
    try:
        from backtest.backtest_engine import BacktestEngine
        import pandas as pd
        import numpy as np
        
        # 创建回测引擎
        backtest_engine = BacktestEngine()
        
        # 生成测试数据
        stock_codes = ['000001', '000002']
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        price_data = {}
        for stock_code in stock_codes:
            np.random.seed(hash(stock_code) % 1000)
            prices = 10 + np.cumsum(np.random.randn(100) * 0.02)
            
            price_data[stock_code] = pd.DataFrame({
                'trade_date': dates,
                'close': prices,
                'open': prices * (1 + np.random.normal(0, 0.01, 100)),
                'high': prices * (1 + np.random.uniform(0, 0.03, 100)),
                'low': prices * (1 - np.random.uniform(0, 0.03, 100)),
                'volume': np.random.randint(1000000, 10000000, 100)
            })
        
        # 生成交易信号
        signals = []
        for i, date in enumerate(dates[::10]):  # 每10天一个信号
            for stock_code in stock_codes:
                if np.random.random() > 0.7:
                    signals.append({
                        'trade_date': date,
                        'stock_code': stock_code,
                        'signal': np.random.choice([1, -1]),
                        'confidence': np.random.uniform(0.6, 0.9)
                    })
        
        signals_df = pd.DataFrame(signals)
        
        # 运行回测
        results = backtest_engine.run_backtest(signals_df, price_data)
        
        if results:
            metrics = results['basic_metrics']
            print(f"  📈 总收益率: {metrics['total_return']:.2f}%")
            print(f"  📊 年化收益率: {metrics['annual_return']:.2f}%")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"  ⚖️  夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"  🎯 胜率: {results['trading_metrics']['win_rate']:.2f}%")
        
        print("  ✅ 策略回测演示完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 策略回测演示失败: {e}")
        return False


def demo_model_training():
    """演示模型训练功能"""
    print("\n🤖 演示模型训练...")
    
    try:
        from models.model_manager import ModelManager
        from features.feature_engine import FeatureEngine
        from utils.helpers import create_sample_data
        
        # 创建组件
        model_manager = ModelManager()
        feature_engine = FeatureEngine()
        
        # 准备训练数据
        training_data = create_sample_data(num_days=200)
        
        # 计算特征（包括量化因子）
        features_df = feature_engine.calculate_features(
            training_data, 
            feature_types=['basic', 'technical', 'quantitative'],
            factor_names=['momentum_10d', 'volume_ratio', 'bollinger_position']
        )
        
        print(f"  📊 特征数据形状: {features_df.shape}")
        
        # 训练模型
        factor_names = ['momentum_10d', 'volume_ratio', 'bollinger_position']
        result = model_manager.train_model(
            'xgboost_next_day_direction', 
            features_df,
            factor_names=factor_names
        )
        
        if result:
            metrics = result.get('validation_metrics', {})
            print(f"  🎯 训练准确率: {metrics.get('accuracy', 0):.4f}")
            print(f"  📈 AUC分数: {metrics.get('auc', 0):.4f}")
            print(f"  🔧 使用因子: {len(factor_names)} 个")
        
        print("  ✅ 模型训练演示完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型训练演示失败: {e}")
        return False


def start_web_interface():
    """启动Web界面"""
    print("\n🌐 启动现代化Web界面...")
    
    try:
        from web.app import app
        
        def open_browser():
            time.sleep(3)
            url = "http://127.0.0.1:5001"
            print(f"\n🔗 自动打开浏览器: {url}")
            webbrowser.open(url)
        
        # 启动浏览器线程
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        print("  🎨 现代化界面特性:")
        print("    • 深色主题设计")
        print("    • 动态渐变背景")
        print("    • 玻璃拟态卡片")
        print("    • 流畅动画效果")
        print("    • 响应式布局")
        print("    • 智能通知系统")
        
        print("\n  📱 访问地址:")
        print("    • 现代化界面: http://127.0.0.1:5001")
        print("    • 经典界面: http://127.0.0.1:5001/classic")
        
        print("\n  🎯 界面功能:")
        print("    • 量化因子工程管理")
        print("    • 模型训练和监控")
        print("    • 策略回测分析")
        print("    • 实时预测系统")
        print("    • 定时调度管理")
        
        print("\n" + "="*60)
        print("🚀 Web界面已启动，按 Ctrl+C 停止演示")
        print("="*60)
        
        # 启动Flask应用
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 演示结束")
    except Exception as e:
        print(f"  ❌ Web界面启动失败: {e}")


def main():
    """主演示函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    print(f"\n⏰ 演示开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 功能演示
    demos = [
        ("量化因子工程", demo_quantitative_factors),
        ("策略回测系统", demo_backtest_engine),
        ("模型训练管理", demo_model_training),
    ]
    
    success_count = 0
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
            time.sleep(1)  # 演示间隔
        except Exception as e:
            print(f"  ❌ {demo_name} 演示异常: {e}")
    
    print(f"\n📊 演示总结:")
    print(f"  ✅ 成功: {success_count}/{len(demos)} 个功能")
    print(f"  🎯 系统完整性: {success_count/len(demos)*100:.1f}%")
    
    if success_count == len(demos):
        print("\n🎉 所有功能演示成功！")
        print("现在启动现代化Web界面进行完整体验...")
        time.sleep(2)
        start_web_interface()
    else:
        print(f"\n⚠️  有 {len(demos)-success_count} 个功能演示失败")
        print("请检查系统配置和依赖")
        
        choice = input("\n是否仍要启动Web界面？(y/n): ")
        if choice.lower() == 'y':
            start_web_interface()
    
    print("\n🙏 感谢体验现代化量化交易系统！")


if __name__ == "__main__":
    main()
