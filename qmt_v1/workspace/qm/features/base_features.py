"""
基础特征模块 - 计算基础价格和成交量特征
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from loguru import logger


class BaseFeatures:
    """基础特征计算类"""
    
    def __init__(self):
        """初始化基础特征计算器"""
        pass
    
    def calculate_returns(self, df: pd.DataFrame, periods: List[int] = [1, 3, 5]) -> pd.DataFrame:
        """
        计算收益率特征
        
        Args:
            df: 包含价格数据的DataFrame
            periods: 计算收益率的周期列表
            
        Returns:
            包含收益率特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            for period in periods:
                # 计算收益率
                result_df[f'return_{period}d'] = result_df['close'].pct_change(periods=period)
                
                # 计算对数收益率
                result_df[f'log_return_{period}d'] = np.log(result_df['close'] / result_df['close'].shift(period))
                
                logger.debug(f"计算 {period} 日收益率特征完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算收益率特征失败: {e}")
            return df
    
    def calculate_volatility(self, df: pd.DataFrame, windows: List[int] = [5, 10, 20]) -> pd.DataFrame:
        """
        计算波动率特征
        
        Args:
            df: 包含价格数据的DataFrame
            windows: 计算波动率的窗口列表
            
        Returns:
            包含波动率特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 先计算日收益率
            if 'return_1d' not in result_df.columns:
                result_df['return_1d'] = result_df['close'].pct_change()
            
            for window in windows:
                # 计算滚动标准差（波动率）
                result_df[f'volatility_{window}d'] = result_df['return_1d'].rolling(window=window).std()
                
                # 计算年化波动率
                result_df[f'volatility_{window}d_annualized'] = result_df[f'volatility_{window}d'] * np.sqrt(252)
                
                logger.debug(f"计算 {window} 日波动率特征完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算波动率特征失败: {e}")
            return df
    
    def calculate_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算价格相关特征
        
        Args:
            df: 包含OHLC数据的DataFrame
            
        Returns:
            包含价格特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 价格位置特征
            result_df['price_position'] = (result_df['close'] - result_df['low']) / (result_df['high'] - result_df['low'])
            
            # 上影线和下影线
            result_df['upper_shadow'] = result_df['high'] - np.maximum(result_df['open'], result_df['close'])
            result_df['lower_shadow'] = np.minimum(result_df['open'], result_df['close']) - result_df['low']
            
            # 实体大小
            result_df['body_size'] = np.abs(result_df['close'] - result_df['open'])
            
            # 振幅
            result_df['amplitude'] = (result_df['high'] - result_df['low']) / result_df['close'].shift(1)
            
            # 缺口
            result_df['gap'] = (result_df['open'] - result_df['close'].shift(1)) / result_df['close'].shift(1)
            
            # 价格相对位置（相对于前N日最高最低价）
            for window in [5, 10, 20]:
                result_df[f'price_rank_{window}d'] = (
                    result_df['close'] - result_df['low'].rolling(window=window).min()
                ) / (
                    result_df['high'].rolling(window=window).max() - result_df['low'].rolling(window=window).min()
                )
            
            logger.debug("计算价格特征完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算价格特征失败: {e}")
            return df
    
    def calculate_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算成交量相关特征
        
        Args:
            df: 包含成交量数据的DataFrame
            
        Returns:
            包含成交量特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 成交量变化率
            for period in [1, 3, 5]:
                result_df[f'volume_change_{period}d'] = result_df['volume'].pct_change(periods=period)
            
            # 成交量移动平均
            for window in [5, 10, 20]:
                result_df[f'volume_ma_{window}d'] = result_df['volume'].rolling(window=window).mean()
                result_df[f'volume_ratio_{window}d'] = result_df['volume'] / result_df[f'volume_ma_{window}d']
            
            # 成交量相对强度
            result_df['volume_strength'] = result_df['volume'] / result_df['volume'].rolling(window=20).mean()
            
            # 价量关系
            result_df['price_volume_trend'] = (result_df['close'] - result_df['close'].shift(1)) * result_df['volume']
            
            # 成交额相关
            if 'amount' in result_df.columns:
                result_df['avg_price'] = result_df['amount'] / result_df['volume']
                result_df['amount_change_1d'] = result_df['amount'].pct_change()
                
                for window in [5, 10, 20]:
                    result_df[f'amount_ma_{window}d'] = result_df['amount'].rolling(window=window).mean()
                    result_df[f'amount_ratio_{window}d'] = result_df['amount'] / result_df[f'amount_ma_{window}d']
            
            logger.debug("计算成交量特征完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算成交量特征失败: {e}")
            return df
    
    def calculate_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算动量特征
        
        Args:
            df: 包含价格数据的DataFrame
            
        Returns:
            包含动量特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 动量指标
            for period in [5, 10, 20]:
                result_df[f'momentum_{period}d'] = result_df['close'] / result_df['close'].shift(period) - 1
            
            # 变化率
            for period in [1, 3, 5, 10]:
                result_df[f'roc_{period}d'] = (result_df['close'] - result_df['close'].shift(period)) / result_df['close'].shift(period) * 100
            
            # 累积收益
            result_df['cumulative_return'] = (1 + result_df['close'].pct_change()).cumprod() - 1
            
            # 最大回撤
            result_df['running_max'] = result_df['close'].expanding().max()
            result_df['drawdown'] = (result_df['close'] - result_df['running_max']) / result_df['running_max']
            
            # 连续上涨/下跌天数
            result_df['price_change'] = result_df['close'] - result_df['close'].shift(1)
            result_df['up_days'] = (result_df['price_change'] > 0).astype(int)
            result_df['down_days'] = (result_df['price_change'] < 0).astype(int)
            
            # 计算连续天数
            result_df['consecutive_up'] = result_df['up_days'].groupby((result_df['up_days'] != result_df['up_days'].shift()).cumsum()).cumsum()
            result_df['consecutive_down'] = result_df['down_days'].groupby((result_df['down_days'] != result_df['down_days'].shift()).cumsum()).cumsum()
            
            logger.debug("计算动量特征完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算动量特征失败: {e}")
            return df
    
    def calculate_all_base_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有基础特征
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有基础特征的DataFrame
        """
        try:
            logger.info("开始计算基础特征...")
            
            result_df = df.copy()
            
            # 确保数据按日期排序
            if 'trade_date' in result_df.columns:
                result_df = result_df.sort_values('trade_date').reset_index(drop=True)
            
            # 计算各类特征
            result_df = self.calculate_returns(result_df)
            result_df = self.calculate_volatility(result_df)
            result_df = self.calculate_price_features(result_df)
            result_df = self.calculate_volume_features(result_df)
            result_df = self.calculate_momentum_features(result_df)
            
            logger.info(f"基础特征计算完成，共生成 {len(result_df.columns) - len(df.columns)} 个新特征")
            return result_df
            
        except Exception as e:
            logger.error(f"计算基础特征失败: {e}")
            return df


# 测试函数
def test_base_features():
    """测试基础特征计算"""
    logger.info("开始测试基础特征计算...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': 10 + np.random.randn(100) * 0.5,
        'close': 10 + np.random.randn(100) * 0.5,
        'high': 10.5 + np.random.randn(100) * 0.3,
        'low': 9.5 + np.random.randn(100) * 0.3,
        'volume': 1000000 + np.random.randint(-200000, 200000, 100),
        'amount': 10000000 + np.random.randint(-2000000, 2000000, 100)
    })
    
    # 确保high >= max(open, close), low <= min(open, close)
    test_data['high'] = np.maximum(test_data['high'], np.maximum(test_data['open'], test_data['close']))
    test_data['low'] = np.minimum(test_data['low'], np.minimum(test_data['open'], test_data['close']))
    
    # 计算特征
    bf = BaseFeatures()
    result = bf.calculate_all_base_features(test_data)
    
    logger.info(f"测试完成，原始数据 {len(test_data.columns)} 列，特征数据 {len(result.columns)} 列")
    logger.info(f"新增特征: {list(set(result.columns) - set(test_data.columns))}")
    
    # 显示部分结果
    print(result[['trade_date', 'close', 'return_1d', 'volatility_5d', 'price_position']].head(10))


if __name__ == "__main__":
    test_base_features()
