"""
特征引擎模块 - 高度可扩展的特征管理和计算引擎
"""
import pandas as pd
import numpy as np
import yaml
import os
from typing import Dict, List, Optional, Any, Union, Callable
from loguru import logger
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, mutual_info_regression, f_regression
from sklearn.impute import SimpleImputer

from .base_features import BaseFeatures
from .technical_indicators import TechnicalIndicators
from .time_series_features import TimeSeriesFeatures
from .quantitative_factors import QuantitativeFactors


class FeatureEngine:
    """特征引擎 - 统一管理所有特征计算和处理"""
    
    def __init__(self, config_path: str = "config/features.yaml"):
        """
        初始化特征引擎
        
        Args:
            config_path: 特征配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # 初始化各个特征计算器
        self.base_features = BaseFeatures()
        self.technical_indicators = TechnicalIndicators()
        self.time_series_features = TimeSeriesFeatures()
        self.quantitative_factors = QuantitativeFactors(config_path)

        # 当前启用的因子列表
        self.enabled_factors = []
        self.factor_combinations = []
        
        # 预处理器
        self.scaler = None
        self.imputer = None
        self.feature_selector = None
        
        # 特征列表缓存
        self._feature_columns = None
        
    def _load_config(self) -> Dict:
        """加载特征配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载特征配置失败: {e}")
            return {}
    
    def get_enabled_features(self) -> Dict[str, List[str]]:
        """获取启用的特征列表"""
        enabled_features = {
            'basic': [],
            'technical': [],
            'time_series': []
        }
        
        try:
            features_config = self.config.get('features', {})
            
            # 基础特征
            basic_features = features_config.get('basic_features', [])
            for feature in basic_features:
                if feature.get('enabled', True):
                    enabled_features['basic'].append(feature['name'])
            
            # 收益率特征
            return_features = features_config.get('return_features', [])
            for feature in return_features:
                if feature.get('enabled', True):
                    enabled_features['basic'].append(feature['name'])
            
            # 波动率特征
            volatility_features = features_config.get('volatility_features', [])
            for feature in volatility_features:
                if feature.get('enabled', True):
                    enabled_features['basic'].append(feature['name'])
            
            # 技术指标特征
            tech_indicators = features_config.get('technical_indicators', {})
            for indicator_type, indicators in tech_indicators.items():
                for indicator in indicators:
                    if indicator.get('enabled', True):
                        enabled_features['technical'].append(indicator['name'])
            
            # 时序特征
            ts_features = features_config.get('time_series_features', {})
            for feature_type, features in ts_features.items():
                for feature in features:
                    if feature.get('enabled', True):
                        enabled_features['time_series'].append(feature['name'])
            
            return enabled_features
            
        except Exception as e:
            logger.error(f"获取启用特征列表失败: {e}")
            return enabled_features
    
    def calculate_features(self, df: pd.DataFrame,
                          feature_types: List[str] = ['basic', 'technical', 'time_series'],
                          factor_names: List[str] = None,
                          factor_categories: List[str] = None) -> pd.DataFrame:
        """
        计算特征

        Args:
            df: 输入数据DataFrame
            feature_types: 要计算的特征类型列表
            factor_names: 指定量化因子名称列表
            factor_categories: 指定量化因子分类列表

        Returns:
            包含特征的DataFrame
        """
        try:
            logger.info(f"开始计算特征，类型: {feature_types}")
            result_df = df.copy()

            # 确保数据按日期排序
            if 'trade_date' in result_df.columns:
                result_df = result_df.sort_values('trade_date').reset_index(drop=True)

            original_columns = len(result_df.columns)

            # 计算基础特征
            if 'basic' in feature_types:
                logger.info("计算基础特征...")
                result_df = self.base_features.calculate_all_base_features(result_df)

            # 计算技术指标
            if 'technical' in feature_types:
                logger.info("计算技术指标...")
                result_df = self.technical_indicators.calculate_all_technical_indicators(result_df)

            # 计算时序特征
            if 'time_series' in feature_types:
                logger.info("计算时序特征...")
                result_df = self.time_series_features.calculate_all_time_series_features(result_df)

            # 计算量化因子
            if 'quantitative' in feature_types or factor_names or factor_categories:
                logger.info("计算量化因子...")
                result_df = self.quantitative_factors.calculate_factors(
                    result_df,
                    factor_names=factor_names,
                    categories=factor_categories
                )

                # 更新启用的因子列表
                if factor_names:
                    self.enabled_factors = factor_names
                elif factor_categories:
                    self.enabled_factors = []
                    for category in factor_categories:
                        if category in self.quantitative_factors.factor_categories:
                            self.enabled_factors.extend(self.quantitative_factors.factor_categories[category])
                else:
                    self.enabled_factors = self.quantitative_factors.get_enabled_factors()

            new_features = len(result_df.columns) - original_columns
            logger.info(f"特征计算完成，新增 {new_features} 个特征")

            return result_df

        except Exception as e:
            logger.error(f"计算特征失败: {e}")
            return df
    
    def preprocess_features(self, df: pd.DataFrame, 
                           fit: bool = True,
                           target_column: str = None) -> pd.DataFrame:
        """
        预处理特征
        
        Args:
            df: 输入数据DataFrame
            fit: 是否拟合预处理器
            target_column: 目标列名（用于特征选择）
            
        Returns:
            预处理后的DataFrame
        """
        try:
            logger.info("开始特征预处理...")
            result_df = df.copy()
            
            # 获取数值特征列
            numeric_columns = result_df.select_dtypes(include=[np.number]).columns.tolist()
            
            # 排除非特征列
            exclude_columns = ['stock_code', 'trade_date', 'year', 'month', 'day']
            if target_column:
                exclude_columns.append(target_column)
            
            feature_columns = [col for col in numeric_columns if col not in exclude_columns]
            
            if not feature_columns:
                logger.warning("没有找到可用的特征列")
                return result_df
            
            # 处理缺失值
            preprocessing_config = self.config.get('preprocessing', {})
            if preprocessing_config.get('fill_missing', True):
                if fit or self.imputer is None:
                    fill_method = preprocessing_config.get('fill_method', 'forward')
                    if fill_method == 'forward':
                        result_df[feature_columns] = result_df[feature_columns].fillna(method='ffill')
                    elif fill_method == 'backward':
                        result_df[feature_columns] = result_df[feature_columns].fillna(method='bfill')
                    else:
                        strategy = 'mean' if fill_method == 'mean' else 'median'
                        self.imputer = SimpleImputer(strategy=strategy)
                        result_df[feature_columns] = self.imputer.fit_transform(result_df[feature_columns])
                else:
                    result_df[feature_columns] = self.imputer.transform(result_df[feature_columns])
            
            # 标准化
            if preprocessing_config.get('standardize', True):
                if fit or self.scaler is None:
                    self.scaler = StandardScaler()
                    result_df[feature_columns] = self.scaler.fit_transform(result_df[feature_columns])
                else:
                    result_df[feature_columns] = self.scaler.transform(result_df[feature_columns])
            
            # 特征选择
            feature_selection_config = self.config.get('feature_selection', {})
            if feature_selection_config.get('enabled', True) and target_column and target_column in result_df.columns:
                if fit or self.feature_selector is None:
                    max_features = feature_selection_config.get('max_features', 50)
                    method = feature_selection_config.get('method', 'mutual_info')
                    
                    # 准备数据
                    X = result_df[feature_columns].fillna(0)
                    y = result_df[target_column].fillna(0)
                    
                    # 选择特征选择方法
                    if method == 'mutual_info':
                        score_func = mutual_info_regression
                    else:
                        score_func = f_regression
                    
                    self.feature_selector = SelectKBest(score_func=score_func, k=min(max_features, len(feature_columns)))
                    X_selected = self.feature_selector.fit_transform(X, y)
                    
                    # 获取选中的特征列名
                    selected_features = [feature_columns[i] for i in self.feature_selector.get_support(indices=True)]
                    self._feature_columns = selected_features
                    
                    logger.info(f"特征选择完成，从 {len(feature_columns)} 个特征中选择了 {len(selected_features)} 个")
                else:
                    if self._feature_columns:
                        X = result_df[self._feature_columns].fillna(0)
                        X_selected = self.feature_selector.transform(X)
                        selected_features = self._feature_columns
                    else:
                        selected_features = feature_columns
                
                # 更新DataFrame
                if self._feature_columns:
                    # 保留非特征列
                    non_feature_columns = [col for col in result_df.columns if col not in feature_columns]
                    result_df = result_df[non_feature_columns + self._feature_columns]
            
            logger.info("特征预处理完成")
            return result_df
            
        except Exception as e:
            logger.error(f"特征预处理失败: {e}")
            return df
    
    def get_feature_importance(self, df: pd.DataFrame, target_column: str) -> pd.DataFrame:
        """
        获取特征重要性
        
        Args:
            df: 包含特征和目标的DataFrame
            target_column: 目标列名
            
        Returns:
            特征重要性DataFrame
        """
        try:
            from sklearn.ensemble import RandomForestRegressor
            
            # 获取特征列
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            exclude_columns = ['stock_code', 'trade_date', 'year', 'month', 'day', target_column]
            feature_columns = [col for col in numeric_columns if col not in exclude_columns]
            
            if not feature_columns:
                logger.warning("没有找到可用的特征列")
                return pd.DataFrame()
            
            # 准备数据
            X = df[feature_columns].fillna(0)
            y = df[target_column].fillna(0)
            
            # 训练随机森林获取特征重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            
            # 创建特征重要性DataFrame
            importance_df = pd.DataFrame({
                'feature': feature_columns,
                'importance': rf.feature_importances_
            }).sort_values('importance', ascending=False)
            
            logger.info(f"计算了 {len(feature_columns)} 个特征的重要性")
            return importance_df
            
        except Exception as e:
            logger.error(f"计算特征重要性失败: {e}")
            return pd.DataFrame()
    
    def save_features(self, df: pd.DataFrame, file_path: str):
        """保存特征数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 根据文件扩展名选择保存格式
            if file_path.endswith('.csv'):
                df.to_csv(file_path, index=False)
            elif file_path.endswith('.parquet'):
                df.to_parquet(file_path, index=False)
            elif file_path.endswith('.pkl'):
                df.to_pickle(file_path)
            else:
                df.to_csv(file_path + '.csv', index=False)
            
            logger.info(f"特征数据已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存特征数据失败: {e}")
    
    def load_features(self, file_path: str) -> pd.DataFrame:
        """加载特征数据"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith('.parquet'):
                df = pd.read_parquet(file_path)
            elif file_path.endswith('.pkl'):
                df = pd.read_pickle(file_path)
            else:
                df = pd.read_csv(file_path)
            
            logger.info(f"从 {file_path} 加载了 {len(df)} 行特征数据")
            return df

        except Exception as e:
            logger.error(f"加载特征数据失败: {e}")
            return pd.DataFrame()

    # ==================== 量化因子管理 ====================

    def get_available_factors(self) -> Dict[str, List[str]]:
        """获取可用的量化因子"""
        return self.quantitative_factors.get_factor_categories()

    def get_enabled_factors(self) -> List[str]:
        """获取当前启用的因子"""
        return self.enabled_factors

    def set_factor_combination(self, factor_names: List[str], combination_name: str = None):
        """设置因子组合"""
        self.enabled_factors = factor_names

        if combination_name:
            # 保存因子组合
            combination = {
                'name': combination_name,
                'factors': factor_names,
                'created_at': pd.Timestamp.now().isoformat()
            }
            self.factor_combinations.append(combination)
            logger.info(f"保存因子组合: {combination_name} ({len(factor_names)} 个因子)")

    def get_factor_combinations(self) -> List[Dict]:
        """获取保存的因子组合"""
        return self.factor_combinations

    def enable_factor(self, factor_name: str):
        """启用单个因子"""
        self.quantitative_factors.enable_factor(factor_name)
        if factor_name not in self.enabled_factors:
            self.enabled_factors.append(factor_name)

    def disable_factor(self, factor_name: str):
        """禁用单个因子"""
        self.quantitative_factors.disable_factor(factor_name)
        if factor_name in self.enabled_factors:
            self.enabled_factors.remove(factor_name)

    def add_custom_factor(self, name: str, category: str, func: Callable, params: Dict = None):
        """添加自定义因子"""
        self.quantitative_factors.add_custom_factor(name, category, func, params)

    def calculate_factor_importance(self, df: pd.DataFrame, target_column: str,
                                  factor_names: List[str] = None) -> pd.DataFrame:
        """计算因子重要性"""
        return self.quantitative_factors.get_factor_importance(df, target_column, factor_names)

    def get_factor_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """获取因子统计信息"""
        try:
            factor_columns = [col for col in df.columns if col.startswith('factor_')]

            if not factor_columns:
                logger.warning("没有找到因子列")
                return pd.DataFrame()

            stats = []
            for col in factor_columns:
                factor_name = col.replace('factor_', '')
                series = df[col].dropna()

                if len(series) > 0:
                    stat = {
                        'factor': factor_name,
                        'count': len(series),
                        'mean': series.mean(),
                        'std': series.std(),
                        'min': series.min(),
                        'max': series.max(),
                        'skew': series.skew(),
                        'kurt': series.kurtosis(),
                        'null_ratio': df[col].isnull().sum() / len(df)
                    }
                    stats.append(stat)

            return pd.DataFrame(stats)

        except Exception as e:
            logger.error(f"计算因子统计信息失败: {e}")
            return pd.DataFrame()


# 测试函数
def test_feature_engine():
    """测试特征引擎"""
    logger.info("开始测试特征引擎...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 生成更真实的价格数据
    price = 10
    prices = [price]
    for i in range(99):
        price = price * (1 + np.random.normal(0, 0.02))
        prices.append(price)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
        'close': prices,
        'high': [p * (1 + np.random.uniform(0, 0.03)) for p in prices],
        'low': [p * (1 - np.random.uniform(0, 0.03)) for p in prices],
        'volume': 1000000 + np.random.randint(-200000, 200000, 100),
        'amount': [p * v for p, v in zip(prices, 1000000 + np.random.randint(-200000, 200000, 100))]
    })
    
    # 确保OHLC数据合理
    test_data['high'] = np.maximum(test_data['high'], np.maximum(test_data['open'], test_data['close']))
    test_data['low'] = np.minimum(test_data['low'], np.minimum(test_data['open'], test_data['close']))
    
    # 测试特征引擎
    fe = FeatureEngine()
    
    # 计算特征
    features_df = fe.calculate_features(test_data)
    logger.info(f"计算特征完成，从 {len(test_data.columns)} 列增加到 {len(features_df.columns)} 列")
    
    # 添加目标变量用于测试预处理
    features_df['target'] = features_df['close'].shift(-1)  # 下一日收盘价
    
    # 预处理特征
    processed_df = fe.preprocess_features(features_df, target_column='target')
    logger.info(f"特征预处理完成")
    
    # 计算特征重要性
    importance_df = fe.get_feature_importance(processed_df.dropna(), 'target')
    if not importance_df.empty:
        logger.info(f"特征重要性计算完成，前5个重要特征:")
        print(importance_df.head())
    
    logger.info("特征引擎测试完成")


if __name__ == "__main__":
    test_feature_engine()
