"""
量化因子计算器 (Quantitative Factors Calculator)
==============================================

本模块实现了20+个专业量化因子的计算，是量化交易系统v2.0的核心组件之一。

📊 主要功能:
- 6大类量化因子计算（动量、均值回归、成交量、波动率、趋势、技术信号）
- 支持动态因子注册和管理
- 因子重要性分析
- 因子相关性检测
- 高度可扩展的架构设计

🔧 因子分类:
1. 动量因子 (Momentum): 捕捉价格动量和趋势延续
2. 均值回归因子 (Mean Reversion): 识别价格偏离和回归机会
3. 成交量因子 (Volume): 分析量价关系和资金流向
4. 波动率因子 (Volatility): 衡量市场波动和风险
5. 趋势因子 (Trend): 识别趋势方向和强度
6. 技术信号因子 (Technical): 基于技术指标的交易信号

💡 设计理念:
- 模块化设计，每个因子独立计算
- 配置驱动，支持参数调整
- 高性能计算，优化pandas操作
- 易于扩展，支持自定义因子

作者: Augment Agent
日期: 2025-06-17
版本: v2.0
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable
from loguru import logger
import yaml
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class QuantitativeFactors:
    """量化因子计算器"""
    
    def __init__(self, config_path: str = "config/features.yaml"):
        """初始化量化因子计算器"""
        self.config_path = config_path
        self.config = self._load_config()
        
        # 因子注册表 - 支持动态添加新因子
        self.factor_registry = {}
        self._register_default_factors()
        
        # 因子分类
        self.factor_categories = {
            'momentum': ['momentum_5d', 'momentum_10d', 'momentum_20d', 'rsi_divergence'],
            'mean_reversion': ['bollinger_position', 'price_deviation', 'rsi_oversold', 'rsi_overbought'],
            'volume': ['volume_ratio', 'volume_price_trend', 'obv_trend', 'volume_momentum'],
            'volatility': ['volatility_ratio', 'atr_ratio', 'volatility_breakout'],
            'trend': ['trend_strength', 'ma_trend', 'macd_trend', 'adx_trend'],
            'value': ['pb_ratio', 'pe_ratio', 'market_cap_factor'],
            'quality': ['roe_trend', 'debt_ratio', 'profit_growth'],
            'technical': ['kdj_signal', 'macd_signal', 'bollinger_signal']
        }
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _register_default_factors(self):
        """注册默认因子"""
        # 动量因子
        self.register_factor('momentum_5d', self._momentum_factor, {'period': 5})
        self.register_factor('momentum_10d', self._momentum_factor, {'period': 10})
        self.register_factor('momentum_20d', self._momentum_factor, {'period': 20})
        self.register_factor('rsi_divergence', self._rsi_divergence_factor, {'period': 14})
        
        # 均值回归因子
        self.register_factor('bollinger_position', self._bollinger_position_factor, {'period': 20, 'std': 2})
        self.register_factor('price_deviation', self._price_deviation_factor, {'period': 20})
        self.register_factor('rsi_oversold', self._rsi_oversold_factor, {'period': 14, 'threshold': 30})
        self.register_factor('rsi_overbought', self._rsi_overbought_factor, {'period': 14, 'threshold': 70})
        
        # 成交量因子
        self.register_factor('volume_ratio', self._volume_ratio_factor, {'period': 20})
        self.register_factor('volume_price_trend', self._volume_price_trend_factor, {'period': 10})
        self.register_factor('obv_trend', self._obv_trend_factor, {'period': 10})
        self.register_factor('volume_momentum', self._volume_momentum_factor, {'period': 5})
        
        # 波动率因子
        self.register_factor('volatility_ratio', self._volatility_ratio_factor, {'short_period': 5, 'long_period': 20})
        self.register_factor('atr_ratio', self._atr_ratio_factor, {'period': 14})
        self.register_factor('volatility_breakout', self._volatility_breakout_factor, {'period': 20})
        
        # 趋势因子
        self.register_factor('trend_strength', self._trend_strength_factor, {'period': 20})
        self.register_factor('ma_trend', self._ma_trend_factor, {'short_period': 5, 'long_period': 20})
        self.register_factor('macd_trend', self._macd_trend_factor, {'fast': 12, 'slow': 26, 'signal': 9})
        
        # 技术信号因子
        self.register_factor('kdj_signal', self._kdj_signal_factor, {'period': 9})
        self.register_factor('macd_signal', self._macd_signal_factor, {'fast': 12, 'slow': 26, 'signal': 9})
        self.register_factor('bollinger_signal', self._bollinger_signal_factor, {'period': 20, 'std': 2})
    
    def register_factor(self, name: str, func: Callable, params: Dict = None):
        """
        注册新因子
        
        Args:
            name: 因子名称
            func: 因子计算函数
            params: 因子参数
        """
        self.factor_registry[name] = {
            'function': func,
            'params': params or {},
            'enabled': True
        }
        logger.info(f"注册因子: {name}")
    
    def calculate_factor(self, df: pd.DataFrame, factor_name: str, **kwargs) -> pd.Series:
        """
        计算单个因子
        
        Args:
            df: 股票数据
            factor_name: 因子名称
            **kwargs: 额外参数
            
        Returns:
            因子值序列
        """
        if factor_name not in self.factor_registry:
            raise ValueError(f"未知因子: {factor_name}")
        
        factor_info = self.factor_registry[factor_name]
        if not factor_info['enabled']:
            logger.warning(f"因子 {factor_name} 已禁用")
            return pd.Series(index=df.index, dtype=float)
        
        # 合并参数
        params = factor_info['params'].copy()
        params.update(kwargs)
        
        try:
            return factor_info['function'](df, **params)
        except Exception as e:
            logger.error(f"计算因子 {factor_name} 失败: {e}")
            return pd.Series(index=df.index, dtype=float)
    
    def calculate_factors(self, df: pd.DataFrame, 
                         factor_names: List[str] = None,
                         categories: List[str] = None) -> pd.DataFrame:
        """
        批量计算因子
        
        Args:
            df: 股票数据
            factor_names: 指定因子名称列表
            categories: 指定因子分类列表
            
        Returns:
            包含因子的DataFrame
        """
        result_df = df.copy()
        
        # 确定要计算的因子
        factors_to_calculate = []
        
        if factor_names:
            factors_to_calculate.extend(factor_names)
        
        if categories:
            for category in categories:
                if category in self.factor_categories:
                    factors_to_calculate.extend(self.factor_categories[category])
        
        # 如果没有指定，计算所有启用的因子
        if not factors_to_calculate:
            factors_to_calculate = [name for name, info in self.factor_registry.items() 
                                  if info['enabled']]
        
        # 去重
        factors_to_calculate = list(set(factors_to_calculate))
        
        logger.info(f"计算 {len(factors_to_calculate)} 个量化因子")
        
        for factor_name in factors_to_calculate:
            try:
                factor_values = self.calculate_factor(df, factor_name)
                result_df[f'factor_{factor_name}'] = factor_values
                logger.debug(f"计算因子 {factor_name} 完成")
            except Exception as e:
                logger.error(f"计算因子 {factor_name} 失败: {e}")
                continue
        
        return result_df
    
    # ==================== 动量因子 ====================
    
    def _momentum_factor(self, df: pd.DataFrame, period: int = 10) -> pd.Series:
        """动量因子：价格动量"""
        return (df['close'] / df['close'].shift(period) - 1) * 100
    
    def _rsi_divergence_factor(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """RSI背离因子"""
        # 计算RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 计算价格和RSI的相关性（背离信号）
        price_change = df['close'].pct_change(period)
        rsi_change = rsi.diff(period)
        
        # 背离信号：价格上涨但RSI下跌，或价格下跌但RSI上涨
        divergence = -(price_change * rsi_change)  # 负相关表示背离
        return divergence.fillna(0)
    
    # ==================== 均值回归因子 ====================
    
    def _bollinger_position_factor(self, df: pd.DataFrame, period: int = 20, std: float = 2) -> pd.Series:
        """布林带位置因子"""
        ma = df['close'].rolling(window=period).mean()
        std_dev = df['close'].rolling(window=period).std()
        upper_band = ma + (std_dev * std)
        lower_band = ma - (std_dev * std)
        
        # 计算价格在布林带中的位置 (0-1)
        position = (df['close'] - lower_band) / (upper_band - lower_band)
        return position.fillna(0.5)
    
    def _price_deviation_factor(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """价格偏离因子"""
        ma = df['close'].rolling(window=period).mean()
        std_dev = df['close'].rolling(window=period).std()
        
        # 标准化偏离度
        deviation = (df['close'] - ma) / std_dev
        return deviation.fillna(0)
    
    def _rsi_oversold_factor(self, df: pd.DataFrame, period: int = 14, threshold: float = 30) -> pd.Series:
        """RSI超卖因子"""
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 超卖信号强度
        oversold_strength = np.where(rsi < threshold, (threshold - rsi) / threshold, 0)
        return pd.Series(oversold_strength, index=df.index)
    
    def _rsi_overbought_factor(self, df: pd.DataFrame, period: int = 14, threshold: float = 70) -> pd.Series:
        """RSI超买因子"""
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 超买信号强度
        overbought_strength = np.where(rsi > threshold, (rsi - threshold) / (100 - threshold), 0)
        return pd.Series(overbought_strength, index=df.index)
    
    # ==================== 成交量因子 ====================
    
    def _volume_ratio_factor(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """成交量比率因子"""
        volume_ma = df['volume'].rolling(window=period).mean()
        volume_ratio = df['volume'] / volume_ma
        return volume_ratio.fillna(1)
    
    def _volume_price_trend_factor(self, df: pd.DataFrame, period: int = 10) -> pd.Series:
        """量价趋势因子"""
        price_change = df['close'].pct_change()
        volume_change = df['volume'].pct_change()
        
        # 计算量价相关性
        correlation = price_change.rolling(window=period).corr(volume_change)
        return correlation.fillna(0)
    
    def _obv_trend_factor(self, df: pd.DataFrame, period: int = 10) -> pd.Series:
        """OBV趋势因子"""
        # 计算OBV
        price_change = df['close'].diff()
        volume_direction = np.where(price_change > 0, df['volume'], 
                                  np.where(price_change < 0, -df['volume'], 0))
        obv = volume_direction.cumsum()
        
        # OBV趋势
        obv_trend = obv.diff(period) / obv.rolling(window=period).std()
        return obv_trend.fillna(0)
    
    def _volume_momentum_factor(self, df: pd.DataFrame, period: int = 5) -> pd.Series:
        """成交量动量因子"""
        volume_momentum = df['volume'].pct_change(period)
        return volume_momentum.fillna(0)
    
    # ==================== 波动率因子 ====================
    
    def _volatility_ratio_factor(self, df: pd.DataFrame, short_period: int = 5, long_period: int = 20) -> pd.Series:
        """波动率比率因子"""
        short_vol = df['close'].pct_change().rolling(window=short_period).std()
        long_vol = df['close'].pct_change().rolling(window=long_period).std()
        vol_ratio = short_vol / long_vol
        return vol_ratio.fillna(1)
    
    def _atr_ratio_factor(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """ATR比率因子"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=period).mean()
        
        # ATR相对于价格的比率
        atr_ratio = atr / df['close']
        return atr_ratio.fillna(0)
    
    def _volatility_breakout_factor(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """波动率突破因子"""
        volatility = df['close'].pct_change().rolling(window=period).std()
        vol_ma = volatility.rolling(window=period).mean()
        vol_std = volatility.rolling(window=period).std()
        
        # 波动率突破信号
        breakout = (volatility - vol_ma) / vol_std
        return breakout.fillna(0)
    
    # ==================== 趋势因子 ====================
    
    def _trend_strength_factor(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """趋势强度因子"""
        # 使用线性回归斜率衡量趋势强度
        def calculate_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            slope = np.polyfit(x, y, 1)[0]
            return slope
        
        trend_strength = df['close'].rolling(window=period).apply(calculate_slope)
        return trend_strength.fillna(0)
    
    def _ma_trend_factor(self, df: pd.DataFrame, short_period: int = 5, long_period: int = 20) -> pd.Series:
        """均线趋势因子"""
        short_ma = df['close'].rolling(window=short_period).mean()
        long_ma = df['close'].rolling(window=long_period).mean()
        
        # 均线趋势信号
        ma_trend = (short_ma - long_ma) / long_ma
        return ma_trend.fillna(0)
    
    def _macd_trend_factor(self, df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """MACD趋势因子"""
        ema_fast = df['close'].ewm(span=fast).mean()
        ema_slow = df['close'].ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        
        # MACD趋势信号
        macd_trend = macd - signal_line
        return macd_trend.fillna(0)
    
    # ==================== 技术信号因子 ====================
    
    def _kdj_signal_factor(self, df: pd.DataFrame, period: int = 9) -> pd.Series:
        """KDJ信号因子"""
        low_min = df['low'].rolling(window=period).min()
        high_max = df['high'].rolling(window=period).max()
        
        rsv = (df['close'] - low_min) / (high_max - low_min) * 100
        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean()
        j = 3 * k - 2 * d
        
        # KDJ信号强度
        kdj_signal = (k - d) / 100  # 标准化
        return kdj_signal.fillna(0)
    
    def _macd_signal_factor(self, df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """MACD信号因子"""
        ema_fast = df['close'].ewm(span=fast).mean()
        ema_slow = df['close'].ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        
        # MACD信号强度
        return histogram.fillna(0)
    
    def _bollinger_signal_factor(self, df: pd.DataFrame, period: int = 20, std: float = 2) -> pd.Series:
        """布林带信号因子"""
        ma = df['close'].rolling(window=period).mean()
        std_dev = df['close'].rolling(window=period).std()
        upper_band = ma + (std_dev * std)
        lower_band = ma - (std_dev * std)
        
        # 布林带突破信号
        upper_break = np.where(df['close'] > upper_band, 1, 0)
        lower_break = np.where(df['close'] < lower_band, -1, 0)
        
        bollinger_signal = upper_break + lower_break
        return pd.Series(bollinger_signal, index=df.index)

    def get_factor_importance(self, df: pd.DataFrame, target_column: str,
                            factor_names: List[str] = None) -> pd.DataFrame:
        """计算因子重要性"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.feature_selection import mutual_info_regression

            # 获取因子列
            if factor_names:
                factor_columns = [f'factor_{name}' for name in factor_names if f'factor_{name}' in df.columns]
            else:
                factor_columns = [col for col in df.columns if col.startswith('factor_')]

            if not factor_columns:
                logger.warning("没有找到因子列")
                return pd.DataFrame()

            # 准备数据
            X = df[factor_columns].fillna(0)
            y = df[target_column].fillna(0)

            # 随机森林重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            rf_importance = rf.feature_importances_

            # 互信息重要性
            mi_importance = mutual_info_regression(X, y, random_state=42)

            # 创建重要性DataFrame
            importance_df = pd.DataFrame({
                'factor': [col.replace('factor_', '') for col in factor_columns],
                'rf_importance': rf_importance,
                'mi_importance': mi_importance
            })

            # 计算综合重要性
            importance_df['combined_importance'] = (
                importance_df['rf_importance'] * 0.6 +
                importance_df['mi_importance'] * 0.4
            )

            importance_df = importance_df.sort_values('combined_importance', ascending=False)

            return importance_df

        except Exception as e:
            logger.error(f"计算因子重要性失败: {e}")
            return pd.DataFrame()

    def get_enabled_factors(self) -> List[str]:
        """获取启用的因子列表"""
        return [name for name, info in self.factor_registry.items() if info['enabled']]

    def enable_factor(self, factor_name: str):
        """启用因子"""
        if factor_name in self.factor_registry:
            self.factor_registry[factor_name]['enabled'] = True
            logger.info(f"启用因子: {factor_name}")

    def disable_factor(self, factor_name: str):
        """禁用因子"""
        if factor_name in self.factor_registry:
            self.factor_registry[factor_name]['enabled'] = False
            logger.info(f"禁用因子: {factor_name}")

    def get_factor_categories(self) -> Dict[str, List[str]]:
        """获取因子分类"""
        return self.factor_categories

    def add_custom_factor(self, name: str, category: str, func: Callable, params: Dict = None):
        """添加自定义因子"""
        self.register_factor(name, func, params)

        # 添加到分类
        if category not in self.factor_categories:
            self.factor_categories[category] = []

        if name not in self.factor_categories[category]:
            self.factor_categories[category].append(name)

        logger.info(f"添加自定义因子: {name} (分类: {category})")


def test_quantitative_factors():
    """测试量化因子"""
    logger.info("开始测试量化因子...")

    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)

    test_data = pd.DataFrame({
        'trade_date': dates,
        'open': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'high': 100 + np.cumsum(np.random.randn(100) * 0.5) + np.random.rand(100) * 2,
        'low': 100 + np.cumsum(np.random.randn(100) * 0.5) - np.random.rand(100) * 2,
        'close': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'volume': np.random.randint(1000000, 10000000, 100)
    })

    # 确保OHLC数据合理
    test_data['high'] = np.maximum(test_data['high'], np.maximum(test_data['open'], test_data['close']))
    test_data['low'] = np.minimum(test_data['low'], np.minimum(test_data['open'], test_data['close']))

    # 测试量化因子
    qf = QuantitativeFactors()

    # 计算所有因子
    factors_df = qf.calculate_factors(test_data)
    logger.info(f"计算因子完成，从 {len(test_data.columns)} 列增加到 {len(factors_df.columns)} 列")

    # 显示因子列
    factor_columns = [col for col in factors_df.columns if col.startswith('factor_')]
    logger.info(f"计算的因子: {factor_columns}")

    # 测试因子重要性
    factors_df['target'] = factors_df['close'].shift(-1)  # 下一日收盘价
    importance_df = qf.get_factor_importance(factors_df.dropna(), 'target')

    if not importance_df.empty:
        logger.info("因子重要性排序:")
        print(importance_df.head(10))

    logger.info("量化因子测试完成")


if __name__ == "__main__":
    test_quantitative_factors()
    
    def get_factor_importance(self, df: pd.DataFrame, target_column: str, 
                            factor_names: List[str] = None) -> pd.DataFrame:
        """
        计算因子重要性
        
        Args:
            df: 包含因子和目标变量的数据
            target_column: 目标变量列名
            factor_names: 指定因子名称列表
            
        Returns:
            因子重要性DataFrame
        """
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.feature_selection import mutual_info_regression
            
            # 获取因子列
            if factor_names:
                factor_columns = [f'factor_{name}' for name in factor_names if f'factor_{name}' in df.columns]
            else:
                factor_columns = [col for col in df.columns if col.startswith('factor_')]
            
            if not factor_columns:
                logger.warning("没有找到因子列")
                return pd.DataFrame()
            
            # 准备数据
            X = df[factor_columns].fillna(0)
            y = df[target_column].fillna(0)
            
            # 随机森林重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            rf_importance = rf.feature_importances_
            
            # 互信息重要性
            mi_importance = mutual_info_regression(X, y, random_state=42)
            
            # 创建重要性DataFrame
            importance_df = pd.DataFrame({
                'factor': [col.replace('factor_', '') for col in factor_columns],
                'rf_importance': rf_importance,
                'mi_importance': mi_importance
            })
            
            # 计算综合重要性
            importance_df['combined_importance'] = (
                importance_df['rf_importance'] * 0.6 + 
                importance_df['mi_importance'] * 0.4
            )
            
            importance_df = importance_df.sort_values('combined_importance', ascending=False)
            
            return importance_df
            
        except Exception as e:
            logger.error(f"计算因子重要性失败: {e}")
            return pd.DataFrame()


def test_quantitative_factors():
    """测试量化因子"""
    logger.info("开始测试量化因子...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'open': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'high': 100 + np.cumsum(np.random.randn(100) * 0.5) + np.random.rand(100) * 2,
        'low': 100 + np.cumsum(np.random.randn(100) * 0.5) - np.random.rand(100) * 2,
        'close': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'volume': np.random.randint(1000000, 10000000, 100)
    })
    
    # 确保OHLC数据合理
    test_data['high'] = np.maximum(test_data['high'], np.maximum(test_data['open'], test_data['close']))
    test_data['low'] = np.minimum(test_data['low'], np.minimum(test_data['open'], test_data['close']))
    
    # 测试量化因子
    qf = QuantitativeFactors()
    
    # 计算所有因子
    factors_df = qf.calculate_factors(test_data)
    logger.info(f"计算因子完成，从 {len(test_data.columns)} 列增加到 {len(factors_df.columns)} 列")
    
    # 显示因子列
    factor_columns = [col for col in factors_df.columns if col.startswith('factor_')]
    logger.info(f"计算的因子: {factor_columns}")
    
    # 测试因子重要性
    factors_df['target'] = factors_df['close'].shift(-1)  # 下一日收盘价
    importance_df = qf.get_factor_importance(factors_df.dropna(), 'target')
    
    if not importance_df.empty:
        logger.info("因子重要性排序:")
        print(importance_df.head(10))
    
    logger.info("量化因子测试完成")


if __name__ == "__main__":
    test_quantitative_factors()
