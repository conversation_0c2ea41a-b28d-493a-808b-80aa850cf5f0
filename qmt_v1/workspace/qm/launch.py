#!/usr/bin/env python3
"""
量化交易系统启动器
支持现代化界面和经典界面
"""
import os
import sys
import webbrowser
import time
import threading

def main():
    """主启动函数"""
    print("🎨 量化交易系统 v2.0")
    print("=" * 40)
    print("1. 现代化界面 (推荐)")
    print("2. 经典界面")
    print("3. 完整演示")
    print("4. 退出")
    print("=" * 40)
    
    choice = input("请选择启动方式 (1-4): ").strip()
    
    if choice == "1":
        start_modern_ui()
    elif choice == "2":
        start_classic_ui()
    elif choice == "3":
        start_demo()
    elif choice == "4":
        print("👋 再见!")
        return
    else:
        print("❌ 无效选择")
        main()

def start_modern_ui():
    """启动现代化界面"""
    print("\n🚀 启动现代化界面...")
    os.system("python start_modern_ui.py")

def start_classic_ui():
    """启动经典界面"""
    print("\n🚀 启动经典界面...")
    
    def open_classic():
        time.sleep(2)
        webbrowser.open("http://127.0.0.1:5001/classic")
    
    threading.Thread(target=open_classic, daemon=True).start()
    os.system("python web/app.py")

def start_demo():
    """启动完整演示"""
    print("\n🎪 启动完整演示...")
    os.system("python demo_modern_system.py")

if __name__ == "__main__":
    main()
