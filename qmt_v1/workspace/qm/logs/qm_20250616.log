2025-06-16 23:24:50.718 | INFO     | utils.logger:setup_logger:44 - 日志系统初始化完成，日志级别: INFO
2025-06-16 23:24:50.719 | INFO     | utils.logger:setup_logger:45 - 日志文件: logs/qm_20250616.log
2025-06-16 23:24:50.719 | INFO     | __main__:main:238 - 量化交易系统启动，模式: test
2025-06-16 23:24:50.720 | INFO     | __main__:test_basic_functionality:26 - 开始测试基本功能...
2025-06-16 23:24:50.720 | INFO     | __main__:test_basic_functionality:30 - 1. 测试样本数据生成...
2025-06-16 23:24:50.730 | INFO     | __main__:test_basic_functionality:32 - 生成样本数据: 50 行
2025-06-16 23:24:50.750 | INFO     | __main__:test_basic_functionality:36 - 2. 测试数据库...
2025-06-16 23:24:50.773 | INFO     | data.database:_init_database:79 - 数据库初始化完成
2025-06-16 23:24:50.778 | INFO     | data.database:insert_market_data:161 - 插入 50 条行情数据
2025-06-16 23:24:50.785 | INFO     | __main__:test_basic_functionality:44 - 从数据库查询到 10 条数据
2025-06-16 23:24:50.785 | INFO     | __main__:test_basic_functionality:47 - 3. 测试特征工程...
2025-06-16 23:24:50.799 | INFO     | features.feature_engine:calculate_features:116 - 开始计算特征，类型: ['basic']
2025-06-16 23:24:50.800 | INFO     | features.feature_engine:calculate_features:127 - 计算基础特征...
2025-06-16 23:24:50.801 | INFO     | features.base_features:calculate_all_base_features:222 - 开始计算基础特征...
2025-06-16 23:24:50.824 | INFO     | features.base_features:calculate_all_base_features:237 - 基础特征计算完成，共生成 55 个新特征
2025-06-16 23:24:50.824 | INFO     | features.feature_engine:calculate_features:141 - 特征计算完成，新增 55 个特征
2025-06-16 23:24:50.824 | INFO     | __main__:test_basic_functionality:52 - 计算特征完成: 67 列
2025-06-16 23:24:50.824 | INFO     | __main__:test_basic_functionality:55 - 4. 测试模型...
2025-06-16 23:24:50.866 | INFO     | models.model_manager:train_model:68 - 开始训练模型: xgboost_next_day_direction
2025-06-16 23:24:50.866 | INFO     | models.xgboost_binary:train:164 - 开始训练XGBoost二分类模型...
2025-06-16 23:24:50.867 | INFO     | models.xgboost_binary:create_target_labels:71 - 创建目标标签: 明日开盘价相对今日收盘价涨跌
2025-06-16 23:24:50.869 | INFO     | models.xgboost_binary:create_target_labels:89 - 目标标签分布: {-1: np.int64(30), 1: np.int64(20)}
2025-06-16 23:24:50.870 | INFO     | models.xgboost_binary:create_target_labels:93 - 上涨概率: 0.400
2025-06-16 23:24:50.873 | INFO     | models.xgboost_binary:prepare_training_data:144 - 准备训练数据完成: 50 样本, 64 特征
2025-06-16 23:24:50.873 | INFO     | models.xgboost_binary:prepare_training_data:145 - 正样本比例: 0.400
2025-06-16 23:24:51.095 | ERROR    | models.xgboost_binary:train:234 - 模型训练失败: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/sklearn/model_selection/_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/sklearn.py", line 1599, in fit
    self._Booster = train(
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/training.py", line 182, in train
    if cb_container.after_iteration(bst, i, dtrain, evals):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in after_iteration
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in <genexpr>
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 446, in after_iteration
    raise ValueError(msg)
ValueError: Must have at least 1 validation dataset for early stopping.

2025-06-16 23:24:51.095 | ERROR    | models.model_manager:train_model:83 - 训练模型 xgboost_next_day_direction 失败: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/sklearn/model_selection/_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/sklearn.py", line 1599, in fit
    self._Booster = train(
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/training.py", line 182, in train
    if cb_container.after_iteration(bst, i, dtrain, evals):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in after_iteration
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in <genexpr>
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 446, in after_iteration
    raise ValueError(msg)
ValueError: Must have at least 1 validation dataset for early stopping.

2025-06-16 23:24:51.096 | ERROR    | __main__:test_basic_functionality:76 - 基本功能测试失败: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/sklearn/model_selection/_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/sklearn.py", line 1599, in fit
    self._Booster = train(
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/training.py", line 182, in train
    if cb_container.after_iteration(bst, i, dtrain, evals):
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in after_iteration
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 261, in <genexpr>
    ret = any(c.after_iteration(model, epoch, self.history) for c in self.callbacks)
  File "/Users/<USER>/opt/anaconda3/envs/qmt/lib/python3.9/site-packages/xgboost/callback.py", line 446, in after_iteration
    raise ValueError(msg)
ValueError: Must have at least 1 validation dataset for early stopping.

2025-06-16 23:24:51.097 | ERROR    | __main__:main:254 - 程序执行失败！
2025-06-16 23:26:01.376 | INFO     | utils.logger:setup_logger:44 - 日志系统初始化完成，日志级别: INFO
2025-06-16 23:26:01.376 | INFO     | utils.logger:setup_logger:45 - 日志文件: logs/qm_20250616.log
2025-06-16 23:26:01.377 | INFO     | __main__:main:238 - 量化交易系统启动，模式: test
2025-06-16 23:26:01.377 | INFO     | __main__:test_basic_functionality:26 - 开始测试基本功能...
2025-06-16 23:26:01.377 | INFO     | __main__:test_basic_functionality:30 - 1. 测试样本数据生成...
2025-06-16 23:26:01.380 | INFO     | __main__:test_basic_functionality:32 - 生成样本数据: 50 行
2025-06-16 23:26:01.389 | INFO     | __main__:test_basic_functionality:36 - 2. 测试数据库...
2025-06-16 23:26:01.402 | INFO     | data.database:_init_database:79 - 数据库初始化完成
2025-06-16 23:26:01.406 | INFO     | data.database:insert_market_data:161 - 插入 50 条行情数据
2025-06-16 23:26:01.410 | INFO     | __main__:test_basic_functionality:44 - 从数据库查询到 10 条数据
2025-06-16 23:26:01.410 | INFO     | __main__:test_basic_functionality:47 - 3. 测试特征工程...
2025-06-16 23:26:01.423 | INFO     | features.feature_engine:calculate_features:116 - 开始计算特征，类型: ['basic']
2025-06-16 23:26:01.424 | INFO     | features.feature_engine:calculate_features:127 - 计算基础特征...
2025-06-16 23:26:01.424 | INFO     | features.base_features:calculate_all_base_features:222 - 开始计算基础特征...
2025-06-16 23:26:01.441 | INFO     | features.base_features:calculate_all_base_features:237 - 基础特征计算完成，共生成 55 个新特征
2025-06-16 23:26:01.441 | INFO     | features.feature_engine:calculate_features:141 - 特征计算完成，新增 55 个特征
2025-06-16 23:26:01.441 | INFO     | __main__:test_basic_functionality:52 - 计算特征完成: 67 列
2025-06-16 23:26:01.441 | INFO     | __main__:test_basic_functionality:55 - 4. 测试模型...
2025-06-16 23:26:01.481 | INFO     | models.model_manager:train_model:68 - 开始训练模型: xgboost_next_day_direction
2025-06-16 23:26:01.481 | INFO     | models.xgboost_binary:train:164 - 开始训练XGBoost二分类模型...
2025-06-16 23:26:01.481 | INFO     | models.xgboost_binary:create_target_labels:71 - 创建目标标签: 明日开盘价相对今日收盘价涨跌
2025-06-16 23:26:01.483 | INFO     | models.xgboost_binary:create_target_labels:89 - 目标标签分布: {-1: np.int64(30), 1: np.int64(20)}
2025-06-16 23:26:01.483 | INFO     | models.xgboost_binary:create_target_labels:93 - 上涨概率: 0.400
2025-06-16 23:26:01.486 | INFO     | models.xgboost_binary:prepare_training_data:144 - 准备训练数据完成: 50 样本, 64 特征
2025-06-16 23:26:01.486 | INFO     | models.xgboost_binary:prepare_training_data:145 - 正样本比例: 0.400
2025-06-16 23:26:02.463 | INFO     | models.xgboost_binary:train:221 - 模型训练完成
2025-06-16 23:26:02.463 | INFO     | models.xgboost_binary:train:222 - 训练集AUC: 1.0000
2025-06-16 23:26:02.463 | INFO     | models.xgboost_binary:train:223 - 测试集AUC: 0.2083
2025-06-16 23:26:02.464 | INFO     | models.xgboost_binary:train:224 - 交叉验证AUC: 0.4250 ± 0.1453
2025-06-16 23:26:02.464 | ERROR    | models.model_manager:_save_training_result:431 - 保存训练结果失败: Object of type DataFrame is not JSON serializable
2025-06-16 23:26:02.464 | INFO     | models.model_manager:train_model:79 - 模型 xgboost_next_day_direction 训练完成
2025-06-16 23:26:02.465 | INFO     | __main__:test_basic_functionality:60 - 模型训练完成，验证AUC: 0.20833333333333334
2025-06-16 23:26:02.482 | INFO     | models.xgboost_binary:predict:272 - 预测完成，10 个样本
2025-06-16 23:26:02.483 | INFO     | models.model_manager:predict:142 - 模型 xgboost_next_day_direction 预测完成
2025-06-16 23:26:02.483 | INFO     | __main__:test_basic_functionality:64 - 预测完成: 10 行
2025-06-16 23:26:02.487 | INFO     | __main__:test_basic_functionality:72 - 基本功能测试完成！
2025-06-16 23:26:02.488 | INFO     | __main__:main:252 - 程序执行成功！
2025-06-16 23:26:09.795 | INFO     | utils.logger:setup_logger:44 - 日志系统初始化完成，日志级别: INFO
2025-06-16 23:26:09.796 | INFO     | utils.logger:setup_logger:45 - 日志文件: logs/qm_20250616.log
2025-06-16 23:26:09.796 | INFO     | __main__:main:238 - 量化交易系统启动，模式: train
2025-06-16 23:26:09.797 | INFO     | __main__:run_training_pipeline:116 - 开始运行训练流水线...
2025-06-16 23:26:09.797 | INFO     | __main__:run_training_pipeline:120 - 1. 准备训练数据...
2025-06-16 23:26:09.801 | INFO     | __main__:run_training_pipeline:124 - 2. 计算特征...
2025-06-16 23:26:09.815 | INFO     | features.feature_engine:calculate_features:116 - 开始计算特征，类型: ['basic', 'technical', 'time_series']
2025-06-16 23:26:09.816 | INFO     | features.feature_engine:calculate_features:127 - 计算基础特征...
2025-06-16 23:26:09.816 | INFO     | features.base_features:calculate_all_base_features:222 - 开始计算基础特征...
2025-06-16 23:26:09.835 | INFO     | features.base_features:calculate_all_base_features:237 - 基础特征计算完成，共生成 55 个新特征
2025-06-16 23:26:09.835 | INFO     | features.feature_engine:calculate_features:132 - 计算技术指标...
2025-06-16 23:26:09.835 | INFO     | features.technical_indicators:calculate_all_technical_indicators:363 - 开始计算技术指标...
2025-06-16 23:26:09.852 | INFO     | features.technical_indicators:calculate_all_technical_indicators:383 - 技术指标计算完成，共生成 45 个新指标
2025-06-16 23:26:09.852 | INFO     | features.feature_engine:calculate_features:137 - 计算时序特征...
2025-06-16 23:26:09.852 | INFO     | features.time_series_features:calculate_all_time_series_features:168 - 开始计算时序特征...
2025-06-16 23:26:09.871 | INFO     | features.time_series_features:calculate_all_time_series_features:181 - 时序特征计算完成，共生成 69 个新特征
2025-06-16 23:26:09.872 | INFO     | features.feature_engine:calculate_features:141 - 特征计算完成，新增 169 个特征
2025-06-16 23:26:09.872 | INFO     | __main__:run_training_pipeline:129 - 3. 训练所有模型...
2025-06-16 23:26:09.911 | INFO     | __main__:run_training_pipeline:138 - 训练模型: xgboost_10d_surge
2025-06-16 23:26:09.911 | INFO     | models.model_manager:train_model:68 - 开始训练模型: xgboost_10d_surge
2025-06-16 23:26:09.911 | INFO     | models.xgboost_classifier:train:184 - 开始训练XGBoost分类模型...
2025-06-16 23:26:09.912 | INFO     | models.xgboost_classifier:create_target_labels:73 - 创建目标标签: 10日拉升，日均涨幅>1.0%，拉升天数>=4天
2025-06-16 23:26:09.945 | INFO     | models.xgboost_classifier:create_target_labels:116 - 目标标签分布: {0: np.int64(185), 1: np.int64(15)}
2025-06-16 23:26:09.949 | INFO     | models.xgboost_classifier:prepare_training_data:164 - 准备训练数据完成: 200 样本, 172 特征
2025-06-16 23:26:09.950 | INFO     | models.xgboost_classifier:prepare_training_data:165 - 正样本比例: 0.075
2025-06-16 23:26:11.011 | INFO     | models.xgboost_classifier:train:241 - 模型训练完成
2025-06-16 23:26:11.012 | INFO     | models.xgboost_classifier:train:242 - 训练集AUC: 1.0000
2025-06-16 23:26:11.012 | INFO     | models.xgboost_classifier:train:243 - 测试集AUC: 1.0000
2025-06-16 23:26:11.012 | INFO     | models.xgboost_classifier:train:244 - 交叉验证AUC: 0.8865 ± 0.2270
2025-06-16 23:26:11.013 | ERROR    | models.model_manager:_save_training_result:431 - 保存训练结果失败: Object of type DataFrame is not JSON serializable
2025-06-16 23:26:11.013 | INFO     | models.model_manager:train_model:79 - 模型 xgboost_10d_surge 训练完成
2025-06-16 23:26:11.016 | INFO     | models.xgboost_classifier:save_model:341 - 模型已保存到: models/xgboost_10d_surge_20250616_232611
2025-06-16 23:26:11.016 | INFO     | models.model_manager:save_model:321 - 模型 xgboost_10d_surge 已保存到: models/xgboost_10d_surge_20250616_232611
2025-06-16 23:26:11.016 | INFO     | __main__:run_training_pipeline:138 - 训练模型: xgboost_next_day_direction
2025-06-16 23:26:11.016 | INFO     | models.model_manager:train_model:68 - 开始训练模型: xgboost_next_day_direction
2025-06-16 23:26:11.016 | INFO     | models.xgboost_binary:train:164 - 开始训练XGBoost二分类模型...
2025-06-16 23:26:11.017 | INFO     | models.xgboost_binary:create_target_labels:71 - 创建目标标签: 明日开盘价相对今日收盘价涨跌
2025-06-16 23:26:11.020 | INFO     | models.xgboost_binary:create_target_labels:89 - 目标标签分布: {-1: np.int64(103), 1: np.int64(97)}
2025-06-16 23:26:11.020 | INFO     | models.xgboost_binary:create_target_labels:93 - 上涨概率: 0.485
2025-06-16 23:26:11.027 | INFO     | models.xgboost_binary:prepare_training_data:144 - 准备训练数据完成: 200 样本, 171 特征
2025-06-16 23:26:11.028 | INFO     | models.xgboost_binary:prepare_training_data:145 - 正样本比例: 0.485
2025-06-16 23:26:13.362 | INFO     | models.xgboost_binary:train:221 - 模型训练完成
2025-06-16 23:26:13.362 | INFO     | models.xgboost_binary:train:222 - 训练集AUC: 1.0000
2025-06-16 23:26:13.362 | INFO     | models.xgboost_binary:train:223 - 测试集AUC: 0.5489
2025-06-16 23:26:13.363 | INFO     | models.xgboost_binary:train:224 - 交叉验证AUC: 0.4923 ± 0.0812
2025-06-16 23:26:13.363 | ERROR    | models.model_manager:_save_training_result:431 - 保存训练结果失败: Object of type DataFrame is not JSON serializable
2025-06-16 23:26:13.363 | INFO     | models.model_manager:train_model:79 - 模型 xgboost_next_day_direction 训练完成
2025-06-16 23:26:13.366 | INFO     | models.xgboost_binary:save_model:331 - 模型已保存到: models/xgboost_next_day_direction_20250616_232613
2025-06-16 23:26:13.366 | INFO     | models.model_manager:save_model:321 - 模型 xgboost_next_day_direction 已保存到: models/xgboost_next_day_direction_20250616_232613
2025-06-16 23:26:13.367 | INFO     | __main__:run_training_pipeline:150 - 4. 训练结果汇总:
2025-06-16 23:26:13.367 | INFO     | __main__:run_training_pipeline:156 - xgboost_10d_surge: AUC=1.0000, Accuracy=1.0000
2025-06-16 23:26:13.367 | INFO     | __main__:run_training_pipeline:156 - xgboost_next_day_direction: AUC=0.5489, Accuracy=0.5500
2025-06-16 23:26:13.367 | INFO     | __main__:run_training_pipeline:159 - 训练流水线完成！
2025-06-16 23:26:13.367 | INFO     | __main__:main:252 - 程序执行成功！
2025-06-16 23:26:20.875 | INFO     | utils.logger:setup_logger:44 - 日志系统初始化完成，日志级别: INFO
2025-06-16 23:26:20.875 | INFO     | utils.logger:setup_logger:45 - 日志文件: logs/qm_20250616.log
2025-06-16 23:26:20.876 | INFO     | __main__:main:238 - 量化交易系统启动，模式: predict
2025-06-16 23:26:20.876 | INFO     | __main__:run_prediction_pipeline:171 - 开始运行预测流水线...
2025-06-16 23:26:20.876 | INFO     | __main__:run_prediction_pipeline:175 - 1. 准备预测数据...
2025-06-16 23:26:20.879 | INFO     | __main__:run_prediction_pipeline:179 - 2. 计算特征...
2025-06-16 23:26:20.937 | INFO     | features.feature_engine:calculate_features:116 - 开始计算特征，类型: ['basic', 'technical', 'time_series']
2025-06-16 23:26:20.938 | INFO     | features.feature_engine:calculate_features:127 - 计算基础特征...
2025-06-16 23:26:20.938 | INFO     | features.base_features:calculate_all_base_features:222 - 开始计算基础特征...
2025-06-16 23:26:20.958 | INFO     | features.base_features:calculate_all_base_features:237 - 基础特征计算完成，共生成 55 个新特征
2025-06-16 23:26:20.958 | INFO     | features.feature_engine:calculate_features:132 - 计算技术指标...
2025-06-16 23:26:20.958 | INFO     | features.technical_indicators:calculate_all_technical_indicators:363 - 开始计算技术指标...
2025-06-16 23:26:20.973 | INFO     | features.technical_indicators:calculate_all_technical_indicators:383 - 技术指标计算完成，共生成 45 个新指标
2025-06-16 23:26:20.974 | INFO     | features.feature_engine:calculate_features:137 - 计算时序特征...
2025-06-16 23:26:20.974 | INFO     | features.time_series_features:calculate_all_time_series_features:168 - 开始计算时序特征...
2025-06-16 23:26:20.991 | INFO     | features.time_series_features:calculate_all_time_series_features:181 - 时序特征计算完成，共生成 69 个新特征
2025-06-16 23:26:20.992 | INFO     | features.feature_engine:calculate_features:141 - 特征计算完成，新增 169 个特征
2025-06-16 23:26:20.992 | INFO     | __main__:run_prediction_pipeline:184 - 3. 加载模型并预测...
2025-06-16 23:26:21.033 | INFO     | models.model_manager:get_combined_predictions:190 - 获取合并预测结果...
2025-06-16 23:26:21.033 | INFO     | models.model_manager:predict_all_models:160 - 开始使用所有模型进行预测...
2025-06-16 23:26:21.033 | WARNING  | models.model_manager:predict:132 - 模型 xgboost_10d_surge 尚未训练，尝试加载已保存的模型
2025-06-16 23:26:21.038 | INFO     | models.xgboost_classifier:load_model:358 - 模型已从 models/xgboost_10d_surge_20250616_232611 加载
2025-06-16 23:26:21.038 | INFO     | models.model_manager:load_model:358 - 模型 xgboost_10d_surge 已从 models/xgboost_10d_surge_20250616_232611 加载
2025-06-16 23:26:21.094 | INFO     | models.xgboost_classifier:predict:282 - 预测完成，30 个样本
2025-06-16 23:26:21.094 | INFO     | models.model_manager:predict:142 - 模型 xgboost_10d_surge 预测完成
2025-06-16 23:26:21.094 | WARNING  | models.model_manager:predict:132 - 模型 xgboost_next_day_direction 尚未训练，尝试加载已保存的模型
2025-06-16 23:26:21.097 | INFO     | models.xgboost_binary:load_model:348 - 模型已从 models/xgboost_next_day_direction_20250616_232613 加载
2025-06-16 23:26:21.098 | INFO     | models.model_manager:load_model:358 - 模型 xgboost_next_day_direction 已从 models/xgboost_next_day_direction_20250616_232613 加载
2025-06-16 23:26:21.142 | INFO     | models.xgboost_binary:predict:272 - 预测完成，30 个样本
2025-06-16 23:26:21.142 | INFO     | models.model_manager:predict:142 - 模型 xgboost_next_day_direction 预测完成
2025-06-16 23:26:21.142 | WARNING  | models.model_manager:predict:132 - 模型 time_series_price_prediction 尚未训练，尝试加载已保存的模型
2025-06-16 23:26:21.142 | ERROR    | models.model_manager:load_model:361 - 加载模型 time_series_price_prediction 失败: 未找到模型 time_series_price_prediction 的保存文件
2025-06-16 23:26:21.142 | ERROR    | models.model_manager:predict:146 - 模型 time_series_price_prediction 预测失败: 未找到模型 time_series_price_prediction 的保存文件
2025-06-16 23:26:21.143 | ERROR    | models.model_manager:predict_all_models:169 - 模型 time_series_price_prediction 预测失败: 未找到模型 time_series_price_prediction 的保存文件
2025-06-16 23:26:21.143 | INFO     | models.model_manager:predict_all_models:172 - 所有模型预测完成
2025-06-16 23:26:21.144 | INFO     | models.model_manager:get_combined_predictions:210 - 合并预测结果完成
2025-06-16 23:26:21.144 | INFO     | __main__:run_prediction_pipeline:194 - 预测结果:
2025-06-16 23:26:21.152 | INFO     | __main__:run_prediction_pipeline:214 - 预测流水线完成！
2025-06-16 23:26:21.153 | INFO     | __main__:main:252 - 程序执行成功！
2025-06-16 23:32:09.337 | INFO     | utils.logger:setup_logger:44 - 日志系统初始化完成，日志级别: INFO
2025-06-16 23:32:09.339 | INFO     | utils.logger:setup_logger:45 - 日志文件: logs/qm_20250616.log
2025-06-16 23:32:09.356 | INFO     | data.database:_init_database:79 - 数据库初始化完成
2025-06-16 23:32:09.466 | INFO     | data.database:_init_database:79 - 数据库初始化完成
2025-06-16 23:32:09.481 | INFO     | __main__:<module>:275 - 启动量化交易系统Web界面...
2025-06-16 23:33:55.436 | ERROR    | __main__:get_predictions:95 - 获取预测结果失败: could not convert string to float: b'X\x95P?'
