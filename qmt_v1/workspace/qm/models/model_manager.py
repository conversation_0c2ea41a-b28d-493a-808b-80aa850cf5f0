"""
模型管理器 - 统一管理所有模型的训练、预测和评估
"""
import pandas as pd
import numpy as np
import yaml
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from loguru import logger

from .xgboost_classifier import XGBoostClassifier
from .xgboost_binary import XGBoostBinary
from .time_series_model import TimeSeriesModel


class ModelManager:
    """模型管理器 - 统一管理所有模型"""
    
    def __init__(self, config_path: str = "config/models.yaml"):
        """
        初始化模型管理器
        
        Args:
            config_path: 模型配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # 初始化模型
        self.models = {
            'xgboost_10d_surge': XGBoostClassifier(config_path),
            'xgboost_next_day_direction': XGBoostBinary(config_path),
            'time_series_price_prediction': TimeSeriesModel(config_path)
        }
        
        # 模型状态
        self.model_status = {name: False for name in self.models.keys()}
        
        # 预测结果缓存
        self.prediction_cache = {}

        # 因子组合管理
        self.current_factor_combination = {}
        self.factor_combinations_history = []
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def train_model(self, model_name: str, training_data: pd.DataFrame,
                   factor_names: List[str] = None) -> Dict[str, Any]:
        """
        训练指定模型

        Args:
            model_name: 模型名称
            training_data: 训练数据
            factor_names: 使用的因子列表

        Returns:
            训练结果
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")

            logger.info(f"开始训练模型: {model_name}")

            # 记录使用的因子组合
            if factor_names:
                self.current_factor_combination[model_name] = {
                    'factors': factor_names,
                    'training_date': pd.Timestamp.now().isoformat(),
                    'data_shape': training_data.shape
                }
                logger.info(f"模型 {model_name} 使用 {len(factor_names)} 个因子")

            model = self.models[model_name]
            training_result = model.train(training_data)

            # 添加因子信息到训练结果
            if factor_names:
                training_result['factor_combination'] = {
                    'factors': factor_names,
                    'factor_count': len(factor_names)
                }

            # 更新模型状态
            self.model_status[model_name] = True

            # 保存训练结果
            self._save_training_result(model_name, training_result)

            # 保存因子组合历史
            if factor_names:
                self.factor_combinations_history.append({
                    'model_name': model_name,
                    'factors': factor_names,
                    'training_date': pd.Timestamp.now().isoformat(),
                    'performance': training_result.get('metrics', {})
                })

            logger.info(f"模型 {model_name} 训练完成")
            return training_result

        except Exception as e:
            logger.error(f"训练模型 {model_name} 失败: {e}")
            raise
    
    def train_all_models(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练所有模型
        
        Args:
            training_data: 训练数据
            
        Returns:
            所有模型的训练结果
        """
        try:
            logger.info("开始训练所有模型...")
            
            all_results = {}
            
            for model_name in self.models.keys():
                try:
                    result = self.train_model(model_name, training_data)
                    all_results[model_name] = result
                except Exception as e:
                    logger.error(f"训练模型 {model_name} 失败: {e}")
                    all_results[model_name] = {'error': str(e)}
            
            logger.info("所有模型训练完成")
            return all_results
            
        except Exception as e:
            logger.error(f"训练所有模型失败: {e}")
            raise
    
    def predict(self, model_name: str, prediction_data: pd.DataFrame) -> pd.DataFrame:
        """
        使用指定模型进行预测
        
        Args:
            model_name: 模型名称
            prediction_data: 预测数据
            
        Returns:
            预测结果
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            if not self.model_status[model_name]:
                logger.warning(f"模型 {model_name} 尚未训练，尝试加载已保存的模型")
                self.load_model(model_name)
            
            model = self.models[model_name]
            predictions = model.predict(prediction_data)
            
            # 缓存预测结果
            cache_key = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.prediction_cache[cache_key] = predictions
            
            logger.info(f"模型 {model_name} 预测完成")
            return predictions
            
        except Exception as e:
            logger.error(f"模型 {model_name} 预测失败: {e}")
            raise
    
    def predict_all_models(self, prediction_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        使用所有模型进行预测
        
        Args:
            prediction_data: 预测数据
            
        Returns:
            所有模型的预测结果
        """
        try:
            logger.info("开始使用所有模型进行预测...")
            
            all_predictions = {}
            
            for model_name in self.models.keys():
                try:
                    predictions = self.predict(model_name, prediction_data)
                    all_predictions[model_name] = predictions
                except Exception as e:
                    logger.error(f"模型 {model_name} 预测失败: {e}")
                    all_predictions[model_name] = pd.DataFrame()
            
            logger.info("所有模型预测完成")
            return all_predictions
            
        except Exception as e:
            logger.error(f"所有模型预测失败: {e}")
            raise
    
    def get_combined_predictions(self, prediction_data: pd.DataFrame) -> pd.DataFrame:
        """
        获取合并的预测结果
        
        Args:
            prediction_data: 预测数据
            
        Returns:
            合并的预测结果
        """
        try:
            logger.info("获取合并预测结果...")
            
            # 获取所有模型的预测结果
            all_predictions = self.predict_all_models(prediction_data)
            
            # 合并预测结果
            result_df = prediction_data.copy()
            
            for model_name, predictions in all_predictions.items():
                if predictions.empty:
                    continue
                
                # 提取预测列
                prediction_columns = [col for col in predictions.columns if col.startswith('prediction_')]
                
                for col in prediction_columns:
                    new_col_name = f"{model_name}_{col}"
                    if col in predictions.columns:
                        result_df[new_col_name] = predictions[col]
            
            logger.info("合并预测结果完成")
            return result_df
            
        except Exception as e:
            logger.error(f"获取合并预测结果失败: {e}")
            raise
    
    def evaluate_model(self, model_name: str, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        评估指定模型
        
        Args:
            model_name: 模型名称
            test_data: 测试数据
            
        Returns:
            评估结果
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            logger.info(f"开始评估模型: {model_name}")
            
            model = self.models[model_name]
            
            # 获取预测结果
            predictions = model.predict(test_data)
            
            # 根据模型类型计算评估指标
            evaluation_result = {}
            
            if model_name == 'xgboost_10d_surge':
                # 10日拉升概率预测评估
                if 'target_10d_surge' in test_data.columns:
                    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
                    
                    y_true = test_data['target_10d_surge']
                    y_pred = predictions['prediction_10d_surge_label']
                    y_proba = predictions['prediction_10d_surge_prob']
                    
                    evaluation_result = {
                        'accuracy': accuracy_score(y_true, y_pred),
                        'precision': precision_score(y_true, y_pred, zero_division=0),
                        'recall': recall_score(y_true, y_pred, zero_division=0),
                        'f1_score': f1_score(y_true, y_pred, zero_division=0),
                        'auc': roc_auc_score(y_true, y_proba) if len(np.unique(y_true)) > 1 else 0.0
                    }
            
            elif model_name == 'xgboost_next_day_direction':
                # 明日涨跌预测评估
                if 'target_next_day_direction' in test_data.columns:
                    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
                    
                    y_true = test_data['target_next_day_direction']
                    y_pred = predictions['prediction_next_day_direction']
                    
                    evaluation_result = {
                        'accuracy': accuracy_score(y_true, y_pred),
                        'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                        'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                        'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
                    }
            
            elif model_name == 'time_series_price_prediction':
                # 价格预测评估
                from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
                
                target_columns = ['target_next_open', 'target_next_close', 'target_next_high']
                prediction_columns = ['prediction_next_open', 'prediction_next_close', 'prediction_next_high']
                
                for target_col, pred_col in zip(target_columns, prediction_columns):
                    if target_col in test_data.columns and pred_col in predictions.columns:
                        y_true = test_data[target_col].dropna()
                        y_pred = predictions[pred_col].dropna()
                        
                        if len(y_true) > 0 and len(y_pred) > 0:
                            min_len = min(len(y_true), len(y_pred))
                            y_true = y_true.iloc[:min_len]
                            y_pred = y_pred.iloc[:min_len]
                            
                            evaluation_result[f'{target_col}_mse'] = mean_squared_error(y_true, y_pred)
                            evaluation_result[f'{target_col}_rmse'] = np.sqrt(evaluation_result[f'{target_col}_mse'])
                            evaluation_result[f'{target_col}_mae'] = mean_absolute_error(y_true, y_pred)
                            evaluation_result[f'{target_col}_r2'] = r2_score(y_true, y_pred)
            
            logger.info(f"模型 {model_name} 评估完成")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"评估模型 {model_name} 失败: {e}")
            return {}
    
    def save_model(self, model_name: str, file_path: str = None):
        """
        保存指定模型
        
        Args:
            model_name: 模型名称
            file_path: 保存路径
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            if file_path is None:
                file_path = f"models/{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            model = self.models[model_name]
            model.save_model(file_path)
            
            logger.info(f"模型 {model_name} 已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模型 {model_name} 失败: {e}")
            raise
    
    def load_model(self, model_name: str, file_path: str = None):
        """
        加载指定模型
        
        Args:
            model_name: 模型名称
            file_path: 模型文件路径
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            if file_path is None:
                # 查找最新的模型文件
                model_dir = "models"
                if os.path.exists(model_dir):
                    model_files = [f for f in os.listdir(model_dir) if f.startswith(model_name)]
                    if model_files:
                        latest_file = sorted(model_files)[-1]
                        file_path = os.path.join(model_dir, latest_file)
                    else:
                        raise FileNotFoundError(f"未找到模型 {model_name} 的保存文件")
                else:
                    raise FileNotFoundError(f"模型目录 {model_dir} 不存在")
            
            model = self.models[model_name]
            model.load_model(file_path)
            
            # 更新模型状态
            self.model_status[model_name] = True
            
            logger.info(f"模型 {model_name} 已从 {file_path} 加载")
            
        except Exception as e:
            logger.error(f"加载模型 {model_name} 失败: {e}")
            raise
    
    def get_model_status(self) -> Dict[str, bool]:
        """获取所有模型的状态"""
        return self.model_status.copy()
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型信息
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            model = self.models[model_name]
            
            info = {
                'name': model_name,
                'is_trained': self.model_status[model_name],
                'training_metrics': getattr(model, 'training_metrics', {}),
                'validation_metrics': getattr(model, 'validation_metrics', {}),
                'feature_columns': getattr(model, 'feature_columns', [])
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取模型 {model_name} 信息失败: {e}")
            return {}
    
    def _save_training_result(self, model_name: str, training_result: Dict[str, Any]):
        """保存训练结果"""
        try:
            # 确保目录存在
            results_dir = "results"
            os.makedirs(results_dir, exist_ok=True)
            
            # 保存训练结果
            result_file = os.path.join(results_dir, f"{model_name}_training_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            # 转换numpy类型为Python原生类型
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, dict):
                    return {key: convert_numpy(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                else:
                    return obj
            
            training_result_serializable = convert_numpy(training_result)
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(training_result_serializable, f, indent=2, ensure_ascii=False)
            
            logger.info(f"训练结果已保存到: {result_file}")
            
        except Exception as e:
            logger.error(f"保存训练结果失败: {e}")

    # ==================== 因子组合管理 ====================

    def get_current_factor_combination(self, model_name: str) -> Dict:
        """获取模型当前使用的因子组合"""
        return self.current_factor_combination.get(model_name, {})

    def get_all_factor_combinations(self) -> Dict[str, Dict]:
        """获取所有模型的因子组合"""
        return self.current_factor_combination

    def get_factor_combinations_history(self) -> List[Dict]:
        """获取因子组合历史记录"""
        return self.factor_combinations_history

    def set_factor_combination(self, model_name: str, factor_names: List[str]):
        """设置模型的因子组合"""
        if model_name in self.models:
            self.current_factor_combination[model_name] = {
                'factors': factor_names,
                'updated_date': pd.Timestamp.now().isoformat(),
                'factor_count': len(factor_names)
            }
            logger.info(f"设置模型 {model_name} 的因子组合: {len(factor_names)} 个因子")

    def compare_factor_combinations(self, model_name: str) -> pd.DataFrame:
        """比较模型的历史因子组合性能"""
        try:
            model_history = [h for h in self.factor_combinations_history if h['model_name'] == model_name]

            if not model_history:
                logger.warning(f"模型 {model_name} 没有历史因子组合记录")
                return pd.DataFrame()

            comparison_data = []
            for record in model_history:
                data = {
                    'training_date': record['training_date'],
                    'factor_count': len(record['factors']),
                    'factors': ', '.join(record['factors'][:5]) + ('...' if len(record['factors']) > 5 else ''),
                }

                # 添加性能指标
                performance = record.get('performance', {})
                for metric, value in performance.items():
                    data[metric] = value

                comparison_data.append(data)

            return pd.DataFrame(comparison_data)

        except Exception as e:
            logger.error(f"比较因子组合失败: {e}")
            return pd.DataFrame()

    def get_factor_usage_statistics(self) -> pd.DataFrame:
        """获取因子使用统计"""
        try:
            factor_usage = {}

            for record in self.factor_combinations_history:
                for factor in record['factors']:
                    if factor not in factor_usage:
                        factor_usage[factor] = {
                            'usage_count': 0,
                            'models': set(),
                            'avg_performance': []
                        }

                    factor_usage[factor]['usage_count'] += 1
                    factor_usage[factor]['models'].add(record['model_name'])

                    # 添加性能指标（如果有的话）
                    performance = record.get('performance', {})
                    if 'accuracy' in performance:
                        factor_usage[factor]['avg_performance'].append(performance['accuracy'])

            # 转换为DataFrame
            stats_data = []
            for factor, stats in factor_usage.items():
                avg_perf = np.mean(stats['avg_performance']) if stats['avg_performance'] else 0

                stats_data.append({
                    'factor': factor,
                    'usage_count': stats['usage_count'],
                    'models_used': len(stats['models']),
                    'avg_performance': round(avg_perf, 4),
                    'models': ', '.join(stats['models'])
                })

            df = pd.DataFrame(stats_data)
            if not df.empty:
                df = df.sort_values('usage_count', ascending=False)

            return df

        except Exception as e:
            logger.error(f"获取因子使用统计失败: {e}")
            return pd.DataFrame()

    def recommend_factor_combination(self, model_name: str,
                                   available_factors: List[str],
                                   max_factors: int = 20) -> List[str]:
        """推荐因子组合"""
        try:
            # 基于历史表现推荐因子组合
            factor_stats = self.get_factor_usage_statistics()

            if factor_stats.empty:
                # 如果没有历史数据，随机选择
                import random
                return random.sample(available_factors, min(max_factors, len(available_factors)))

            # 按使用频率和性能排序
            factor_stats['score'] = (
                factor_stats['usage_count'] * 0.3 +
                factor_stats['avg_performance'] * 0.7
            )

            # 选择评分最高的因子
            top_factors = factor_stats.nlargest(max_factors, 'score')['factor'].tolist()

            # 确保因子在可用列表中
            recommended_factors = [f for f in top_factors if f in available_factors]

            # 如果推荐的因子不够，补充其他因子
            if len(recommended_factors) < max_factors:
                remaining_factors = [f for f in available_factors if f not in recommended_factors]
                additional_count = min(max_factors - len(recommended_factors), len(remaining_factors))
                recommended_factors.extend(remaining_factors[:additional_count])

            logger.info(f"为模型 {model_name} 推荐 {len(recommended_factors)} 个因子")
            return recommended_factors

        except Exception as e:
            logger.error(f"推荐因子组合失败: {e}")
            return available_factors[:max_factors]


# 测试函数
def test_model_manager():
    """测试模型管理器"""
    logger.info("开始测试模型管理器...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # 生成价格数据
    price = 10
    prices = []
    opens = []
    highs = []
    lows = []
    
    for i in range(200):
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        close_price = open_price * (1 + np.random.normal(0, 0.02))
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.02))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.02))
        
        opens.append(open_price)
        prices.append(close_price)
        highs.append(high_price)
        lows.append(low_price)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': opens,
        'close': prices,
        'high': highs,
        'low': lows,
        'volume': 1000000 + np.random.randint(-200000, 200000, 200),
        'feature1': np.random.randn(200),
        'feature2': np.random.randn(200),
        'feature3': np.random.randn(200)
    })
    
    # 测试模型管理器
    manager = ModelManager()
    
    # 获取模型状态
    status = manager.get_model_status()
    print("模型状态:", status)
    
    # 训练单个模型
    try:
        result = manager.train_model('xgboost_next_day_direction', test_data)
        print("XGBoost二分类模型训练结果:", result['validation_metrics'])
    except Exception as e:
        print(f"训练失败: {e}")
    
    # 预测
    try:
        predictions = manager.predict('xgboost_next_day_direction', test_data.head(50))
        print("预测结果:")
        pred_cols = [col for col in predictions.columns if col.startswith('prediction_')]
        if pred_cols:
            print(predictions[['trade_date', 'close'] + pred_cols].head())
    except Exception as e:
        print(f"预测失败: {e}")
    
    logger.info("模型管理器测试完成")


if __name__ == "__main__":
    test_model_manager()
