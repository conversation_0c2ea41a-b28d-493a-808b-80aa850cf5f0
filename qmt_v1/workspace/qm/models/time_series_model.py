"""
时序模型 - 用于明日价格预测（多目标回归）
"""
import pandas as pd
import numpy as np
import yaml
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger
from datetime import datetime, timedelta
import os
import joblib

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Input
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow未安装，LSTM模型不可用")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    logger.warning("PyTorch未安装，Transformer模型不可用")

from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score


class TimeSeriesModel:
    """时序模型 - 预测明日开盘价、收盘价、最高价"""
    
    def __init__(self, config_path: str = "config/models.yaml"):
        """
        初始化时序模型
        
        Args:
            config_path: 模型配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.model_config = self.config.get('models', {}).get('time_series_price_prediction', {})
        
        # 模型参数
        self.algorithm = self.model_config.get('algorithm', 'lstm')
        self.model_params = self.model_config.get('parameters', {}).get(self.algorithm, {})
        self.training_config = self.model_config.get('training', {})
        self.target_config = self.model_config.get('target_definition', {})
        
        # 模型对象
        self.model = None
        self.scaler_X = None
        self.scaler_y = None
        self.feature_columns = None
        self.is_trained = False
        
        # 序列参数
        self.sequence_length = self.model_params.get('sequence_length', 20)
        
        # 评估结果
        self.training_metrics = {}
        self.validation_metrics = {}
        
    def _load_config(self) -> Dict:
        """加载模型配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载模型配置失败: {e}")
            return {}
    
    def create_target_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建目标标签 - 明日开盘价、收盘价、最高价
        
        Args:
            df: 包含价格数据的DataFrame
            
        Returns:
            包含目标标签的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 获取目标定义
            targets = self.target_config.get('targets', ['next_open', 'next_close', 'next_high'])
            
            logger.info(f"创建目标标签: {targets}")
            
            # 创建目标变量
            if 'next_open' in targets:
                result_df['target_next_open'] = result_df['open'].shift(-1)
            
            if 'next_close' in targets:
                result_df['target_next_close'] = result_df['close'].shift(-1)
            
            if 'next_high' in targets:
                result_df['target_next_high'] = result_df['high'].shift(-1)
            
            # 统计目标变量
            target_columns = [f'target_{target}' for target in targets]
            for col in target_columns:
                if col in result_df.columns:
                    logger.info(f"{col}: 均值={result_df[col].mean():.4f}, 标准差={result_df[col].std():.4f}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"创建目标标签失败: {e}")
            return df
    
    def prepare_sequence_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str], List[str]]:
        """
        准备序列数据
        
        Args:
            df: 包含特征和目标的DataFrame
            
        Returns:
            特征序列, 目标序列, 特征列名, 目标列名
        """
        try:
            # 创建目标标签
            df_with_target = self.create_target_labels(df)
            
            # 获取目标列
            targets = self.target_config.get('targets', ['next_open', 'next_close', 'next_high'])
            target_columns = [f'target_{target}' for target in targets]
            
            # 删除包含NaN的行
            required_columns = target_columns + ['close', 'open', 'high', 'low', 'volume']
            df_clean = df_with_target.dropna(subset=required_columns)
            
            if len(df_clean) < self.sequence_length + 1:
                raise ValueError(f"数据量不足，需要至少 {self.sequence_length + 1} 行数据")
            
            # 获取特征列
            exclude_columns = ['stock_code', 'trade_date'] + target_columns
            feature_columns = [col for col in df_clean.columns 
                             if col not in exclude_columns and df_clean[col].dtype in ['int64', 'float64']]
            
            if not feature_columns:
                raise ValueError("没有找到可用的特征列")
            
            # 准备数据
            X_data = df_clean[feature_columns].values
            y_data = df_clean[target_columns].values
            
            # 处理无穷大和NaN值
            X_data = np.nan_to_num(X_data, nan=0.0, posinf=0.0, neginf=0.0)
            y_data = np.nan_to_num(y_data, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 数据标准化
            self.scaler_X = MinMaxScaler()
            self.scaler_y = MinMaxScaler()
            
            X_scaled = self.scaler_X.fit_transform(X_data)
            y_scaled = self.scaler_y.fit_transform(y_data)
            
            # 创建序列数据
            X_sequences = []
            y_sequences = []
            
            for i in range(self.sequence_length, len(X_scaled)):
                X_sequences.append(X_scaled[i-self.sequence_length:i])
                y_sequences.append(y_scaled[i])
            
            X_sequences = np.array(X_sequences)
            y_sequences = np.array(y_sequences)
            
            logger.info(f"准备序列数据完成: {X_sequences.shape[0]} 序列, 序列长度 {X_sequences.shape[1]}, 特征数 {X_sequences.shape[2]}")
            logger.info(f"目标维度: {y_sequences.shape}")
            
            return X_sequences, y_sequences, feature_columns, target_columns
            
        except Exception as e:
            logger.error(f"准备序列数据失败: {e}")
            raise
    
    def build_lstm_model(self, input_shape: Tuple, output_dim: int) -> Any:
        """构建LSTM模型"""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow未安装，无法使用LSTM模型")
        
        try:
            model = Sequential([
                LSTM(self.model_params.get('hidden_size', 64), 
                     return_sequences=True, 
                     input_shape=input_shape),
                Dropout(self.model_params.get('dropout', 0.2)),
                
                LSTM(self.model_params.get('hidden_size', 64), 
                     return_sequences=False),
                Dropout(self.model_params.get('dropout', 0.2)),
                
                Dense(32, activation='relu'),
                Dense(output_dim)
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=self.model_params.get('learning_rate', 0.001)),
                loss='mse',
                metrics=['mae']
            )
            
            logger.info("LSTM模型构建完成")
            return model
            
        except Exception as e:
            logger.error(f"构建LSTM模型失败: {e}")
            raise
    
    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            df: 训练数据DataFrame
            
        Returns:
            训练结果字典
        """
        try:
            logger.info(f"开始训练时序模型 ({self.algorithm})...")
            
            # 准备序列数据
            X, y, feature_columns, target_columns = self.prepare_sequence_data(df)
            self.feature_columns = feature_columns
            self.target_columns = target_columns
            
            # 分割训练集和测试集
            test_size = self.training_config.get('test_size', 0.2)
            split_idx = int(len(X) * (1 - test_size))
            
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # 进一步分割验证集
            val_size = self.training_config.get('validation_size', 0.1)
            if val_size > 0:
                val_split_idx = int(len(X_train) * (1 - val_size))
                X_train, X_val = X_train[:val_split_idx], X_train[val_split_idx:]
                y_train, y_val = y_train[:val_split_idx], y_train[val_split_idx:]
            else:
                X_val, y_val = X_test, y_test
            
            # 根据算法选择模型
            if self.algorithm == 'lstm':
                self.model = self.build_lstm_model(
                    input_shape=(X_train.shape[1], X_train.shape[2]),
                    output_dim=y_train.shape[1]
                )
                
                # 训练LSTM模型
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=self.model_params.get('early_stopping_patience', 10),
                    restore_best_weights=True
                )
                
                history = self.model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    epochs=self.model_params.get('epochs', 100),
                    batch_size=self.model_params.get('batch_size', 32),
                    callbacks=[early_stopping],
                    verbose=0
                )
                
                # 预测
                y_train_pred = self.model.predict(X_train)
                y_test_pred = self.model.predict(X_test)
                
            else:
                raise ValueError(f"不支持的算法: {self.algorithm}")
            
            # 反标准化预测结果
            y_train_pred_original = self.scaler_y.inverse_transform(y_train_pred)
            y_test_pred_original = self.scaler_y.inverse_transform(y_test_pred)
            y_train_original = self.scaler_y.inverse_transform(y_train)
            y_test_original = self.scaler_y.inverse_transform(y_test)
            
            # 计算评估指标
            self.training_metrics = self._calculate_metrics(y_train_original, y_train_pred_original)
            self.validation_metrics = self._calculate_metrics(y_test_original, y_test_pred_original)
            
            self.is_trained = True
            
            training_result = {
                'training_metrics': self.training_metrics,
                'validation_metrics': self.validation_metrics,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'sequence_length': self.sequence_length,
                'feature_count': len(feature_columns),
                'target_count': len(target_columns)
            }
            
            logger.info(f"模型训练完成")
            logger.info(f"训练集RMSE: {self.training_metrics['rmse']:.4f}")
            logger.info(f"测试集RMSE: {self.validation_metrics['rmse']:.4f}")
            
            return training_result
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        预测
        
        Args:
            df: 预测数据DataFrame
            
        Returns:
            包含预测结果的DataFrame
        """
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            if self.feature_columns is None:
                raise ValueError("特征列信息缺失")
            
            # 准备预测数据
            X_data = df[self.feature_columns].values
            X_data = np.nan_to_num(X_data, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 标准化
            X_scaled = self.scaler_X.transform(X_data)
            
            # 创建序列（只预测最后一个时间点）
            if len(X_scaled) < self.sequence_length:
                raise ValueError(f"数据长度不足，需要至少 {self.sequence_length} 行数据")
            
            X_sequence = X_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            
            # 预测
            y_pred_scaled = self.model.predict(X_sequence)
            y_pred = self.scaler_y.inverse_transform(y_pred_scaled)
            
            # 创建结果DataFrame
            result_df = df.copy()
            
            # 添加预测结果到最后一行
            targets = self.target_config.get('targets', ['next_open', 'next_close', 'next_high'])
            for i, target in enumerate(targets):
                result_df.loc[result_df.index[-1], f'prediction_{target}'] = y_pred[0, i]
            
            logger.info(f"预测完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        try:
            metrics = {}
            
            # 对每个目标分别计算指标
            for i in range(y_true.shape[1]):
                target_name = self.target_columns[i] if hasattr(self, 'target_columns') else f'target_{i}'
                
                mse = mean_squared_error(y_true[:, i], y_pred[:, i])
                rmse = np.sqrt(mse)
                mae = mean_absolute_error(y_true[:, i], y_pred[:, i])
                r2 = r2_score(y_true[:, i], y_pred[:, i])
                
                # 计算MAPE
                mape = np.mean(np.abs((y_true[:, i] - y_pred[:, i]) / (y_true[:, i] + 1e-8))) * 100
                
                metrics[f'{target_name}_mse'] = mse
                metrics[f'{target_name}_rmse'] = rmse
                metrics[f'{target_name}_mae'] = mae
                metrics[f'{target_name}_r2'] = r2
                metrics[f'{target_name}_mape'] = mape
            
            # 计算总体指标
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            return {}
    
    def save_model(self, file_path: str):
        """保存模型"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存模型和相关信息
            if self.algorithm == 'lstm':
                # 保存Keras模型
                self.model.save(file_path + '_model.h5')
            
            # 保存其他信息
            model_data = {
                'algorithm': self.algorithm,
                'scaler_X': self.scaler_X,
                'scaler_y': self.scaler_y,
                'feature_columns': self.feature_columns,
                'target_columns': getattr(self, 'target_columns', []),
                'sequence_length': self.sequence_length,
                'training_metrics': self.training_metrics,
                'validation_metrics': self.validation_metrics,
                'config': self.model_config
            }
            
            joblib.dump(model_data, file_path + '_data.pkl')
            logger.info(f"模型已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def load_model(self, file_path: str):
        """加载模型"""
        try:
            # 加载模型数据
            model_data = joblib.load(file_path + '_data.pkl')
            
            self.algorithm = model_data['algorithm']
            self.scaler_X = model_data['scaler_X']
            self.scaler_y = model_data['scaler_y']
            self.feature_columns = model_data['feature_columns']
            self.target_columns = model_data.get('target_columns', [])
            self.sequence_length = model_data['sequence_length']
            self.training_metrics = model_data.get('training_metrics', {})
            self.validation_metrics = model_data.get('validation_metrics', {})
            
            # 加载模型
            if self.algorithm == 'lstm':
                if TENSORFLOW_AVAILABLE:
                    self.model = tf.keras.models.load_model(file_path + '_model.h5')
                else:
                    raise ImportError("TensorFlow未安装，无法加载LSTM模型")
            
            self.is_trained = True
            logger.info(f"模型已从 {file_path} 加载")
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise


# 测试函数
def test_time_series_model():
    """测试时序模型"""
    if not TENSORFLOW_AVAILABLE:
        logger.warning("TensorFlow未安装，跳过时序模型测试")
        return
    
    logger.info("开始测试时序模型...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # 生成价格数据
    price = 10
    prices = []
    opens = []
    highs = []
    lows = []
    
    for i in range(200):
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        close_price = open_price * (1 + np.random.normal(0, 0.02))
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.02))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.02))
        
        opens.append(open_price)
        prices.append(close_price)
        highs.append(high_price)
        lows.append(low_price)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': opens,
        'close': prices,
        'high': highs,
        'low': lows,
        'volume': 1000000 + np.random.randint(-200000, 200000, 200),
        'feature1': np.random.randn(200),
        'feature2': np.random.randn(200)
    })
    
    # 测试模型
    model = TimeSeriesModel()
    
    # 训练模型
    training_result = model.train(test_data)
    print("训练结果:", training_result['validation_metrics'])
    
    # 预测
    predictions = model.predict(test_data)
    print("预测结果:")
    prediction_cols = [col for col in predictions.columns if col.startswith('prediction_')]
    if prediction_cols:
        print(predictions[['trade_date', 'open', 'close', 'high'] + prediction_cols].tail())
    
    logger.info("时序模型测试完成")


if __name__ == "__main__":
    test_time_series_model()
