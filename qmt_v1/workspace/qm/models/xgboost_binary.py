"""
XGBoost二分类模型 - 用于明日开盘涨跌预测
"""
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
import joblib
import yaml
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger
from datetime import datetime, timedelta
import os


class XGBoostBinary:
    """XGBoost二分类模型 - 预测明日开盘涨跌"""
    
    def __init__(self, config_path: str = "config/models.yaml"):
        """
        初始化XGBoost二分类模型
        
        Args:
            config_path: 模型配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.model_config = self.config.get('models', {}).get('xgboost_next_day_direction', {})
        
        # 模型参数
        self.model_params = self.model_config.get('parameters', {})
        self.training_config = self.model_config.get('training', {})
        self.target_config = self.model_config.get('target_definition', {})
        
        # 模型对象
        self.model = None
        self.feature_columns = None
        self.is_trained = False
        
        # 评估结果
        self.training_metrics = {}
        self.validation_metrics = {}
        
    def _load_config(self) -> Dict:
        """加载模型配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载模型配置失败: {e}")
            return {}
    
    def create_target_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建目标标签 - 明日开盘涨跌
        
        Args:
            df: 包含价格数据的DataFrame
            
        Returns:
            包含目标标签的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 获取目标定义参数
            positive_label = self.target_config.get('positive_label', 1)  # 上涨
            negative_label = self.target_config.get('negative_label', -1)  # 下跌
            
            logger.info(f"创建目标标签: 明日开盘价相对今日收盘价涨跌")
            
            # 计算明日开盘价相对今日收盘价的变化
            result_df['next_open'] = result_df['open'].shift(-1)
            result_df['price_change'] = result_df['next_open'] - result_df['close']
            result_df['price_change_pct'] = result_df['price_change'] / result_df['close']
            
            # 创建目标标签
            # 上涨：明日开盘价 > 今日收盘价 → 标签 1
            # 下跌：明日开盘价 ≤ 今日收盘价 → 标签 -1
            result_df['target_next_day_direction'] = np.where(
                result_df['price_change'] > 0, 
                positive_label, 
                negative_label
            )
            
            # 统计标签分布
            label_counts = result_df['target_next_day_direction'].value_counts()
            logger.info(f"目标标签分布: {dict(label_counts)}")
            
            # 计算上涨概率
            up_ratio = (result_df['target_next_day_direction'] == positive_label).mean()
            logger.info(f"上涨概率: {up_ratio:.3f}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"创建目标标签失败: {e}")
            return df
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
        """
        准备训练数据
        
        Args:
            df: 包含特征和目标的DataFrame
            
        Returns:
            特征DataFrame, 目标Series, 特征列名列表
        """
        try:
            # 创建目标标签
            df_with_target = self.create_target_labels(df)
            
            # 删除包含NaN的行
            df_clean = df_with_target.dropna(subset=['target_next_day_direction'])
            
            if df_clean.empty:
                raise ValueError("清理后的数据为空")
            
            # 获取特征列
            exclude_columns = [
                'stock_code', 'trade_date', 'target_next_day_direction', 
                'next_open', 'price_change', 'price_change_pct'
            ]
            
            feature_columns = [col for col in df_clean.columns 
                             if col not in exclude_columns and df_clean[col].dtype in ['int64', 'float64']]
            
            if not feature_columns:
                raise ValueError("没有找到可用的特征列")
            
            # 准备特征和目标
            X = df_clean[feature_columns]
            y = df_clean['target_next_day_direction']
            
            # 处理特征中的无穷大和NaN值
            X = X.replace([np.inf, -np.inf], np.nan)
            X = X.fillna(0)
            
            # 将标签转换为0和1（XGBoost要求）
            y_binary = (y == self.target_config.get('positive_label', 1)).astype(int)
            
            logger.info(f"准备训练数据完成: {len(X)} 样本, {len(feature_columns)} 特征")
            logger.info(f"正样本比例: {y_binary.mean():.3f}")
            
            return X, y_binary, feature_columns
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            raise
    
    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            df: 训练数据DataFrame
            
        Returns:
            训练结果字典
        """
        try:
            logger.info("开始训练XGBoost二分类模型...")
            
            # 准备训练数据
            X, y, feature_columns = self.prepare_training_data(df)
            self.feature_columns = feature_columns
            
            # 分割训练集和测试集
            test_size = self.training_config.get('test_size', 0.2)
            random_state = self.model_params.get('random_state', 42)
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state, stratify=y
            )
            
            # 进一步分割验证集
            val_size = self.training_config.get('validation_size', 0.1)
            if val_size > 0:
                X_train, X_val, y_train, y_val = train_test_split(
                    X_train, y_train, test_size=val_size/(1-test_size), 
                    random_state=random_state, stratify=y_train
                )
            else:
                X_val, y_val = X_test, y_test
            
            # 创建XGBoost模型
            self.model = xgb.XGBClassifier(**self.model_params)
            
            # 训练模型
            self.model.fit(X_train, y_train)
            
            # 预测
            y_train_pred = self.model.predict(X_train)
            y_train_proba = self.model.predict_proba(X_train)[:, 1]
            y_test_pred = self.model.predict(X_test)
            y_test_proba = self.model.predict_proba(X_test)[:, 1]
            
            # 计算评估指标
            self.training_metrics = self._calculate_metrics(y_train, y_train_pred, y_train_proba)
            self.validation_metrics = self._calculate_metrics(y_test, y_test_pred, y_test_proba)
            
            self.is_trained = True
            
            # 交叉验证
            cv_folds = self.training_config.get('cv_folds', 5)
            cv_scores = cross_val_score(self.model, X, y, cv=cv_folds, scoring='roc_auc')
            
            training_result = {
                'training_metrics': self.training_metrics,
                'validation_metrics': self.validation_metrics,
                'cv_scores': cv_scores,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'feature_importance': self.get_feature_importance(),
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
            
            logger.info(f"模型训练完成")
            logger.info(f"训练集AUC: {self.training_metrics['auc']:.4f}")
            logger.info(f"测试集AUC: {self.validation_metrics['auc']:.4f}")
            logger.info(f"交叉验证AUC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            
            return training_result
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        预测
        
        Args:
            df: 预测数据DataFrame
            
        Returns:
            包含预测结果的DataFrame
        """
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            if self.feature_columns is None:
                raise ValueError("特征列信息缺失")
            
            # 准备预测数据
            X = df[self.feature_columns].copy()
            X = X.replace([np.inf, -np.inf], np.nan)
            X = X.fillna(0)
            
            # 预测
            predictions_proba = self.model.predict_proba(X)[:, 1]  # 获取正类概率
            predictions_binary = self.model.predict(X)
            
            # 转换为原始标签格式
            positive_label = self.target_config.get('positive_label', 1)
            negative_label = self.target_config.get('negative_label', -1)
            
            predictions_label = np.where(predictions_binary == 1, positive_label, negative_label)
            
            # 创建结果DataFrame
            result_df = df.copy()
            result_df['prediction_next_day_prob'] = predictions_proba  # 上涨概率
            result_df['prediction_next_day_direction'] = predictions_label  # 涨跌方向
            
            # 添加置信度（概率距离0.5的距离）
            result_df['prediction_confidence'] = np.abs(predictions_proba - 0.5) * 2
            
            logger.info(f"预测完成，{len(result_df)} 个样本")
            
            return result_df
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, y_proba: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        try:
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred),
                'precision': precision_score(y_true, y_pred, zero_division=0),
                'recall': recall_score(y_true, y_pred, zero_division=0),
                'f1_score': f1_score(y_true, y_pred, zero_division=0),
                'auc': roc_auc_score(y_true, y_proba) if len(np.unique(y_true)) > 1 else 0.0
            }
            return metrics
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            return {}
    
    def get_feature_importance(self) -> pd.DataFrame:
        """获取特征重要性"""
        try:
            if not self.is_trained or self.model is None:
                return pd.DataFrame()
            
            importance_df = pd.DataFrame({
                'feature': self.feature_columns,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            return importance_df
            
        except Exception as e:
            logger.error(f"获取特征重要性失败: {e}")
            return pd.DataFrame()
    
    def save_model(self, file_path: str):
        """保存模型"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存模型和相关信息
            model_data = {
                'model': self.model,
                'feature_columns': self.feature_columns,
                'training_metrics': self.training_metrics,
                'validation_metrics': self.validation_metrics,
                'config': self.model_config
            }
            
            joblib.dump(model_data, file_path)
            logger.info(f"模型已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def load_model(self, file_path: str):
        """加载模型"""
        try:
            model_data = joblib.load(file_path)
            
            self.model = model_data['model']
            self.feature_columns = model_data['feature_columns']
            self.training_metrics = model_data.get('training_metrics', {})
            self.validation_metrics = model_data.get('validation_metrics', {})
            self.is_trained = True
            
            logger.info(f"模型已从 {file_path} 加载")
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise


# 测试函数
def test_xgboost_binary():
    """测试XGBoost二分类模型"""
    logger.info("开始测试XGBoost二分类模型...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # 生成价格数据
    price = 10
    prices = []
    opens = []
    
    for i in range(200):
        # 生成开盘价
        if i == 0:
            open_price = price
        else:
            # 开盘价相对前一日收盘价有一定随机性
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        opens.append(open_price)
        
        # 生成收盘价
        close_price = open_price * (1 + np.random.normal(0, 0.02))
        prices.append(close_price)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': opens,
        'close': prices,
        'volume': 1000000 + np.random.randint(-200000, 200000, 200),
        'feature1': np.random.randn(200),
        'feature2': np.random.randn(200),
        'feature3': np.random.randn(200)
    })
    
    # 测试模型
    model = XGBoostBinary()
    
    # 训练模型
    training_result = model.train(test_data)
    print("训练结果:", training_result['validation_metrics'])
    
    # 预测
    predictions = model.predict(test_data.head(50))
    print("预测结果:")
    print(predictions[['trade_date', 'open', 'close', 'prediction_next_day_prob', 'prediction_next_day_direction']].head())
    
    logger.info("XGBoost二分类模型测试完成")


if __name__ == "__main__":
    test_xgboost_binary()
