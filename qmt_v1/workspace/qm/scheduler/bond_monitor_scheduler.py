"""
债券监控调度器

集成到现有调度系统中，每个交易日14:30执行债券监控
监控中短债、十年期长债、30年期长债、高等级信用债的当日利率涨跌幅
维护30天历史涨跌幅队列
"""

import schedule
import time
import threading
import sqlite3
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from contextlib import contextmanager

from utils.logger import setup_logger
from data.database import DatabaseManager

logger = setup_logger()


class BondMonitorScheduler:
    """债券监控调度器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化债券监控调度器"""
        self.db_manager = DatabaseManager(config_path)
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.monitor_time = "14:30"  # 每个交易日14:30执行
        self._init_bond_tables()
        
    def _init_bond_tables(self):
        """初始化债券相关数据表"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建债券监控数据表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_monitor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    bond_category TEXT NOT NULL,
                    yield_rate REAL,
                    daily_change REAL,
                    data_source TEXT,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, bond_category)
                )
                """)
                
                # 创建债券历史涨跌幅表（30天队列）
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_change_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    short_medium_term_change REAL DEFAULT 0.0,
                    ten_year_change REAL DEFAULT 0.0,
                    thirty_year_change REAL DEFAULT 0.0,
                    high_grade_credit_change REAL DEFAULT 0.0,
                    market_summary TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date)
                )
                """)
                
                # 创建债券监控日志表
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bond_monitor_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    monitor_time TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    data_count INTEGER DEFAULT 0,
                    error_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                conn.commit()
                logger.info("债券数据表初始化完成")
                
        except Exception as e:
            logger.error(f"初始化债券数据表失败: {e}")
            raise
    
    def is_trading_day(self, date: datetime = None) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 要判断的日期，默认为今天
            
        Returns:
            是否为交易日
        """
        if date is None:
            date = datetime.now()
        
        # 简单判断：周一到周五为交易日
        weekday = date.weekday()
        is_weekday = weekday < 5  # 0-4 表示周一到周五
        
        logger.info(f"日期 {date.strftime('%Y-%m-%d')} ({'周一二三四五六日'[weekday]}) "
                   f"{'是' if is_weekday else '不是'}交易日")
        
        return is_weekday
    
    def get_bond_data_simulation(self) -> Dict:
        """
        模拟获取债券数据（由于AkShare接口调用问题，暂时使用模拟数据）
        实际部署时需要替换为真实的AkShare接口调用
        """
        import random
        
        # 模拟债券收益率数据
        base_yields = {
            'short_medium_term': 2.5,  # 中短债基准收益率
            'ten_year': 3.2,           # 十年期基准收益率
            'thirty_year': 3.8,        # 30年期基准收益率
            'high_grade_credit': 3.5   # 高等级信用债基准收益率
        }
        
        # 模拟当日涨跌幅（基点）
        changes = {}
        for category, base_yield in base_yields.items():
            # 随机生成-20到+20基点的变化
            change = round(random.uniform(-20, 20), 2)
            changes[f'{category}_change'] = change
            changes[f'{category}_yield'] = base_yield + change / 100
        
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'yields': {k: v for k, v in changes.items() if '_yield' in k},
            'changes': {k: v for k, v in changes.items() if '_change' in k},
            'data_source': 'simulation'
        }
    
    def calculate_yield_changes(self, current_data: Dict) -> Dict[str, float]:
        """
        计算各类债券的收益率涨跌幅
        
        Args:
            current_data: 当前债券数据
            
        Returns:
            各类别债券的涨跌幅字典
        """
        try:
            # 从模拟数据中提取涨跌幅
            changes = current_data.get('changes', {})
            
            result = {
                'short_medium_term_change': changes.get('short_medium_term_change', 0.0),
                'ten_year_change': changes.get('ten_year_change', 0.0),
                'thirty_year_change': changes.get('thirty_year_change', 0.0),
                'high_grade_credit_change': changes.get('high_grade_credit_change', 0.0)
            }
            
            logger.info("计算得到的涨跌幅:")
            for category, change in result.items():
                logger.info(f"  {category}: {change} bp")
            
            return result
            
        except Exception as e:
            logger.error(f"计算收益率涨跌幅失败: {e}")
            return {
                'short_medium_term_change': 0.0,
                'ten_year_change': 0.0,
                'thirty_year_change': 0.0,
                'high_grade_credit_change': 0.0
            }
    
    def generate_market_summary(self, changes: Dict[str, float]) -> str:
        """
        生成市场总结
        
        Args:
            changes: 涨跌幅数据
            
        Returns:
            市场总结文本
        """
        try:
            summary_parts = []
            
            # 分析各类别债券表现
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                if change > 0:
                    summary_parts.append(f"{name}上涨{change}bp")
                elif change < 0:
                    summary_parts.append(f"{name}下跌{abs(change)}bp")
                else:
                    summary_parts.append(f"{name}持平")
            
            # 整体市场判断
            total_change = sum(changes.values())
            if total_change > 5:
                market_trend = "债券收益率整体上行"
            elif total_change < -5:
                market_trend = "债券收益率整体下行"
            else:
                market_trend = "债券收益率整体平稳"
            
            summary = f"{market_trend}。{'; '.join(summary_parts)}。"
            
            logger.info(f"生成市场总结: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"生成市场总结失败: {e}")
            return "市场数据获取异常"
    
    def save_bond_data(self, date: str, changes: Dict[str, float], 
                      market_summary: str) -> bool:
        """
        保存债券监控数据
        
        Args:
            date: 日期
            changes: 涨跌幅数据
            market_summary: 市场总结
            
        Returns:
            是否保存成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 保存当日涨跌幅数据
                cursor.execute("""
                INSERT OR REPLACE INTO bond_change_history 
                (date, short_medium_term_change, ten_year_change, 
                 thirty_year_change, high_grade_credit_change, market_summary)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    date,
                    changes.get('short_medium_term_change', 0.0),
                    changes.get('ten_year_change', 0.0),
                    changes.get('thirty_year_change', 0.0),
                    changes.get('high_grade_credit_change', 0.0),
                    market_summary
                ))
                
                conn.commit()
                logger.info(f"保存债券数据成功: {date}")
                return True
                
        except Exception as e:
            logger.error(f"保存债券数据失败: {e}")
            return False
    
    def cleanup_old_data(self, keep_days: int = 30):
        """
        清理超过指定天数的旧数据
        
        Args:
            keep_days: 保留天数
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=keep_days)).strftime('%Y-%m-%d')
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 清理旧的监控数据
                cursor.execute("DELETE FROM bond_monitor_data WHERE date < ?", (cutoff_date,))
                deleted_monitor = cursor.rowcount
                
                # 清理旧的涨跌幅历史数据
                cursor.execute("DELETE FROM bond_change_history WHERE date < ?", (cutoff_date,))
                deleted_history = cursor.rowcount
                
                # 清理旧的日志数据
                cursor.execute("DELETE FROM bond_monitor_logs WHERE date < ?", (cutoff_date,))
                deleted_logs = cursor.rowcount
                
                conn.commit()
                
                logger.info(f"数据清理完成: 监控数据 {deleted_monitor} 条, "
                          f"历史数据 {deleted_history} 条, 日志 {deleted_logs} 条")
                
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
    
    def log_monitor_activity(self, date: str, monitor_time: str, status: str, 
                           message: str = "", data_count: int = 0, 
                           error_info: str = "") -> bool:
        """
        记录监控活动日志
        
        Args:
            date: 日期
            monitor_time: 监控时间
            status: 状态 (success/error/warning)
            message: 消息
            data_count: 数据条数
            error_info: 错误信息
            
        Returns:
            是否记录成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO bond_monitor_logs 
                (date, monitor_time, status, message, data_count, error_info)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (date, monitor_time, status, message, data_count, error_info))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"记录监控日志失败: {e}")
            return False
    
    def run_bond_monitor_job(self):
        """执行债券监控任务"""
        monitor_time = datetime.now()
        date_str = monitor_time.strftime('%Y-%m-%d')
        time_str = monitor_time.strftime('%H:%M:%S')
        
        logger.info(f"开始执行债券监控任务: {date_str} {time_str}")
        
        try:
            # 检查是否为交易日
            if not self.is_trading_day(monitor_time):
                logger.info("今日非交易日，跳过债券监控任务")
                self.log_monitor_activity(
                    date_str, time_str, 'info', '今日非交易日，跳过监控'
                )
                return
            
            # 获取债券数据（目前使用模拟数据）
            logger.info("获取债券数据...")
            bond_data = self.get_bond_data_simulation()
            
            if not bond_data.get('success', False):
                error_msg = '获取债券数据失败'
                logger.error(error_msg)
                self.log_monitor_activity(
                    date_str, time_str, 'error', error_msg, 0, 
                    bond_data.get('error', '')
                )
                return
            
            # 计算涨跌幅
            logger.info("计算当日涨跌幅...")
            changes = self.calculate_yield_changes(bond_data)
            
            # 生成市场总结
            market_summary = self.generate_market_summary(changes)
            
            # 保存数据
            logger.info("保存监控数据...")
            save_success = self.save_bond_data(date_str, changes, market_summary)
            
            if save_success:
                # 记录成功日志
                self.log_monitor_activity(
                    date_str, time_str, 'success', '债券监控完成', 4
                )
                
                # 清理旧数据
                self.cleanup_old_data(30)
                
                # 输出监控结果
                self._log_monitor_result(date_str, time_str, changes, market_summary)
                
                logger.info(f"债券监控任务完成: {market_summary}")
            else:
                error_msg = '保存监控数据失败'
                logger.error(error_msg)
                self.log_monitor_activity(
                    date_str, time_str, 'error', error_msg
                )
            
        except Exception as e:
            error_msg = f"债券监控任务异常: {e}"
            logger.error(error_msg)
            self.log_monitor_activity(
                date_str, time_str, 'error', error_msg, 0, str(e)
            )
    
    def _log_monitor_result(self, date: str, time: str, changes: Dict[str, float], 
                          summary: str):
        """记录监控结果"""
        try:
            logger.info("=== 债券监控结果 ===")
            logger.info(f"监控日期: {date}")
            logger.info(f"监控时间: {time}")
            logger.info(f"市场总结: {summary}")
            
            logger.info("各类债券涨跌幅:")
            categories_cn = {
                'short_medium_term_change': '中短债',
                'ten_year_change': '十年期长债',
                'thirty_year_change': '30年期长债',
                'high_grade_credit_change': '高等级信用债'
            }
            
            for key, name in categories_cn.items():
                change = changes.get(key, 0.0)
                direction = "上涨" if change > 0 else "下跌" if change < 0 else "持平"
                logger.info(f"  {name}: {direction} {abs(change)} bp")
            
            logger.info("==================")
            
        except Exception as e:
            logger.error(f"记录监控结果失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置债券监控定时任务...")
        
        # 设置每个交易日14:30的监控任务
        schedule.every().monday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().tuesday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().wednesday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().thursday.at(self.monitor_time).do(self.run_bond_monitor_job)
        schedule.every().friday.at(self.monitor_time).do(self.run_bond_monitor_job)
        
        logger.info(f"已设置债券监控定时任务: 每个交易日 {self.monitor_time}")
        
        # 设置每日数据清理任务（凌晨2点）
        schedule.every().day.at("02:00").do(self.cleanup_old_data, 30)
        
        logger.info("债券监控定时任务设置完成")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("债券监控调度器已在运行中")
            return
        
        logger.info("启动债券监控调度器...")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("债券监控调度器启动成功")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("债券监控调度器未在运行")
            return
        
        logger.info("停止债券监控调度器...")
        
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("债券监控调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("债券监控调度器主循环开始...")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
        
        logger.info("债券监控调度器主循环结束")
    
    def run_manual_monitor(self):
        """手动执行一次债券监控"""
        logger.info("手动执行债券监控任务...")
        self.run_bond_monitor_job()
    
    def get_30day_history(self) -> Optional[pd.DataFrame]:
        """获取30天历史数据"""
        try:
            thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            with self.db_manager.get_connection() as conn:
                query = """
                SELECT * FROM bond_change_history 
                WHERE date >= ? 
                ORDER BY date DESC
                LIMIT 30
                """
                
                df = pd.read_sql_query(query, conn, params=(thirty_days_ago,))
                
                if not df.empty:
                    logger.info(f"获取30天历史数据成功: {len(df)} 条记录")
                    return df
                else:
                    logger.warning("30天历史数据为空")
                    return None
                    
        except Exception as e:
            logger.error(f"获取30天历史数据失败: {e}")
            return None
