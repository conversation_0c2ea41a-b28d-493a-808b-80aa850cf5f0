#!/bin/bash

# 量化交易系统启动脚本

echo "==================================="
echo "    量化交易系统 (QMT v1.0)"
echo "==================================="

# 检查conda环境
if ! command -v conda &> /dev/null; then
    echo "错误：未找到conda命令，请先安装Anaconda或Miniconda"
    exit 1
fi

# 激活conda环境
echo "激活conda环境: qmt"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate qmt

if [ $? -ne 0 ]; then
    echo "错误：无法激活conda环境qmt，请先创建环境："
    echo "conda create -n qmt python=3.9 -y"
    exit 1
fi

# 检查当前目录
if [ ! -f "main.py" ]; then
    echo "错误：请在项目根目录运行此脚本"
    exit 1
fi

# 显示菜单
echo ""
echo "请选择运行模式："
echo "1. 基本功能测试"
echo "2. 训练模型"
echo "3. 执行预测"
echo "4. 启动Web界面"
echo "5. 查看系统状态"
echo "0. 退出"
echo ""

read -p "请输入选项 (0-5): " choice

case $choice in
    1)
        echo "开始基本功能测试..."
        python main.py --mode test
        ;;
    2)
        echo "开始训练模型..."
        python main.py --mode train
        ;;
    3)
        echo "开始执行预测..."
        python main.py --mode predict
        ;;
    4)
        echo "启动Web界面..."
        echo "Web界面将在 http://127.0.0.1:5001 启动"
        echo "按 Ctrl+C 停止服务"
        python web/app.py
        ;;
    5)
        echo "系统状态："
        echo "- Python版本: $(python --version)"
        echo "- 当前目录: $(pwd)"
        echo "- 数据库文件: $(ls -la data/*.db 2>/dev/null || echo '未找到数据库文件')"
        echo "- 模型文件: $(ls -la models/*.pkl 2>/dev/null || echo '未找到模型文件')"
        echo "- 日志文件: $(ls -la logs/*.log 2>/dev/null || echo '未找到日志文件')"
        ;;
    0)
        echo "退出系统"
        exit 0
        ;;
    *)
        echo "无效选项，请重新运行脚本"
        exit 1
        ;;
esac

echo ""
echo "操作完成！"
