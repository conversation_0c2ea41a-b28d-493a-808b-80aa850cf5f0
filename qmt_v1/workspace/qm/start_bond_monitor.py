#!/usr/bin/env python3
"""
债券监控系统启动脚本

专注于中国债券市场监控：
- 中短债（1-3年期）
- 十年期长债
- 30年期长债
- 高等级信用债（AAA级）

每个交易日14:30执行监控，维护30天历史涨跌幅队列
"""

import sys
import os
import argparse
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from utils.logger import setup_logger
from scheduler.bond_monitor_scheduler import BondMonitorScheduler

logger = setup_logger()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='债券监控系统')
    
    parser.add_argument('action', choices=[
        'start', 'monitor', 'status', 'history'
    ], help='执行的操作')
    
    parser.add_argument('--days', type=int, default=7, 
                       help='历史数据显示天数（默认7天）')
    
    args = parser.parse_args()
    
    try:
        scheduler = BondMonitorScheduler()
        
        if args.action == 'start':
            # 启动调度器
            print("启动债券监控调度器...")
            print(f"监控时间: 每个交易日 14:30")
            print("按 Ctrl+C 停止")
            print("=" * 50)
            
            scheduler.start()
            
            # 保持运行
            import time
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n正在停止债券监控调度器...")
                scheduler.stop()
                print("债券监控调度器已停止")
        
        elif args.action == 'monitor':
            # 手动执行一次监控
            print("执行债券监控...")
            print("=" * 50)
            scheduler.run_manual_monitor()
            print("=" * 50)
            print("监控完成")
        
        elif args.action == 'status':
            # 显示状态
            print("债券监控系统状态")
            print("=" * 50)
            
            # 检查今日是否交易日
            is_trading = scheduler.is_trading_day()
            print(f"今日是否交易日: {'是' if is_trading else '否'}")
            print(f"监控时间: 每个交易日 14:30")
            print()
            
            # 显示最近的监控记录
            try:
                from data.database import DatabaseManager
                db = DatabaseManager()
                
                with db.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 获取最近的监控日志
                    cursor.execute("""
                    SELECT date, monitor_time, status, message 
                    FROM bond_monitor_logs 
                    ORDER BY created_at DESC 
                    LIMIT 5
                    """)
                    
                    logs = cursor.fetchall()
                    if logs:
                        print("最近监控记录:")
                        for log in logs:
                            print(f"  {log[0]} {log[1]} - {log[2]}: {log[3]}")
                    else:
                        print("暂无监控记录")
                        
            except Exception as e:
                print(f"获取状态信息失败: {e}")
        
        elif args.action == 'history':
            # 显示历史数据
            print(f"最近{args.days}天债券涨跌幅历史")
            print("=" * 80)
            
            history = scheduler.get_30day_history()
            
            if history is not None and not history.empty:
                # 只显示最近指定天数的数据
                recent_history = history.head(args.days)
                
                print(f"{'日期':<12} {'中短债':<8} {'十年期':<8} {'30年期':<8} {'信用债':<8} {'市场总结'}")
                print("-" * 80)
                
                for _, row in recent_history.iterrows():
                    date = row.get('date', 'N/A')
                    short_change = row.get('short_medium_term_change', 0.0)
                    ten_change = row.get('ten_year_change', 0.0)
                    thirty_change = row.get('thirty_year_change', 0.0)
                    credit_change = row.get('high_grade_credit_change', 0.0)
                    summary = row.get('market_summary', '')
                    
                    # 截断过长的总结
                    if len(summary) > 20:
                        summary = summary[:20] + '...'
                    
                    print(f"{date:<12} {short_change:>6.1f}bp {ten_change:>6.1f}bp "
                          f"{thirty_change:>6.1f}bp {credit_change:>6.1f}bp {summary}")
            else:
                print("暂无历史数据")
            
            print("=" * 80)
        
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"执行操作失败: {e}")
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
