#!/usr/bin/env python3
"""
启动现代化量化交易系统Web界面
"""
import os
import sys
import time
import webbrowser
from loguru import logger

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动现代化Web界面"""
    logger.info("🚀 启动现代化量化交易系统")
    
    # 显示启动信息
    print("=" * 60)
    print("🎨 现代化量化交易系统 v2.0")
    print("=" * 60)
    print("✨ 全新设计的现代化界面")
    print("🔧 量化因子工程管理")
    print("📊 策略回测可视化")
    print("🤖 AI驱动的智能预测")
    print("⚡ 实时数据和调度")
    print("=" * 60)
    
    # 检查环境
    try:
        import flask
        import pandas
        import numpy
        import sklearn
        logger.info("✅ 依赖检查通过")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖: {e}")
        print("\n请安装所需依赖:")
        print("pip install flask pandas numpy scikit-learn xgboost loguru pyyaml")
        return
    
    # 启动Web服务器
    try:
        logger.info("🌐 启动Web服务器...")
        
        # 导入并启动应用
        from web.app import app
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            url = "http://127.0.0.1:5001"
            logger.info(f"🔗 打开浏览器: {url}")
            webbrowser.open(url)
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🌟 现代化界面功能:")
        print("• 深色主题设计")
        print("• 动态背景效果")
        print("• 流畅的动画交互")
        print("• 响应式布局")
        print("• 实时状态更新")
        print("• 智能通知系统")
        
        print("\n📱 访问地址:")
        print("• 现代化界面: http://127.0.0.1:5001")
        print("• 经典界面: http://127.0.0.1:5001/classic")
        
        print("\n🎯 主要功能:")
        print("• 量化因子工程 - 20+个专业因子")
        print("• 模型训练管理 - 支持因子组合")
        print("• 策略回测分析 - 完整回测指标")
        print("• 实时预测系统 - AI驱动预测")
        print("• 定时调度任务 - 自动化交易")
        
        print("\n" + "=" * 60)
        print("🚀 系统已启动，按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        logger.info("\n👋 用户停止服务")
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        print(f"\n错误详情: {e}")
        print("\n请检查:")
        print("1. 端口5001是否被占用")
        print("2. 依赖是否正确安装")
        print("3. 配置文件是否存在")
    
    print("\n感谢使用现代化量化交易系统! 🎉")


if __name__ == "__main__":
    main()
