#!/usr/bin/env python3
"""
时间感知系统测试脚本
Test Script for Time-Aware System

测试时间感知的数据管理、模型训练和预测功能。

🧪 测试内容:
1. 时间感知数据管理器测试
   - 交易日历功能测试
   - 数据分割功能测试（按天数和日期范围）
   - 时间感知训练数据获取测试

2. 时间感知模型管理器测试
   - 时间感知模型训练测试
   - 早盘和尾盘预测测试
   - 模型性能对比测试

3. 时间感知调度器测试
   - 交易日检测测试
   - 调度器状态测试
   - 手动预测测试

4. Web API接口测试
   - 时间感知API接口测试
   - 数据分割API测试
   - 调度器管理API测试

5. 完整流程集成测试
   - 端到端功能测试
   - 错误处理测试
   - 性能测试

📝 使用方法:
```bash
# 激活conda环境
conda activate qmt

# 进入项目目录
cd /Users/<USER>/Desktop/cursor_temp/qmt_v1/workspace/qm

# 运行测试
python test_time_aware_system.py
```

🎯 测试目标:
- 验证时间感知功能的正确性
- 确保API接口的稳定性
- 检查错误处理的完整性
- 验证系统的整体性能

作者: Augment Agent
日期: 2025-06-17
版本: v2.1
"""

import sys
import os
import time
import requests
import json
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.time_aware_data_manager import TimeAwareDataManager
from models.time_aware_model_manager import TimeAwareModelManager
from scheduler.time_aware_scheduler import TimeAwareScheduler


def test_time_aware_data_manager():
    """测试时间感知数据管理器"""
    print("\n" + "="*60)
    print("🧪 测试时间感知数据管理器")
    print("="*60)
    
    try:
        # 创建数据管理器
        data_manager = TimeAwareDataManager()
        
        # 测试1: 交易日历功能
        print("\n📅 测试1: 交易日历功能")
        recent_days = data_manager.get_recent_trading_days(10)
        print(f"最近10个交易日: {[day.strftime('%Y-%m-%d') for day in recent_days]}")
        
        # 测试2: 按交易日数量分割数据
        print("\n📊 测试2: 按交易日数量分割数据")
        config1 = {
            'train_days': 60,
            'backtest_days': 20,
            'prediction_days': 10
        }
        result1 = data_manager.split_data_by_trading_days(**config1)
        print(f"训练期间: {result1['summary']['train_start']} 到 {result1['summary']['train_end']}")
        print(f"回测期间: {result1['summary']['backtest_start']} 到 {result1['summary']['backtest_end']}")
        print(f"训练天数: {result1['summary']['train_days']}, 回测天数: {result1['summary']['backtest_days']}")
        
        # 测试3: 按日期范围分割数据
        print("\n📈 测试3: 按日期范围分割数据")
        config2 = {
            'train_days': ('2023-06-01', '2023-12-29'),
            'backtest_days': 30,
            'prediction_days': 10
        }
        result2 = data_manager.split_data_by_trading_days(**config2)
        print(f"训练期间: {result2['summary']['train_start']} 到 {result2['summary']['train_end']}")
        print(f"回测期间: {result2['summary']['backtest_start']} 到 {result2['summary']['backtest_end']}")
        
        # 测试4: 获取时间感知训练数据
        print("\n🕐 测试4: 获取时间感知训练数据")
        stock_codes = ['000001', '000002']
        
        # 早盘数据
        morning_data = data_manager.get_time_aware_training_data(
            stock_codes, config1, prediction_time="morning"
        )
        print(f"早盘训练数据: {len(morning_data['data'])} 只股票")
        
        # 尾盘数据
        afternoon_data = data_manager.get_time_aware_training_data(
            stock_codes, config1, prediction_time="afternoon"
        )
        print(f"尾盘训练数据: {len(afternoon_data['data'])} 只股票")
        
        print("✅ 时间感知数据管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间感知数据管理器测试失败: {e}")
        return False


def test_time_aware_model_manager():
    """测试时间感知模型管理器"""
    print("\n" + "="*60)
    print("🤖 测试时间感知模型管理器")
    print("="*60)
    
    try:
        # 创建模型管理器
        model_manager = TimeAwareModelManager()
        
        # 测试配置
        stock_codes = ['000001', '000002']
        train_config = {
            'train_days': 60,
            'backtest_days': 20,
            'prediction_days': 10
        }
        factor_names = ['momentum_10d', 'volume_ratio']
        
        print("\n🏋️ 测试1: 时间感知模型训练")
        print(f"股票池: {stock_codes}")
        print(f"训练配置: {train_config}")
        print(f"使用因子: {factor_names}")
        
        # 训练时间感知模型
        training_results = model_manager.train_time_aware_models(
            model_names=['xgboost_next_day_direction'],
            stock_codes=stock_codes,
            train_config=train_config,
            factor_names=factor_names,
            train_both_times=True
        )
        
        print(f"训练结果: {list(training_results.keys())}")
        
        # 检查训练结果
        success_count = sum(1 for result in training_results.values() if result.get('success', False))
        print(f"成功训练模型数: {success_count}/{len(training_results)}")
        
        # 测试2: 时间感知预测
        print("\n🔮 测试2: 时间感知预测")
        
        # 早盘预测
        morning_predictions = model_manager.predict_with_time_awareness(
            stock_codes=stock_codes,
            prediction_time='morning',
            model_names=['xgboost_next_day_direction'],
            prediction_days=10
        )
        print(f"早盘预测结果: {list(morning_predictions.keys())}")
        
        # 尾盘预测
        afternoon_predictions = model_manager.predict_with_time_awareness(
            stock_codes=stock_codes,
            prediction_time='afternoon',
            model_names=['xgboost_next_day_direction'],
            prediction_days=10
        )
        print(f"尾盘预测结果: {list(afternoon_predictions.keys())}")
        
        # 测试3: 模型性能对比
        print("\n📊 测试3: 模型性能对比")
        performance = model_manager.get_model_performance_comparison()
        print(f"性能对比数据: {list(performance.keys())}")
        
        print("✅ 时间感知模型管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间感知模型管理器测试失败: {e}")
        return False


def test_time_aware_scheduler():
    """测试时间感知调度器"""
    print("\n" + "="*60)
    print("⏰ 测试时间感知调度器")
    print("="*60)
    
    try:
        # 创建调度器
        scheduler = TimeAwareScheduler()
        
        # 测试1: 交易日检查
        print("\n📅 测试1: 交易日检查")
        today = datetime.now()
        is_trading = scheduler.is_trading_day(today)
        print(f"今日 ({today.strftime('%Y-%m-%d %A')}) 是否为交易日: {is_trading}")
        
        # 测试几个特定日期
        test_dates = [
            datetime(2024, 1, 1),   # 元旦
            datetime(2024, 6, 10),  # 端午节
            datetime(2024, 12, 25), # 圣诞节（工作日）
            datetime(2024, 12, 28), # 周六
        ]
        
        for test_date in test_dates:
            is_trading = scheduler.is_trading_day(test_date)
            print(f"{test_date.strftime('%Y-%m-%d %A')}: {is_trading}")
        
        # 测试2: 调度器状态
        print("\n📊 测试2: 调度器状态")
        status = scheduler.get_scheduler_status()
        print(f"调度器运行状态: {status['is_running']}")
        print(f"计划任务数: {status['scheduled_jobs']}")
        
        # 测试3: 手动预测
        print("\n🔮 测试3: 手动预测")
        
        # 早盘预测
        morning_result = scheduler.manual_prediction('morning')
        print(f"手动早盘预测: {morning_result}")
        
        # 尾盘预测
        afternoon_result = scheduler.manual_prediction('afternoon')
        print(f"手动尾盘预测: {afternoon_result}")
        
        print("✅ 时间感知调度器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间感知调度器测试失败: {e}")
        return False


def test_web_api_interfaces():
    """测试Web API接口"""
    print("\n" + "="*60)
    print("🌐 测试Web API接口")
    print("="*60)
    
    base_url = 'http://127.0.0.1:5001/api/time_aware'
    
    try:
        # 测试1: 交易日接口
        print("\n📅 测试1: 交易日接口")
        response = requests.get(f'{base_url}/trading_days?num_days=10', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                trading_days = data['data']['trading_days']
                print(f"最近10个交易日: {trading_days[:5]}...")
                print(f"交易日总数: {data['data']['count']}")
            else:
                print(f"接口返回错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.status_code}")
        
        # 测试2: 数据分割接口
        print("\n📊 测试2: 数据分割接口")
        split_data = {
            'train_days': 60,
            'backtest_days': 20,
            'prediction_days': 10
        }
        response = requests.post(f'{base_url}/data_split', json=split_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                summary = data['data']['summary']
                print(f"训练天数: {summary['train_days']}, 回测天数: {summary['backtest_days']}")
                print(f"训练期间: {summary['train_start']} 到 {summary['train_end']}")
            else:
                print(f"接口返回错误: {data.get('error')}")
        
        # 测试3: 调度器状态接口
        print("\n⏰ 测试3: 调度器状态接口")
        response = requests.get(f'{base_url}/scheduler/status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['data']
                print(f"调度器状态: {'运行中' if status['is_running'] else '已停止'}")
                print(f"计划任务数: {status['scheduled_jobs']}")
            else:
                print(f"接口返回错误: {data.get('error')}")
        
        # 测试4: 时间感知预测接口
        print("\n🔮 测试4: 时间感知预测接口")
        predict_data = {
            'stock_codes': ['000001', '000002'],
            'prediction_time': 'morning',
            'models': ['xgboost_next_day_direction'],
            'prediction_days': 10
        }
        response = requests.post(f'{base_url}/predict', json=predict_data, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                predictions = data['data']
                print(f"预测结果: {list(predictions.keys())}")
            else:
                print(f"预测失败: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.status_code}")
        
        print("✅ Web API接口测试通过")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器，请先启动 python web/app.py")
        return False
    except Exception as e:
        print(f"❌ Web API接口测试失败: {e}")
        return False


def run_comprehensive_test():
    """运行完整测试"""
    print("🚀 开始时间感知系统完整测试")
    print("="*80)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("时间感知数据管理器", test_time_aware_data_manager),
        ("时间感知模型管理器", test_time_aware_model_manager),
        ("时间感知调度器", test_time_aware_scheduler),
        ("Web API接口", test_web_api_interfaces),
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 测试总结
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！时间感知系统功能正常。")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 项测试失败，请检查相关功能。")
    
    return passed_tests == total_tests


def main():
    """主函数"""
    print("时间感知量化交易系统测试")
    print("Time-Aware Quantitative Trading System Test")
    print("="*80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print("="*80)
    
    # 运行完整测试
    success = run_comprehensive_test()
    
    print("\n" + "="*80)
    if success:
        print("🎉 时间感知系统测试完成，所有功能正常！")
        print("\n💡 使用建议:")
        print("1. 启动Web服务: python web/app.py")
        print("2. 访问现代化界面: http://127.0.0.1:5001")
        print("3. 使用时间感知训练: 配置训练天数或日期范围")
        print("4. 启动定时调度: 9:35早盘预测，14:50尾盘预测")
    else:
        print("⚠️  时间感知系统测试发现问题，请检查相关功能。")
    
    print("="*80)


if __name__ == "__main__":
    main()
