"""
辅助函数模块
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import os
import json


def create_sample_data(num_days: int = 100, start_date: str = "2023-01-01") -> pd.DataFrame:
    """
    创建样本股票数据用于测试
    
    Args:
        num_days: 数据天数
        start_date: 开始日期
        
    Returns:
        样本数据DataFrame
    """
    np.random.seed(42)
    
    dates = pd.date_range(start_date, periods=num_days, freq='D')
    
    # 生成价格数据
    price = 10.0
    prices = []
    opens = []
    highs = []
    lows = []
    volumes = []
    amounts = []
    
    for i in range(num_days):
        # 开盘价
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        # 收盘价
        close_price = open_price * (1 + np.random.normal(0, 0.02))
        
        # 最高价和最低价
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.02))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.02))
        
        # 成交量和成交额
        volume = 1000000 + np.random.randint(-200000, 200000)
        amount = volume * (open_price + close_price) / 2
        
        opens.append(open_price)
        prices.append(close_price)
        highs.append(high_price)
        lows.append(low_price)
        volumes.append(volume)
        amounts.append(amount)
    
    return pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'open': opens,
        'close': prices,
        'high': highs,
        'low': lows,
        'volume': volumes,
        'amount': amounts,
        'pre_close': [np.nan] + prices[:-1],
        'change': [0] + [prices[i] - prices[i-1] for i in range(1, len(prices))],
        'change_pct': [0] + [(prices[i] - prices[i-1]) / prices[i-1] * 100 for i in range(1, len(prices))],
        'turnover_ratio': np.random.uniform(0.1, 2.0, num_days)
    })


def validate_data(df: pd.DataFrame, required_columns: List[str]) -> bool:
    """
    验证数据完整性
    
    Args:
        df: 数据DataFrame
        required_columns: 必需的列名列表
        
    Returns:
        是否验证通过
    """
    if df.empty:
        return False
    
    for col in required_columns:
        if col not in df.columns:
            return False
    
    return True


def save_json(data: Any, file_path: str):
    """
    保存JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    def convert_numpy(obj):
        """转换numpy类型为Python原生类型"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    data_serializable = convert_numpy(data)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data_serializable, f, indent=2, ensure_ascii=False)


def load_json(file_path: str) -> Any:
    """
    加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的数据
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def calculate_returns(prices: pd.Series, periods: int = 1) -> pd.Series:
    """
    计算收益率
    
    Args:
        prices: 价格序列
        periods: 计算周期
        
    Returns:
        收益率序列
    """
    return prices.pct_change(periods=periods)


def calculate_volatility(returns: pd.Series, window: int = 20) -> pd.Series:
    """
    计算波动率
    
    Args:
        returns: 收益率序列
        window: 计算窗口
        
    Returns:
        波动率序列
    """
    return returns.rolling(window=window).std()


def is_trading_day(date: datetime) -> bool:
    """
    简单判断是否为交易日（周一到周五）
    
    Args:
        date: 日期
        
    Returns:
        是否为交易日
    """
    return date.weekday() < 5


def get_trading_days(start_date: str, end_date: str) -> List[str]:
    """
    获取交易日列表
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        交易日列表
    """
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    trading_days = []
    current = start
    
    while current <= end:
        if is_trading_day(current):
            trading_days.append(current.strftime('%Y-%m-%d'))
        current += timedelta(days=1)
    
    return trading_days


def format_number(num: float, precision: int = 4) -> str:
    """
    格式化数字显示
    
    Args:
        num: 数字
        precision: 精度
        
    Returns:
        格式化后的字符串
    """
    if abs(num) >= 1e9:
        return f"{num/1e9:.{precision}f}B"
    elif abs(num) >= 1e6:
        return f"{num/1e6:.{precision}f}M"
    elif abs(num) >= 1e3:
        return f"{num/1e3:.{precision}f}K"
    else:
        return f"{num:.{precision}f}"


def ensure_dir(directory: str):
    """
    确保目录存在
    
    Args:
        directory: 目录路径
    """
    os.makedirs(directory, exist_ok=True)


def get_latest_file(directory: str, pattern: str = None) -> Optional[str]:
    """
    获取目录中最新的文件
    
    Args:
        directory: 目录路径
        pattern: 文件名模式
        
    Returns:
        最新文件路径
    """
    if not os.path.exists(directory):
        return None
    
    files = os.listdir(directory)
    
    if pattern:
        files = [f for f in files if pattern in f]
    
    if not files:
        return None
    
    files.sort(key=lambda x: os.path.getmtime(os.path.join(directory, x)), reverse=True)
    return os.path.join(directory, files[0])


def clean_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """
    清理DataFrame数据
    
    Args:
        df: 输入DataFrame
        
    Returns:
        清理后的DataFrame
    """
    # 复制数据
    cleaned_df = df.copy()
    
    # 处理无穷大值
    cleaned_df = cleaned_df.replace([np.inf, -np.inf], np.nan)
    
    # 删除全为NaN的行
    cleaned_df = cleaned_df.dropna(how='all')
    
    return cleaned_df
