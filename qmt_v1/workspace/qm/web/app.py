"""
量化交易系统Web界面
"""
import os
import sys
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from datetime import datetime, timedelta
import pandas as pd
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import setup_logger
from utils.helpers import create_sample_data
from data.database import DatabaseManager
from models.model_manager import ModelManager
from scheduler.prediction_scheduler import PredictionScheduler
from scheduler.time_aware_scheduler import TimeAwareScheduler
from models.time_aware_model_manager import TimeAwareModelManager
from data.time_aware_data_manager import TimeAwareDataManager
from features.feature_engine import FeatureEngine
from backtest.backtest_engine import BacktestEngine
from data.stock_data_manager import StockDataManager

# 设置日志
logger = setup_logger()

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 初始化组件
db = DatabaseManager()
model_manager = ModelManager()
scheduler = PredictionScheduler()
feature_engine = FeatureEngine()
backtest_engine = BacktestEngine()
stock_data_manager = StockDataManager()

# 初始化时间感知组件
try:
    time_aware_scheduler = TimeAwareScheduler()
    time_aware_model_manager = TimeAwareModelManager()
    time_aware_data_manager = TimeAwareDataManager()
    logger.info("时间感知组件初始化成功")
except Exception as e:
    logger.error(f"时间感知组件初始化失败: {e}")
    time_aware_scheduler = None
    time_aware_model_manager = None
    time_aware_data_manager = None


@app.route('/')
def index():
    """主页 - 现代化界面"""
    return render_template('index_modern.html')

@app.route('/classic')
def classic():
    """经典界面"""
    return render_template('index.html')


@app.route('/api/status')
def get_status():
    """获取系统状态"""
    try:
        status = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'models': model_manager.get_model_status(),
            'scheduler': scheduler.get_status(),
            'database': 'connected'
        }
        return jsonify({'success': True, 'data': status})
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/predictions')
def get_predictions():
    """获取预测结果"""
    try:
        # 获取查询参数
        stock_code = request.args.get('stock_code', '000001')
        days = int(request.args.get('days', 7))
        
        # 从数据库获取预测结果
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        predictions_df = db.get_predictions(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        if predictions_df.empty:
            return jsonify({'success': True, 'data': []})
        
        # 转换为JSON格式
        predictions_list = []
        for _, row in predictions_df.iterrows():
            predictions_list.append({
                'stock_code': row['stock_code'],
                'model_name': row['model_name'],
                'prediction_date': row['prediction_date'].strftime('%Y-%m-%d') if pd.notna(row['prediction_date']) else None,
                'prediction_time': str(row['prediction_time']) if pd.notna(row['prediction_time']) else None,
                'target_date': row['target_date'].strftime('%Y-%m-%d') if pd.notna(row['target_date']) else None,
                'prediction_value': float(row['prediction_value']) if pd.notna(row['prediction_value']) else None,
                'confidence': float(row['confidence']) if pd.notna(row['confidence']) else None
            })
        
        return jsonify({'success': True, 'data': predictions_list})
        
    except Exception as e:
        logger.error(f"获取预测结果失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/market_data')
def get_market_data():
    """获取行情数据"""
    try:
        # 获取查询参数
        stock_code = request.args.get('stock_code', '000001')
        days = int(request.args.get('days', 30))
        
        # 从数据库获取行情数据
        market_data_df = db.get_market_data(stock_code=stock_code, limit=days)
        
        if market_data_df.empty:
            # 如果数据库没有数据，生成示例数据
            market_data_df = create_sample_data(num_days=days)
            market_data_df['stock_code'] = stock_code
        
        # 转换为JSON格式
        market_data_list = []
        for _, row in market_data_df.iterrows():
            market_data_list.append({
                'trade_date': row['trade_date'].strftime('%Y-%m-%d') if pd.notna(row['trade_date']) else None,
                'stock_code': row['stock_code'],
                'open': float(row['open']) if pd.notna(row['open']) else None,
                'close': float(row['close']) if pd.notna(row['close']) else None,
                'high': float(row['high']) if pd.notna(row['high']) else None,
                'low': float(row['low']) if pd.notna(row['low']) else None,
                'volume': int(row['volume']) if pd.notna(row['volume']) else None,
                'change_pct': float(row['change_pct']) if pd.notna(row['change_pct']) else None
            })
        
        return jsonify({'success': True, 'data': market_data_list})
        
    except Exception as e:
        logger.error(f"获取行情数据失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/predict', methods=['POST'])
def run_prediction():
    """执行预测"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code', '000001')
        
        logger.info(f"开始为股票 {stock_code} 执行预测...")
        
        # 获取股票数据（这里使用示例数据）
        stock_data = create_sample_data(num_days=60)
        stock_data['stock_code'] = stock_code
        
        # 计算特征
        features_data = feature_engine.calculate_features(stock_data)
        
        # 执行预测
        predictions = model_manager.get_combined_predictions(features_data.tail(1))
        
        if predictions.empty:
            return jsonify({'success': False, 'error': '预测失败，没有返回结果'})
        
        # 提取预测结果
        latest_prediction = predictions.iloc[-1]
        
        # 保存预测结果到数据库
        current_time = datetime.now()
        for col in predictions.columns:
            if 'prediction' in col:
                prediction_record = {
                    'stock_code': stock_code,
                    'model_name': col.split('_')[0] + '_' + col.split('_')[1],
                    'prediction_date': current_time.strftime('%Y-%m-%d'),
                    'prediction_time': current_time.strftime('%H:%M:%S'),
                    'target_date': (current_time + timedelta(days=1)).strftime('%Y-%m-%d'),
                    'prediction_value': latest_prediction[col],
                    'confidence': latest_prediction.get(col.replace('prediction', 'confidence'), 0.5)
                }
                
                db.insert_prediction(prediction_record)
        
        # 格式化返回结果
        result = {
            'stock_code': stock_code,
            'prediction_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'predictions': {}
        }
        
        for col in predictions.columns:
            if 'prediction' in col:
                result['predictions'][col] = float(latest_prediction[col])
        
        logger.info(f"股票 {stock_code} 预测完成")
        return jsonify({'success': True, 'data': result})
        
    except Exception as e:
        logger.error(f"执行预测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/train', methods=['POST'])
def train_models():
    """训练模型"""
    try:
        data = request.get_json()
        model_names = data.get('models', ['xgboost_next_day_direction'])
        factor_names = data.get('factors', [])
        factor_categories = data.get('factor_categories', [])

        logger.info(f"开始训练模型: {model_names}")
        if factor_names:
            logger.info(f"使用指定因子: {len(factor_names)} 个")
        if factor_categories:
            logger.info(f"使用因子分类: {factor_categories}")

        # 准备训练数据
        training_data = create_sample_data(num_days=200)

        # 计算特征（包括量化因子）
        feature_types = ['basic', 'technical', 'time_series']
        if factor_names or factor_categories:
            feature_types.append('quantitative')

        features_data = feature_engine.calculate_features(
            training_data,
            feature_types=feature_types,
            factor_names=factor_names,
            factor_categories=factor_categories
        )

        # 训练指定模型
        training_results = {}
        for model_name in model_names:
            try:
                result = model_manager.train_model(
                    model_name,
                    features_data,
                    factor_names=factor_names or feature_engine.get_enabled_factors()
                )

                training_results[model_name] = {
                    'success': True,
                    'metrics': result.get('validation_metrics', {}),
                    'factor_combination': result.get('factor_combination', {})
                }

                # 保存模型
                model_manager.save_model(model_name)

            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")
                training_results[model_name] = {
                    'success': False,
                    'error': str(e)
                }

        logger.info("模型训练完成")
        return jsonify({'success': True, 'data': training_results})

    except Exception as e:
        logger.error(f"训练模型失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/scheduler/start', methods=['POST'])
def start_scheduler():
    """启动调度器"""
    try:
        scheduler.start()
        return jsonify({'success': True, 'message': '调度器已启动'})
    except Exception as e:
        logger.error(f"启动调度器失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/scheduler/stop', methods=['POST'])
def stop_scheduler():
    """停止调度器"""
    try:
        scheduler.stop()
        return jsonify({'success': True, 'message': '调度器已停止'})
    except Exception as e:
        logger.error(f"停止调度器失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/scheduler/manual', methods=['POST'])
def manual_prediction():
    """手动执行预测"""
    try:
        scheduler.run_manual_prediction()
        return jsonify({'success': True, 'message': '手动预测已执行'})
    except Exception as e:
        logger.error(f"手动预测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


# ==================== 因子管理API ====================

@app.route('/api/factors/available')
def get_available_factors():
    """获取可用的量化因子"""
    try:
        available_factors = feature_engine.get_available_factors()
        return jsonify({'success': True, 'data': available_factors})
    except Exception as e:
        logger.error(f"获取可用因子失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/enabled')
def get_enabled_factors():
    """获取当前启用的因子"""
    try:
        enabled_factors = feature_engine.get_enabled_factors()
        return jsonify({'success': True, 'data': enabled_factors})
    except Exception as e:
        logger.error(f"获取启用因子失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/combinations')
def get_factor_combinations():
    """获取因子组合"""
    try:
        combinations = feature_engine.get_factor_combinations()
        return jsonify({'success': True, 'data': combinations})
    except Exception as e:
        logger.error(f"获取因子组合失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/set_combination', methods=['POST'])
def set_factor_combination():
    """设置因子组合"""
    try:
        data = request.get_json()
        factor_names = data.get('factors', [])
        combination_name = data.get('name', 'Custom Combination')

        feature_engine.set_factor_combination(factor_names, combination_name)

        return jsonify({'success': True, 'message': f'因子组合已设置: {len(factor_names)} 个因子'})
    except Exception as e:
        logger.error(f"设置因子组合失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/importance')
def get_factor_importance():
    """获取因子重要性"""
    try:
        # 获取查询参数
        stock_code = request.args.get('stock_code', '000001')

        # 生成测试数据
        test_data = create_sample_data(num_days=100)
        test_data['stock_code'] = stock_code

        # 计算特征
        features_data = feature_engine.calculate_features(test_data, feature_types=['quantitative'])

        # 添加目标变量
        features_data['target'] = features_data['close'].shift(-1)

        # 计算因子重要性
        importance_df = feature_engine.calculate_factor_importance(features_data.dropna(), 'target')

        if importance_df.empty:
            return jsonify({'success': True, 'data': []})

        # 转换为JSON格式
        importance_list = importance_df.to_dict('records')

        return jsonify({'success': True, 'data': importance_list})

    except Exception as e:
        logger.error(f"获取因子重要性失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/statistics')
def get_factor_statistics():
    """获取因子统计信息"""
    try:
        # 生成测试数据
        test_data = create_sample_data(num_days=100)

        # 计算特征
        features_data = feature_engine.calculate_features(test_data, feature_types=['quantitative'])

        # 获取因子统计
        stats_df = feature_engine.get_factor_statistics(features_data)

        if stats_df.empty:
            return jsonify({'success': True, 'data': []})

        # 转换为JSON格式
        stats_list = stats_df.to_dict('records')

        return jsonify({'success': True, 'data': stats_list})

    except Exception as e:
        logger.error(f"获取因子统计信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


# ==================== 模型因子组合管理API ====================

@app.route('/api/models/factor_combinations')
def get_model_factor_combinations():
    """获取所有模型的因子组合"""
    try:
        combinations = model_manager.get_all_factor_combinations()
        return jsonify({'success': True, 'data': combinations})
    except Exception as e:
        logger.error(f"获取模型因子组合失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/models/<model_name>/factor_combination')
def get_model_current_factors(model_name):
    """获取指定模型当前使用的因子组合"""
    try:
        combination = model_manager.get_current_factor_combination(model_name)
        return jsonify({'success': True, 'data': combination})
    except Exception as e:
        logger.error(f"获取模型 {model_name} 因子组合失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/models/<model_name>/factor_history')
def get_model_factor_history(model_name):
    """获取模型因子组合历史"""
    try:
        history_df = model_manager.compare_factor_combinations(model_name)

        if history_df.empty:
            return jsonify({'success': True, 'data': []})

        history_list = history_df.to_dict('records')
        return jsonify({'success': True, 'data': history_list})
    except Exception as e:
        logger.error(f"获取模型 {model_name} 因子历史失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/factors/usage_statistics')
def get_factor_usage_statistics():
    """获取因子使用统计"""
    try:
        stats_df = model_manager.get_factor_usage_statistics()

        if stats_df.empty:
            return jsonify({'success': True, 'data': []})

        stats_list = stats_df.to_dict('records')
        return jsonify({'success': True, 'data': stats_list})
    except Exception as e:
        logger.error(f"获取因子使用统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/models/<model_name>/recommend_factors')
def recommend_factors(model_name):
    """为模型推荐因子组合"""
    try:
        # 获取可用因子
        available_factors = feature_engine.get_available_factors()
        all_factors = []
        for category, factors in available_factors.items():
            all_factors.extend(factors)

        # 推荐因子
        max_factors = int(request.args.get('max_factors', 20))
        recommended = model_manager.recommend_factor_combination(model_name, all_factors, max_factors)

        return jsonify({'success': True, 'data': recommended})
    except Exception as e:
        logger.error(f"推荐因子组合失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


# ==================== 回测API ====================

@app.route('/api/backtest/run', methods=['POST'])
def run_backtest():
    """运行回测"""
    try:
        data = request.get_json()

        # 获取参数
        start_date = data.get('start_date', '2023-01-01')
        end_date = data.get('end_date', '2023-12-31')
        stock_codes = data.get('stock_codes', ['000001', '000002', '600000'])
        model_name = data.get('model_name', 'xgboost_next_day_direction')
        factor_names = data.get('factors', [])

        logger.info(f"开始运行回测: {start_date} 到 {end_date}")

        # 生成测试数据（实际应用中从数据库获取）
        price_data = {}
        for stock_code in stock_codes:
            # 生成价格数据
            dates = pd.date_range(start_date, end_date, freq='D')
            np.random.seed(hash(stock_code) % 1000)
            prices = 10 + np.cumsum(np.random.randn(len(dates)) * 0.02)

            price_data[stock_code] = pd.DataFrame({
                'trade_date': dates,
                'open': prices * (1 + np.random.normal(0, 0.01, len(dates))),
                'close': prices,
                'high': prices * (1 + np.random.uniform(0, 0.03, len(dates))),
                'low': prices * (1 - np.random.uniform(0, 0.03, len(dates))),
                'volume': np.random.randint(1000000, 10000000, len(dates))
            })

        # 生成交易信号（实际应用中使用模型预测）
        signals = []
        for date in pd.date_range(start_date, end_date, freq='5D'):  # 每5天一个信号
            for stock_code in stock_codes:
                if np.random.random() > 0.7:  # 30%概率产生信号
                    signal = {
                        'trade_date': date,
                        'stock_code': stock_code,
                        'signal': np.random.choice([1, -1]),  # 随机买入或卖出
                        'confidence': np.random.uniform(0.6, 0.9)
                    }
                    signals.append(signal)

        signals_df = pd.DataFrame(signals)

        # 运行回测
        backtest_results = backtest_engine.run_backtest(
            signals_df=signals_df,
            price_data=price_data,
            start_date=start_date,
            end_date=end_date
        )

        if not backtest_results:
            return jsonify({'success': False, 'error': '回测运行失败'})

        # 处理回测结果，转换为JSON可序列化格式
        result_data = {
            'basic_metrics': backtest_results['basic_metrics'],
            'trading_metrics': backtest_results['trading_metrics'],
            'trading_period': backtest_results['trading_period'],
            'final_portfolio_value': backtest_results['final_portfolio_value']
        }

        # 处理时间序列数据
        if 'portfolio_data' in backtest_results:
            portfolio_df = backtest_results['portfolio_data']
            result_data['portfolio_curve'] = []
            for _, row in portfolio_df.iterrows():
                result_data['portfolio_curve'].append({
                    'date': row['date'].strftime('%Y-%m-%d'),
                    'portfolio_value': float(row['portfolio_value']),
                    'cumulative_return': float(row['cumulative_return']),
                    'daily_return': float(row['daily_return']) if pd.notna(row['daily_return']) else 0
                })

        # 处理交易记录
        if 'trades_data' in backtest_results:
            trades_df = backtest_results['trades_data']
            result_data['trades'] = []
            for _, row in trades_df.iterrows():
                trade_record = {
                    'date': row['date'],
                    'stock_code': row['stock_code'],
                    'action': row['action'],
                    'shares': int(row['shares']),
                    'price': float(row['price']),
                    'value': float(row['value'])
                }

                if 'profit_loss' in row and pd.notna(row['profit_loss']):
                    trade_record['profit_loss'] = float(row['profit_loss'])
                    trade_record['profit_loss_pct'] = float(row['profit_loss_pct'])

                result_data['trades'].append(trade_record)

        logger.info("回测完成")
        return jsonify({'success': True, 'data': result_data})

    except Exception as e:
        logger.error(f"运行回测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/backtest/history')
def get_backtest_history():
    """获取回测历史记录"""
    try:
        # 从数据库获取回测历史（这里返回示例数据）
        history_data = [
            {
                'id': 1,
                'model_name': 'xgboost_next_day_direction',
                'start_date': '2023-01-01',
                'end_date': '2023-06-30',
                'total_return': 15.6,
                'annual_return': 31.2,
                'max_drawdown': -8.5,
                'sharpe_ratio': 1.85,
                'win_rate': 65.4,
                'created_at': '2023-07-01 10:30:00'
            },
            {
                'id': 2,
                'model_name': 'xgboost_10d_surge',
                'start_date': '2023-01-01',
                'end_date': '2023-06-30',
                'total_return': 12.3,
                'annual_return': 24.6,
                'max_drawdown': -6.2,
                'sharpe_ratio': 1.92,
                'win_rate': 58.7,
                'created_at': '2023-07-01 11:15:00'
            }
        ]

        return jsonify({'success': True, 'data': history_data})

    except Exception as e:
        logger.error(f"获取回测历史失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/backtest/compare', methods=['POST'])
def compare_backtests():
    """比较多个回测结果"""
    try:
        data = request.get_json()
        backtest_ids = data.get('backtest_ids', [])

        # 这里应该从数据库获取指定的回测结果进行比较
        # 返回示例比较数据
        comparison_data = {
            'metrics_comparison': [
                {
                    'metric': 'total_return',
                    'backtest_1': 15.6,
                    'backtest_2': 12.3,
                    'difference': 3.3
                },
                {
                    'metric': 'annual_return',
                    'backtest_1': 31.2,
                    'backtest_2': 24.6,
                    'difference': 6.6
                },
                {
                    'metric': 'max_drawdown',
                    'backtest_1': -8.5,
                    'backtest_2': -6.2,
                    'difference': -2.3
                },
                {
                    'metric': 'sharpe_ratio',
                    'backtest_1': 1.85,
                    'backtest_2': 1.92,
                    'difference': -0.07
                }
            ]
        }

        return jsonify({'success': True, 'data': comparison_data})

    except Exception as e:
        logger.error(f"比较回测结果失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


# ==================== 时间感知API ====================

@app.route('/api/time_aware/train', methods=['POST'])
def train_time_aware_models():
    """训练时间感知模型"""
    try:
        if not time_aware_model_manager:
            return jsonify({'success': False, 'error': '时间感知组件未初始化'})

        data = request.get_json()

        # 获取参数
        model_names = data.get('models', ['xgboost_next_day_direction'])
        stock_codes = data.get('stock_codes', ['000001', '000002'])
        factor_names = data.get('factors', [])
        train_both_times = data.get('train_both_times', True)

        # 训练配置
        train_config = {
            'train_days': data.get('train_days', 100),
            'backtest_days': data.get('backtest_days', 30),
            'prediction_days': data.get('prediction_days', 20)
        }

        # 如果提供了日期范围
        if data.get('train_start') and data.get('train_end'):
            train_config['train_days'] = (data.get('train_start'), data.get('train_end'))

        logger.info(f"开始训练时间感知模型: {model_names}")

        # 训练模型
        results = time_aware_model_manager.train_time_aware_models(
            model_names=model_names,
            stock_codes=stock_codes,
            train_config=train_config,
            factor_names=factor_names,
            train_both_times=train_both_times
        )

        return jsonify({'success': True, 'data': results})

    except Exception as e:
        logger.error(f"训练时间感知模型失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/predict', methods=['POST'])
def predict_time_aware():
    """时间感知预测"""
    try:
        if not time_aware_model_manager:
            return jsonify({'success': False, 'error': '时间感知组件未初始化'})

        data = request.get_json()

        # 获取参数
        stock_codes = data.get('stock_codes', ['000001', '000002'])
        prediction_time = data.get('prediction_time', 'morning')  # 'morning' 或 'afternoon'
        model_names = data.get('models', ['xgboost_next_day_direction'])
        prediction_days = data.get('prediction_days', 20)

        logger.info(f"开始 {prediction_time} 时间感知预测")

        # 执行预测
        predictions = time_aware_model_manager.predict_with_time_awareness(
            stock_codes=stock_codes,
            prediction_time=prediction_time,
            model_names=model_names,
            prediction_days=prediction_days
        )

        return jsonify({'success': True, 'data': predictions})

    except Exception as e:
        logger.error(f"时间感知预测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/data_split', methods=['POST'])
def get_time_aware_data_split():
    """获取时间感知数据分割信息"""
    try:
        if not time_aware_data_manager:
            return jsonify({'success': False, 'error': '时间感知组件未初始化'})

        data = request.get_json()

        # 训练配置
        train_config = {
            'train_days': data.get('train_days', 100),
            'backtest_days': data.get('backtest_days', 30),
            'prediction_days': data.get('prediction_days', 20),
            'end_date': data.get('end_date')
        }

        # 如果提供了日期范围
        if data.get('train_start') and data.get('train_end'):
            train_config['train_days'] = (data.get('train_start'), data.get('train_end'))

        # 获取数据分割信息
        date_split = time_aware_data_manager.split_data_by_trading_days(**train_config)

        return jsonify({'success': True, 'data': date_split})

    except Exception as e:
        logger.error(f"获取数据分割信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/trading_days')
def get_trading_days():
    """获取交易日信息"""
    try:
        if not time_aware_data_manager:
            return jsonify({'success': False, 'error': '时间感知组件未初始化'})

        # 获取参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        num_days = request.args.get('num_days', type=int)

        if start_date and end_date:
            # 按日期范围获取
            trading_days = time_aware_data_manager.get_trading_days_between(start_date, end_date)
        elif num_days:
            # 按天数获取
            trading_days = time_aware_data_manager.get_recent_trading_days(num_days, end_date)
        else:
            # 默认获取最近20个交易日
            trading_days = time_aware_data_manager.get_recent_trading_days(20)

        # 转换为字符串列表
        trading_days_list = [day.strftime('%Y-%m-%d') for day in trading_days]

        return jsonify({
            'success': True,
            'data': {
                'trading_days': trading_days_list,
                'count': len(trading_days_list)
            }
        })

    except Exception as e:
        logger.error(f"获取交易日信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/scheduler/status')
def get_time_aware_scheduler_status():
    """获取时间感知调度器状态"""
    try:
        if not time_aware_scheduler:
            return jsonify({'success': False, 'error': '时间感知调度器未初始化'})

        status = time_aware_scheduler.get_scheduler_status()
        return jsonify({'success': True, 'data': status})

    except Exception as e:
        logger.error(f"获取时间感知调度器状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/scheduler/start', methods=['POST'])
def start_time_aware_scheduler():
    """启动时间感知调度器"""
    try:
        if not time_aware_scheduler:
            return jsonify({'success': False, 'error': '时间感知调度器未初始化'})

        time_aware_scheduler.start_scheduler()
        return jsonify({'success': True, 'message': '时间感知调度器已启动'})

    except Exception as e:
        logger.error(f"启动时间感知调度器失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/scheduler/stop', methods=['POST'])
def stop_time_aware_scheduler():
    """停止时间感知调度器"""
    try:
        if not time_aware_scheduler:
            return jsonify({'success': False, 'error': '时间感知调度器未初始化'})

        time_aware_scheduler.stop_scheduler()
        return jsonify({'success': True, 'message': '时间感知调度器已停止'})

    except Exception as e:
        logger.error(f"停止时间感知调度器失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/scheduler/manual', methods=['POST'])
def manual_time_aware_prediction():
    """手动执行时间感知预测"""
    try:
        if not time_aware_scheduler:
            return jsonify({'success': False, 'error': '时间感知调度器未初始化'})

        data = request.get_json()
        prediction_type = data.get('prediction_type', 'morning')  # 'morning' 或 'afternoon'

        result = time_aware_scheduler.manual_prediction(prediction_type)
        return jsonify(result)

    except Exception as e:
        logger.error(f"手动时间感知预测失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/time_aware/model_performance')
def get_time_aware_model_performance():
    """获取时间感知模型性能对比"""
    try:
        if not time_aware_model_manager:
            return jsonify({'success': False, 'error': '时间感知组件未初始化'})

        performance = time_aware_model_manager.get_model_performance_comparison()
        return jsonify({'success': True, 'data': performance})

    except Exception as e:
        logger.error(f"获取时间感知模型性能失败: {e}")
        return jsonify({'success': False, 'error': str(e)})


if __name__ == '__main__':
    # 切换到项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_root)

    logger.info("启动量化交易系统Web界面...")
    app.run(host='0.0.0.0', port=5001, debug=True)
