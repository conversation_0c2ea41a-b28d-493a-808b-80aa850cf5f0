# 模型配置文件
models:
  # XGBoost模型1：10日拉升概率预测
  xgboost_10d_surge:
    name: "10日拉升概率预测"
    type: "classification"
    algorithm: "xgboost"
    target_definition:
      # 明显拉升定义：未来10个交易日同时满足
      description: "未来10日明显拉升"
      conditions:
        - name: "avg_daily_return"
          threshold: 0.01  # 日均涨幅 > 1%
          operator: ">"
        - name: "surge_days_count"
          threshold: 4     # 至少4个交易日单日涨幅 > 2%
          operator: ">="
          surge_threshold: 0.02
      lookforward_days: 10
    
    # 模型参数
    parameters:
      n_estimators: 100
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42
      eval_metric: "auc"
    
    # 训练配置
    training:
      test_size: 0.2
      validation_size: 0.1
      cv_folds: 5
      
    # 特征配置
    features:
      use_basic: true
      use_technical: true
      use_time_series: false
      exclude_features: []

  # XGBoost模型2：明日开盘涨跌预测
  xgboost_next_day_direction:
    name: "明日开盘涨跌预测"
    type: "binary_classification"
    algorithm: "xgboost"
    target_definition:
      description: "明日开盘价相对今日收盘价涨跌"
      # 上涨：明日开盘价 > 今日收盘价 → 标签 1
      # 下跌：明日开盘价 ≤ 今日收盘价 → 标签 -1
      positive_label: 1  # 上涨
      negative_label: -1 # 下跌
      
    # 模型参数
    parameters:
      n_estimators: 150
      max_depth: 5
      learning_rate: 0.05
      subsample: 0.9
      colsample_bytree: 0.9
      random_state: 42
      eval_metric: "auc"
      
    # 训练配置
    training:
      test_size: 0.2
      validation_size: 0.1
      cv_folds: 5
      
    # 特征配置
    features:
      use_basic: true
      use_technical: true
      use_time_series: false
      exclude_features: []

  # 时序模型：明日价格预测
  time_series_price_prediction:
    name: "明日价格预测"
    type: "multi_target_regression"
    algorithm: "lstm"  # lstm, transformer, arima, prophet
    target_definition:
      description: "预测明日开盘价、收盘价、最高价"
      targets:
        - "next_open"
        - "next_close" 
        - "next_high"
        
    # 模型参数
    parameters:
      # LSTM参数
      lstm:
        sequence_length: 20  # 输入序列长度
        hidden_size: 64
        num_layers: 2
        dropout: 0.2
        learning_rate: 0.001
        batch_size: 32
        epochs: 100
        early_stopping_patience: 10
        
      # Transformer参数
      transformer:
        sequence_length: 20
        d_model: 64
        nhead: 8
        num_layers: 3
        dropout: 0.1
        learning_rate: 0.0001
        batch_size: 32
        epochs: 100
        early_stopping_patience: 10
        
      # ARIMA参数（备选）
      arima:
        order: [1, 1, 1]  # (p, d, q)
        seasonal_order: [1, 1, 1, 5]  # (P, D, Q, s)
        
      # Prophet参数（备选）
      prophet:
        yearly_seasonality: false
        weekly_seasonality: true
        daily_seasonality: false
        changepoint_prior_scale: 0.05
        
    # 训练配置
    training:
      test_size: 0.2
      validation_size: 0.1
      
    # 特征配置
    features:
      use_basic: true
      use_technical: true
      use_time_series: true
      exclude_features: []

# 模型评估配置
evaluation:
  metrics:
    classification:
      - "accuracy"
      - "precision"
      - "recall"
      - "f1_score"
      - "auc"
      - "confusion_matrix"
    regression:
      - "mse"
      - "rmse"
      - "mae"
      - "r2_score"
      - "mape"
      
  # 回测配置
  backtest:
    start_date: "2022-01-01"
    end_date: "2023-12-31"
    rebalance_frequency: "daily"  # daily, weekly, monthly
    
# 预测配置
prediction:
  # 定时预测时间
  schedule:
    - time: "09:35"
      description: "开盘后预测"
    - time: "14:50"
      description: "收盘前预测"
      
  # 预测股票池
  stock_pool:
    # 可以配置特定股票列表，或者使用筛选条件
    type: "list"  # list, filter
    stocks:
      - "000001"  # 平安银行
      - "000002"  # 万科A
      - "600000"  # 浦发银行
      - "600036"  # 招商银行
      # 可以继续添加更多股票
    
    # 筛选条件（当type为filter时使用）
    filter_conditions:
      market_cap_min: 1000000000  # 最小市值10亿
      volume_min: 1000000         # 最小成交量100万
      exclude_st: true            # 排除ST股票
      
  # 预测结果存储
  storage:
    save_to_db: true
    save_to_file: true
    file_format: "csv"  # csv, json, excel
