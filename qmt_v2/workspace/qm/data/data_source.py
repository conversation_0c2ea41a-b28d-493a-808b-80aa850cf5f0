"""
多数据源模块 - 支持多个免费A股数据源
支持的数据源：
1. adata - 主要数据源 (已集成)
2. akshare - 备用数据源 (最全面)
3. baostock - 历史数据源 (免费稳定)
4. yfinance - 国际化数据源 (支持港股美股)
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import yaml
from typing import List, Dict, Optional, Union
from loguru import logger
import os
import sys
from abc import ABC, abstractmethod

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 数据源导入 - 使用try-except处理可选依赖
AVAILABLE_SOURCES = {}

try:
    import adata
    AVAILABLE_SOURCES['adata'] = True
    logger.info("adata 数据源可用")
except ImportError:
    AVAILABLE_SOURCES['adata'] = False
    logger.warning("adata 未安装，请运行: pip install adata")

try:
    import akshare as ak
    AVAILABLE_SOURCES['akshare'] = True
    logger.info("akshare 数据源可用")
except ImportError:
    AVAILABLE_SOURCES['akshare'] = False
    logger.warning("akshare 未安装，请运行: pip install akshare")

try:
    import baostock as bs
    AVAILABLE_SOURCES['baostock'] = True
    logger.info("baostock 数据源可用")
except ImportError:
    AVAILABLE_SOURCES['baostock'] = False
    logger.warning("baostock 未安装，请运行: pip install baostock")

try:
    import yfinance as yf
    AVAILABLE_SOURCES['yfinance'] = True
    logger.info("yfinance 数据源可用")
except ImportError:
    AVAILABLE_SOURCES['yfinance'] = False
    logger.warning("yfinance 未安装，请运行: pip install yfinance")


class BaseDataSource(ABC):
    """数据源基类"""

    def __init__(self, config: Dict):
        self.config = config
        self.name = self.__class__.__name__

    @abstractmethod
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        pass

    @abstractmethod
    def get_stock_market_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票行情数据"""
        pass

    @abstractmethod
    def get_current_market_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据"""
        pass

    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return True


class AdataSource(BaseDataSource):
    """adata数据源实现"""

    def __init__(self, config: Dict):
        super().__init__(config)
        if not AVAILABLE_SOURCES.get('adata', False):
            raise ImportError("adata 未安装")
        self._setup_adata()

    def _setup_adata(self):
        """设置adata配置"""
        if self.config.get('use_proxy', False):
            proxy_config = self.config.get('proxy', {})
            if proxy_config.get('ip'):
                adata.proxy(is_proxy=True, ip=proxy_config['ip'])
                logger.info("已设置adata代理")

    def is_available(self) -> bool:
        return AVAILABLE_SOURCES.get('adata', False)
    
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        try:
            logger.info("adata: 获取股票列表...")
            df = adata.stock.info.all_code()
            logger.info(f"adata: 获取到 {len(df)} 只股票")
            return df
        except Exception as e:
            logger.error(f"adata: 获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_stock_market_data(self,
                            stock_code: str,
                            start_date: str = None,
                            end_date: str = None,
                            k_type: int = 1,
                            adjust_type: int = 1) -> pd.DataFrame:
        """
        获取股票行情数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            k_type: k线类型 1.日 2.周 3.月
            adjust_type: 复权类型 0.不复权 1.前复权 2.后复权
        """
        try:
            # 设置默认日期 - 最近2年数据
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365*2)).strftime('%Y-%m-%d')

            logger.info(f"adata: 获取股票 {stock_code} 行情数据: {start_date} 到 {end_date}")

            df = adata.stock.market.get_market(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                k_type=k_type,
                adjust_type=adjust_type
            )

            if not df.empty:
                # 数据清洗和格式化
                df = self._clean_market_data(df)
                logger.info(f"adata: 获取到 {len(df)} 条行情数据")
            else:
                logger.warning(f"adata: 股票 {stock_code} 没有获取到行情数据")

            return df

        except Exception as e:
            logger.error(f"adata: 获取股票 {stock_code} 行情数据失败: {e}")
            return pd.DataFrame()

    def get_stock_shares(self, stock_code: str, is_history: bool = False) -> pd.DataFrame:
        """获取股票股本信息"""
        try:
            logger.info(f"adata: 获取股票 {stock_code} 股本信息")
            df = adata.stock.info.get_stock_shares(stock_code=stock_code, is_history=is_history)
            if not df.empty:
                logger.info(f"adata: 获取到 {len(df)} 条股本数据")
            return df
        except Exception as e:
            logger.error(f"adata: 获取股票 {stock_code} 股本信息失败: {e}")
            return pd.DataFrame()

    def get_stock_industry(self, stock_code: str) -> pd.DataFrame:
        """获取股票行业信息"""
        try:
            logger.info(f"adata: 获取股票 {stock_code} 行业信息")
            df = adata.stock.info.get_industry_sw(stock_code=stock_code)
            if not df.empty:
                logger.info(f"adata: 获取到股票行业信息")
            return df
        except Exception as e:
            logger.error(f"adata: 获取股票 {stock_code} 行业信息失败: {e}")
            return pd.DataFrame()

    def get_trade_calendar(self, year: int) -> pd.DataFrame:
        """获取交易日历"""
        try:
            logger.info(f"adata: 获取 {year} 年交易日历")
            df = adata.stock.info.trade_calendar(year=year)
            return df
        except Exception as e:
            logger.error(f"adata: 获取 {year} 年交易日历失败: {e}")
            return pd.DataFrame()
    
    def get_current_market_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据"""
        try:
            logger.info(f"获取 {len(stock_codes)} 只股票的实时行情")
            
            # adata限制最多500只股票
            if len(stock_codes) > 500:
                logger.warning("股票数量超过500只，将分批获取")
                all_data = []
                for i in range(0, len(stock_codes), 500):
                    batch = stock_codes[i:i+500]
                    batch_data = adata.stock.market.list_market_current(code_list=batch)
                    if not batch_data.empty:
                        all_data.append(batch_data)
                    time.sleep(self.adata_config.get('request_interval', 0.1))
                
                if all_data:
                    df = pd.concat(all_data, ignore_index=True)
                else:
                    df = pd.DataFrame()
            else:
                df = adata.stock.market.list_market_current(code_list=stock_codes)
            
            if not df.empty:
                logger.info(f"获取到 {len(df)} 只股票的实时行情")
            
            return df
            
        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            return pd.DataFrame()
    
    def get_stock_info(self, stock_code: str) -> Dict:
        """获取股票基本信息"""
        try:
            # 获取行业信息
            industry_df = adata.stock.info.get_industry_sw(stock_code=stock_code)
            industry = ""
            if not industry_df.empty:
                industry = industry_df.iloc[0]['industry_name']
            
            return {
                'stock_code': stock_code,
                'industry': industry
            }
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基本信息失败: {e}")
            return {}
    
    def get_trade_calendar(self, year: int) -> pd.DataFrame:
        """获取交易日历"""
        try:
            logger.info(f"获取 {year} 年交易日历")
            df = adata.stock.info.trade_calendar(year=year)
            return df
        except Exception as e:
            logger.error(f"获取 {year} 年交易日历失败: {e}")
            return pd.DataFrame()
    
    def _clean_market_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗行情数据"""
        if df.empty:
            return df
            
        # 确保数据类型正确
        numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount', 
                          'change', 'change_pct', 'turnover_ratio', 'pre_close']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理日期
        if 'trade_date' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 删除缺失值过多的行
        df = df.dropna(subset=['open', 'close', 'high', 'low'])
        
        # 按日期排序
        if 'trade_date' in df.columns:
            df = df.sort_values('trade_date').reset_index(drop=True)
        
        return df
    
    def is_trading_day(self, date: datetime = None) -> bool:
        """判断是否为交易日"""
        if date is None:
            date = datetime.now()
            
        try:
            # 获取当年交易日历
            calendar_df = self.get_trade_calendar(date.year)
            if calendar_df.empty:
                # 如果获取失败，使用简单规则：周一到周五
                return date.weekday() < 5
            
            # 检查是否为交易日
            date_str = date.strftime('%Y-%m-%d')
            trading_days = calendar_df[calendar_df['trade_status'] == 1]['trade_date'].dt.strftime('%Y-%m-%d')
            return date_str in trading_days.values
            
        except Exception as e:
            logger.error(f"判断交易日失败: {e}")
            # 默认规则：周一到周五
            return date.weekday() < 5
    
    def get_latest_trading_day(self) -> str:
        """获取最近的交易日"""
        try:
            current_date = datetime.now()
            
            # 向前查找最近的交易日
            for i in range(10):  # 最多查找10天
                check_date = current_date - timedelta(days=i)
                if self.is_trading_day(check_date):
                    return check_date.strftime('%Y-%m-%d')
            
            # 如果都不是交易日，返回当前日期
            return current_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"获取最近交易日失败: {e}")
            return datetime.now().strftime('%Y-%m-%d')


class DataSource:
    """统一数据源管理器"""

    def __init__(self, config_path: str = None):
        """初始化数据源管理器"""
        self.config = self._load_config(config_path)
        self.primary_source = None
        self.backup_sources = []
        self._setup_sources()

    def _load_config(self, config_path: str = None) -> Dict:
        """加载配置"""
        if config_path is None:
            # 使用默认配置
            return {
                'primary_source': 'adata',
                'backup_sources': ['akshare', 'baostock'],
                'adata': {
                    'use_proxy': False,
                    'request_interval': 0.1
                },
                'akshare': {
                    'request_interval': 0.2
                },
                'baostock': {
                    'request_interval': 0.1
                }
            }

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def _setup_sources(self):
        """设置数据源"""
        primary_name = self.config.get('primary_source', 'adata')

        # 设置主数据源
        if primary_name == 'adata' and AVAILABLE_SOURCES.get('adata', False):
            try:
                self.primary_source = AdataSource(self.config.get('adata', {}))
                logger.info("主数据源设置为: adata")
            except Exception as e:
                logger.error(f"设置adata数据源失败: {e}")

        # 设置备用数据源
        backup_names = self.config.get('backup_sources', [])
        for name in backup_names:
            if AVAILABLE_SOURCES.get(name, False):
                try:
                    if name == 'adata':
                        source = AdataSource(self.config.get('adata', {}))
                    # 可以在这里添加其他数据源
                    else:
                        continue

                    self.backup_sources.append(source)
                    logger.info(f"备用数据源添加: {name}")
                except Exception as e:
                    logger.error(f"设置备用数据源 {name} 失败: {e}")

    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        # 尝试主数据源
        if self.primary_source:
            try:
                return self.primary_source.get_stock_list()
            except Exception as e:
                logger.error(f"主数据源获取股票列表失败: {e}")

        # 尝试备用数据源
        for source in self.backup_sources:
            try:
                return source.get_stock_list()
            except Exception as e:
                logger.error(f"备用数据源获取股票列表失败: {e}")

        logger.error("所有数据源都无法获取股票列表")
        return pd.DataFrame()

    def get_stock_market_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """获取股票行情数据"""
        # 尝试主数据源
        if self.primary_source:
            try:
                return self.primary_source.get_stock_market_data(stock_code, start_date, end_date)
            except Exception as e:
                logger.error(f"主数据源获取行情数据失败: {e}")

        # 尝试备用数据源
        for source in self.backup_sources:
            try:
                return source.get_stock_market_data(stock_code, start_date, end_date)
            except Exception as e:
                logger.error(f"备用数据源获取行情数据失败: {e}")

        logger.error(f"所有数据源都无法获取股票 {stock_code} 的行情数据")
        return pd.DataFrame()

    def get_current_market_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据"""
        # 尝试主数据源
        if self.primary_source:
            try:
                return self.primary_source.get_current_market_data(stock_codes)
            except Exception as e:
                logger.error(f"主数据源获取实时行情失败: {e}")

        # 尝试备用数据源
        for source in self.backup_sources:
            try:
                return source.get_current_market_data(stock_codes)
            except Exception as e:
                logger.error(f"备用数据源获取实时行情失败: {e}")

        logger.error("所有数据源都无法获取实时行情")
        return pd.DataFrame()

    def get_available_sources(self) -> Dict[str, bool]:
        """获取可用数据源列表"""
        return AVAILABLE_SOURCES.copy()


# 测试函数
def test_data_source():
    """测试数据源功能"""
    logger.info("开始测试数据源...")
    
    ds = DataSource()
    
    # 测试获取股票列表（只获取前10只）
    stock_list = ds.get_stock_list()
    if not stock_list.empty:
        logger.info(f"股票列表测试成功，共 {len(stock_list)} 只股票")
        test_stocks = stock_list.head(3)['stock_code'].tolist()
    else:
        logger.error("股票列表测试失败")
        test_stocks = ['000001', '000002']  # 使用默认测试股票
    
    # 测试获取行情数据（只获取最近30天的数据）
    test_stock = test_stocks[0]
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    market_data = ds.get_stock_market_data(
        stock_code=test_stock,
        start_date=start_date
    )
    
    if not market_data.empty:
        logger.info(f"行情数据测试成功，股票 {test_stock} 获取到 {len(market_data)} 条数据")
        print(market_data.head())
    else:
        logger.error(f"行情数据测试失败，股票 {test_stock}")
    
    # 测试实时行情
    current_data = ds.get_current_market_data(test_stocks[:2])
    if not current_data.empty:
        logger.info(f"实时行情测试成功，获取到 {len(current_data)} 只股票数据")
        print(current_data)
    else:
        logger.error("实时行情测试失败")
    
    logger.info("数据源测试完成")


if __name__ == "__main__":
    test_data_source()
