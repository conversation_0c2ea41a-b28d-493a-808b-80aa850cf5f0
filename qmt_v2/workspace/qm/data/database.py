"""
数据库管理模块 - SQLite数据库操作
"""
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yaml
import os
import json
from typing import List, Dict, Optional, Any, Union
from loguru import logger
import threading
from contextlib import contextmanager


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化数据库管理器"""
        self.config = self._load_config(config_path)
        self.db_config = self.config.get('database', {}).get('sqlite', {})
        self.tables_config = self.config.get('database', {}).get('tables', {})
        
        # 数据库文件路径
        self.db_path = self.db_config.get('db_path', 'data/quantitative_trading.db')
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 初始化数据库
        self._init_database()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载数据库配置失败: {e}")
            return {}
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.db_config.get('timeout', 30),
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建所有表
                for table_name, table_config in self.tables_config.items():
                    self._create_table(cursor, table_config)
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_table(self, cursor: sqlite3.Cursor, table_config: Dict):
        """创建数据表"""
        table_name = table_config['name']
        columns = table_config['columns']
        
        # 构建CREATE TABLE语句
        column_definitions = []
        for col_name, col_type in columns.items():
            column_definitions.append(f"{col_name} {col_type}")
        
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {', '.join(column_definitions)}
        )
        """
        
        cursor.execute(create_sql)
        
        # 创建索引
        indexes = table_config.get('indexes', [])
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except sqlite3.Error as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建索引失败: {e}")
        
        logger.debug(f"表 {table_name} 创建完成")
    
    def insert_stock_info(self, stock_data: Union[Dict, pd.DataFrame]):
        """插入股票基本信息"""
        try:
            with self.get_connection() as conn:
                if isinstance(stock_data, dict):
                    # 单条记录
                    sql = """
                    INSERT OR REPLACE INTO stock_info 
                    (stock_code, short_name, exchange, list_date, industry, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """
                    conn.execute(sql, (
                        stock_data.get('stock_code'),
                        stock_data.get('short_name'),
                        stock_data.get('exchange'),
                        stock_data.get('list_date'),
                        stock_data.get('industry'),
                        datetime.now()
                    ))
                elif isinstance(stock_data, pd.DataFrame):
                    # 批量插入
                    stock_data['updated_at'] = datetime.now()
                    stock_data.to_sql('stock_info', conn, if_exists='replace', index=False)
                
                conn.commit()
                logger.info("股票信息插入成功")
                
        except Exception as e:
            logger.error(f"插入股票信息失败: {e}")
            raise
    
    def insert_market_data(self, market_data: pd.DataFrame):
        """插入行情数据"""
        try:
            if market_data.empty:
                logger.warning("行情数据为空，跳过插入")
                return
            
            with self.get_connection() as conn:
                # 添加创建时间
                market_data = market_data.copy()
                market_data['created_at'] = datetime.now()
                
                # 批量插入
                market_data.to_sql('stock_market', conn, if_exists='append', index=False)
                conn.commit()
                
                logger.info(f"插入 {len(market_data)} 条行情数据")
                
        except Exception as e:
            logger.error(f"插入行情数据失败: {e}")
            raise
    
    def insert_features(self, features_data: pd.DataFrame):
        """插入特征数据"""
        try:
            if features_data.empty:
                logger.warning("特征数据为空，跳过插入")
                return
            
            with self.get_connection() as conn:
                # 添加创建时间
                features_data = features_data.copy()
                features_data['created_at'] = datetime.now()
                
                # 批量插入
                features_data.to_sql('features', conn, if_exists='append', index=False)
                conn.commit()
                
                logger.info(f"插入 {len(features_data)} 条特征数据")
                
        except Exception as e:
            logger.error(f"插入特征数据失败: {e}")
            raise
    
    def insert_prediction(self, prediction_data: Dict):
        """插入预测结果"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT INTO predictions 
                (stock_code, model_name, prediction_date, prediction_time, target_date, 
                 prediction_value, confidence, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    prediction_data.get('stock_code'),
                    prediction_data.get('model_name'),
                    prediction_data.get('prediction_date'),
                    prediction_data.get('prediction_time'),
                    prediction_data.get('target_date'),
                    prediction_data.get('prediction_value'),
                    prediction_data.get('confidence'),
                    datetime.now()
                ))
                conn.commit()
                
                logger.debug(f"插入预测结果: {prediction_data.get('stock_code')} - {prediction_data.get('model_name')}")
                
        except Exception as e:
            logger.error(f"插入预测结果失败: {e}")
            raise
    
    def insert_trade(self, trade_data: Dict):
        """插入交易记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT INTO trades 
                (stock_code, trade_type, trade_date, trade_time, quantity, price, 
                 amount, commission, model_signal, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    trade_data.get('stock_code'),
                    trade_data.get('trade_type'),
                    trade_data.get('trade_date'),
                    trade_data.get('trade_time'),
                    trade_data.get('quantity'),
                    trade_data.get('price'),
                    trade_data.get('amount'),
                    trade_data.get('commission'),
                    trade_data.get('model_signal'),
                    trade_data.get('notes'),
                    datetime.now()
                ))
                conn.commit()
                
                logger.info(f"插入交易记录: {trade_data.get('stock_code')} {trade_data.get('trade_type')}")
                
        except Exception as e:
            logger.error(f"插入交易记录失败: {e}")
            raise
    
    def get_market_data(self, 
                       stock_code: str, 
                       start_date: str = None, 
                       end_date: str = None,
                       limit: int = None) -> pd.DataFrame:
        """获取行情数据"""
        try:
            with self.get_connection() as conn:
                sql = "SELECT * FROM stock_market WHERE stock_code = ?"
                params = [stock_code]
                
                if start_date:
                    sql += " AND trade_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    sql += " AND trade_date <= ?"
                    params.append(end_date)
                
                sql += " ORDER BY trade_date DESC"
                
                if limit:
                    sql += f" LIMIT {limit}"
                
                df = pd.read_sql_query(sql, conn, params=params)
                
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                
                return df
                
        except Exception as e:
            logger.error(f"获取行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_features(self, 
                    stock_code: str, 
                    start_date: str = None, 
                    end_date: str = None,
                    feature_names: List[str] = None) -> pd.DataFrame:
        """获取特征数据"""
        try:
            with self.get_connection() as conn:
                sql = "SELECT * FROM features WHERE stock_code = ?"
                params = [stock_code]
                
                if start_date:
                    sql += " AND trade_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    sql += " AND trade_date <= ?"
                    params.append(end_date)
                
                if feature_names:
                    placeholders = ','.join(['?' for _ in feature_names])
                    sql += f" AND feature_name IN ({placeholders})"
                    params.extend(feature_names)
                
                sql += " ORDER BY trade_date, feature_name"
                
                df = pd.read_sql_query(sql, conn, params=params)
                
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                
                return df
                
        except Exception as e:
            logger.error(f"获取特征数据失败: {e}")
            return pd.DataFrame()
    
    def get_predictions(self, 
                       stock_code: str = None,
                       model_name: str = None,
                       start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """获取预测结果"""
        try:
            with self.get_connection() as conn:
                sql = "SELECT * FROM predictions WHERE 1=1"
                params = []
                
                if stock_code:
                    sql += " AND stock_code = ?"
                    params.append(stock_code)
                
                if model_name:
                    sql += " AND model_name = ?"
                    params.append(model_name)
                
                if start_date:
                    sql += " AND prediction_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    sql += " AND prediction_date <= ?"
                    params.append(end_date)
                
                sql += " ORDER BY prediction_date DESC, prediction_time DESC"
                
                df = pd.read_sql_query(sql, conn, params=params)
                
                if not df.empty:
                    df['prediction_date'] = pd.to_datetime(df['prediction_date'])
                    if 'target_date' in df.columns:
                        df['target_date'] = pd.to_datetime(df['target_date'])
                
                return df
                
        except Exception as e:
            logger.error(f"获取预测结果失败: {e}")
            return pd.DataFrame()
    
    def get_trades(self, 
                  stock_code: str = None,
                  start_date: str = None,
                  end_date: str = None) -> pd.DataFrame:
        """获取交易记录"""
        try:
            with self.get_connection() as conn:
                sql = "SELECT * FROM trades WHERE 1=1"
                params = []
                
                if stock_code:
                    sql += " AND stock_code = ?"
                    params.append(stock_code)
                
                if start_date:
                    sql += " AND trade_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    sql += " AND trade_date <= ?"
                    params.append(end_date)
                
                sql += " ORDER BY trade_date DESC, trade_time DESC"
                
                df = pd.read_sql_query(sql, conn, params=params)
                
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                
                return df
                
        except Exception as e:
            logger.error(f"获取交易记录失败: {e}")
            return pd.DataFrame()
    
    def cleanup_old_data(self):
        """清理过期数据"""
        try:
            retention_config = self.config.get('data_management', {}).get('retention', {})
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 清理行情数据
                market_days = retention_config.get('market_data_days', 365)
                if market_days > 0:
                    cutoff_date = (datetime.now() - timedelta(days=market_days)).strftime('%Y-%m-%d')
                    cursor.execute("DELETE FROM stock_market WHERE trade_date < ?", (cutoff_date,))
                    logger.info(f"清理了 {cursor.rowcount} 条过期行情数据")
                
                # 清理特征数据
                feature_days = retention_config.get('feature_data_days', 180)
                if feature_days > 0:
                    cutoff_date = (datetime.now() - timedelta(days=feature_days)).strftime('%Y-%m-%d')
                    cursor.execute("DELETE FROM features WHERE trade_date < ?", (cutoff_date,))
                    logger.info(f"清理了 {cursor.rowcount} 条过期特征数据")
                
                # 清理预测数据
                prediction_days = retention_config.get('prediction_data_days', 90)
                if prediction_days > 0:
                    cutoff_date = (datetime.now() - timedelta(days=prediction_days)).strftime('%Y-%m-%d')
                    cursor.execute("DELETE FROM predictions WHERE prediction_date < ?", (cutoff_date,))
                    logger.info(f"清理了 {cursor.rowcount} 条过期预测数据")
                
                conn.commit()
                logger.info("数据清理完成")
                
        except Exception as e:
            logger.error(f"数据清理失败: {e}")


# 测试函数
def test_database():
    """测试数据库功能"""
    logger.info("开始测试数据库...")
    
    db = DatabaseManager()
    
    # 测试插入股票信息
    test_stock_info = {
        'stock_code': '000001',
        'short_name': '平安银行',
        'exchange': 'SZ',
        'list_date': '1991-04-03',
        'industry': '银行'
    }
    
    db.insert_stock_info(test_stock_info)
    logger.info("股票信息插入测试完成")
    
    # 测试插入行情数据
    test_market_data = pd.DataFrame({
        'stock_code': ['000001', '000001'],
        'trade_date': ['2023-12-01', '2023-12-02'],
        'open': [10.0, 10.1],
        'close': [10.1, 10.2],
        'high': [10.2, 10.3],
        'low': [9.9, 10.0],
        'volume': [1000000, 1100000],
        'amount': [10100000, 11220000],
        'change': [0.1, 0.1],
        'change_pct': [1.0, 0.99],
        'turnover_ratio': [0.5, 0.55],
        'pre_close': [10.0, 10.1]
    })
    
    db.insert_market_data(test_market_data)
    logger.info("行情数据插入测试完成")
    
    # 测试查询数据
    market_data = db.get_market_data('000001', limit=10)
    logger.info(f"查询到 {len(market_data)} 条行情数据")
    
    logger.info("数据库测试完成")


if __name__ == "__main__":
    test_database()
