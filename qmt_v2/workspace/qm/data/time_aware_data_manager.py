"""
时间感知数据管理器 (Time-Aware Data Manager)
==========================================

本模块实现了时间感知的数据管理功能，支持按交易日进行数据分割和管理。

🕐 主要功能:
- 按交易日数量或日期范围进行数据分割
- 支持训练、回测、预测数据的灵活配置
- 时间感知的模型训练（早盘vs尾盘）
- 自动跳过非交易日
- 支持多种时间格式输入

📊 数据分割策略:
- 训练数据：指定交易日数量或日期范围
- 回测数据：训练数据之后的指定交易日数量
- 预测数据：最近的指定交易日数量

⏰ 时间感知特性:
- 早盘预测模型（9:35）：基于前一交易日收盘数据
- 尾盘预测模型（14:50）：基于当日盘中数据
- 自动识别交易时间节点

🔧 核心类和方法:
- TimeAwareDataManager: 主要的数据管理类
  - split_data_by_trading_days(): 按交易日分割数据
  - get_time_aware_training_data(): 获取时间感知训练数据
  - get_trading_days_between(): 获取指定日期范围的交易日
  - get_recent_trading_days(): 获取最近的N个交易日

📝 使用示例:
```python
# 创建数据管理器
manager = TimeAwareDataManager()

# 按交易日数量分割
config = {'train_days': 252, 'backtest_days': 63, 'prediction_days': 20}
result = manager.split_data_by_trading_days(**config)

# 按日期范围分割
config = {'train_days': ('2023-01-02', '2024-03-08'), 'backtest_days': 60}
result = manager.split_data_by_trading_days(**config)

# 获取时间感知训练数据
data = manager.get_time_aware_training_data(
    stock_codes=['000001', '000002'],
    train_config=config,
    prediction_time='morning'  # 或 'afternoon'
)
```

"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from loguru import logger
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.stock_data_manager import StockDataManager


class TimeAwareDataManager:
    """
    时间感知数据管理器
    
    支持按交易日进行数据分割，时间感知的模型训练和预测
    """
    
    def __init__(self, stock_data_manager: StockDataManager = None):
        """
        初始化时间感知数据管理器
        
        Args:
            stock_data_manager: 股票数据管理器实例
        """
        self.stock_data_manager = stock_data_manager or StockDataManager()
        self.trading_calendar = None
        self._load_trading_calendar()
    
    def _load_trading_calendar(self):
        """加载交易日历"""
        try:
            # 生成2020-2025年的交易日历（排除周末和主要节假日）
            start_date = datetime(2020, 1, 1)
            end_date = datetime(2025, 12, 31)
            
            # 生成所有日期
            all_dates = pd.date_range(start_date, end_date, freq='D')
            
            # 排除周末
            weekdays = all_dates[all_dates.weekday < 5]
            
            # 简化版：排除主要节假日（实际应用中可以使用更完整的交易日历）
            holidays = [
                # 2023年节假日示例
                '2023-01-01', '2023-01-02', '2023-01-21', '2023-01-22', '2023-01-23', '2023-01-24', '2023-01-25', '2023-01-26', '2023-01-27',
                '2023-04-05', '2023-05-01', '2023-05-02', '2023-05-03', '2023-06-22', '2023-06-23', '2023-06-24',
                '2023-09-29', '2023-09-30', '2023-10-01', '2023-10-02', '2023-10-03', '2023-10-04', '2023-10-05', '2023-10-06',
                # 2024年节假日示例
                '2024-01-01', '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14', '2024-02-15', '2024-02-16', '2024-02-17',
                '2024-04-04', '2024-04-05', '2024-04-06', '2024-05-01', '2024-05-02', '2024-05-03',
                '2024-06-10', '2024-09-15', '2024-09-16', '2024-09-17', '2024-10-01', '2024-10-02', '2024-10-03', '2024-10-04', '2024-10-07'
            ]
            
            holiday_dates = pd.to_datetime(holidays)
            trading_days = weekdays[~weekdays.isin(holiday_dates)]
            
            self.trading_calendar = trading_days.sort_values()
            logger.info(f"加载交易日历成功，共 {len(self.trading_calendar)} 个交易日")
            
        except Exception as e:
            logger.error(f"加载交易日历失败: {e}")
            # 备用方案：只排除周末
            start_date = datetime(2020, 1, 1)
            end_date = datetime(2025, 12, 31)
            all_dates = pd.date_range(start_date, end_date, freq='D')
            self.trading_calendar = all_dates[all_dates.weekday < 5]
    
    def get_trading_days_between(self, start_date: str, end_date: str) -> pd.DatetimeIndex:
        """
        获取指定日期范围内的交易日
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            交易日列表
        """
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        mask = (self.trading_calendar >= start_dt) & (self.trading_calendar <= end_dt)
        return self.trading_calendar[mask]
    
    def get_recent_trading_days(self, num_days: int, end_date: str = None) -> pd.DatetimeIndex:
        """
        获取最近的N个交易日
        
        Args:
            num_days: 交易日数量
            end_date: 结束日期，默认为今天
            
        Returns:
            交易日列表
        """
        if end_date is None:
            end_dt = datetime.now()
        else:
            end_dt = pd.to_datetime(end_date)
        
        # 找到最近的交易日
        recent_trading_days = self.trading_calendar[self.trading_calendar <= end_dt]
        
        if len(recent_trading_days) < num_days:
            logger.warning(f"请求 {num_days} 个交易日，但只找到 {len(recent_trading_days)} 个")
            return recent_trading_days
        
        return recent_trading_days[-num_days:]
    
    def split_data_by_trading_days(self, 
                                   train_days: Union[int, Tuple[str, str]] = None,
                                   backtest_days: int = None,
                                   prediction_days: int = 20,
                                   end_date: str = None) -> Dict[str, pd.DatetimeIndex]:
        """
        按交易日分割数据
        
        Args:
            train_days: 训练数据天数或日期范围 (int 或 (start_date, end_date))
            backtest_days: 回测数据天数
            prediction_days: 预测数据天数（用于特征计算）
            end_date: 数据结束日期
            
        Returns:
            包含训练、回测、预测日期范围的字典
        """
        if end_date is None:
            end_dt = datetime.now()
        else:
            end_dt = pd.to_datetime(end_date)
        
        result = {}
        
        # 处理训练数据日期范围
        if isinstance(train_days, tuple):
            # 日期范围格式
            train_start, train_end = train_days
            train_dates = self.get_trading_days_between(train_start, train_end)
            result['train_dates'] = train_dates
            
            # 回测数据从训练结束后开始
            if backtest_days and backtest_days > 0:
                train_end_dt = pd.to_datetime(train_end)
                all_dates_after_train = self.trading_calendar[self.trading_calendar > train_end_dt]
                
                if len(all_dates_after_train) >= backtest_days:
                    result['backtest_dates'] = all_dates_after_train[:backtest_days]
                else:
                    result['backtest_dates'] = all_dates_after_train
                    logger.warning(f"回测期间不足 {backtest_days} 个交易日，实际 {len(all_dates_after_train)} 个")
        
        elif isinstance(train_days, int):
            # 交易日数量格式
            if backtest_days and backtest_days > 0:
                # 需要为回测预留数据
                total_days_needed = train_days + backtest_days
                all_recent_days = self.get_recent_trading_days(total_days_needed + prediction_days, end_date)
                
                if len(all_recent_days) >= total_days_needed:
                    # 分割训练和回测数据
                    result['train_dates'] = all_recent_days[prediction_days:prediction_days + train_days]
                    result['backtest_dates'] = all_recent_days[prediction_days + train_days:prediction_days + train_days + backtest_days]
                else:
                    logger.warning(f"数据不足，需要 {total_days_needed} 个交易日，实际 {len(all_recent_days)} 个")
                    result['train_dates'] = all_recent_days[prediction_days:-backtest_days] if len(all_recent_days) > backtest_days else all_recent_days[prediction_days:]
                    result['backtest_dates'] = all_recent_days[-backtest_days:] if len(all_recent_days) > backtest_days else pd.DatetimeIndex([])
            else:
                # 只有训练数据
                all_recent_days = self.get_recent_trading_days(train_days + prediction_days, end_date)
                result['train_dates'] = all_recent_days[prediction_days:]
        
        # 预测数据（用于特征计算的历史数据）
        result['prediction_dates'] = self.get_recent_trading_days(prediction_days, end_date)
        
        # 添加统计信息
        result['summary'] = {
            'train_days': len(result.get('train_dates', [])),
            'backtest_days': len(result.get('backtest_dates', [])),
            'prediction_days': len(result.get('prediction_dates', [])),
            'train_start': result.get('train_dates', [None])[0] if len(result.get('train_dates', [])) > 0 else None,
            'train_end': result.get('train_dates', [None])[-1] if len(result.get('train_dates', [])) > 0 else None,
            'backtest_start': result.get('backtest_dates', [None])[0] if len(result.get('backtest_dates', [])) > 0 else None,
            'backtest_end': result.get('backtest_dates', [None])[-1] if len(result.get('backtest_dates', [])) > 0 else None
        }
        
        logger.info(f"数据分割完成: 训练 {result['summary']['train_days']} 天, "
                   f"回测 {result['summary']['backtest_days']} 天, "
                   f"预测 {result['summary']['prediction_days']} 天")
        
        return result
    
    def get_time_aware_training_data(self, 
                                     stock_codes: List[str],
                                     train_config: Dict,
                                     prediction_time: str = "morning") -> Dict[str, pd.DataFrame]:
        """
        获取时间感知的训练数据
        
        Args:
            stock_codes: 股票代码列表
            train_config: 训练配置
            prediction_time: 预测时间点 ("morning" 9:35 或 "afternoon" 14:50)
            
        Returns:
            包含训练数据的字典
        """
        # 分割数据日期
        date_split = self.split_data_by_trading_days(**train_config)
        
        training_data = {}
        
        for stock_code in stock_codes:
            try:
                # 获取训练期间的数据
                if 'train_dates' in date_split and len(date_split['train_dates']) > 0:
                    start_date = date_split['train_dates'][0].strftime('%Y-%m-%d')
                    end_date = date_split['train_dates'][-1].strftime('%Y-%m-%d')
                    
                    stock_data = self.stock_data_manager.get_stock_market_data(
                        stock_code, start_date, end_date
                    )
                    
                    if not stock_data.empty:
                        # 根据预测时间点调整数据
                        if prediction_time == "morning":
                            # 早盘预测：使用前一交易日的收盘数据
                            stock_data = self._prepare_morning_prediction_data(stock_data)
                        elif prediction_time == "afternoon":
                            # 尾盘预测：使用当日盘中数据
                            stock_data = self._prepare_afternoon_prediction_data(stock_data)
                        
                        training_data[stock_code] = stock_data
                        logger.info(f"获取 {stock_code} 训练数据: {len(stock_data)} 条记录")
                
            except Exception as e:
                logger.error(f"获取 {stock_code} 训练数据失败: {e}")
        
        return {
            'data': training_data,
            'date_split': date_split,
            'prediction_time': prediction_time
        }
    
    def _prepare_morning_prediction_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备早盘预测数据（9:35）
        
        基于前一交易日的收盘数据进行预测
        """
        # 早盘预测主要基于前一日的收盘价、成交量等数据
        # 添加时间标识
        data = data.copy()
        data['prediction_time'] = 'morning'
        data['prediction_hour'] = 9
        data['prediction_minute'] = 35
        
        return data
    
    def _prepare_afternoon_prediction_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备尾盘预测数据（14:50）
        
        可以使用当日的开盘价、盘中数据等
        """
        # 尾盘预测可以使用当日的开盘价、盘中走势等信息
        # 添加时间标识
        data = data.copy()
        data['prediction_time'] = 'afternoon'
        data['prediction_hour'] = 14
        data['prediction_minute'] = 50
        
        return data
    
    def get_prediction_data(self, 
                           stock_codes: List[str],
                           prediction_days: int = 20,
                           prediction_time: str = "morning") -> Dict[str, pd.DataFrame]:
        """
        获取预测数据
        
        Args:
            stock_codes: 股票代码列表
            prediction_days: 用于预测的历史数据天数
            prediction_time: 预测时间点
            
        Returns:
            预测数据字典
        """
        prediction_dates = self.get_recent_trading_days(prediction_days)
        
        if len(prediction_dates) == 0:
            logger.error("没有找到可用的交易日数据")
            return {}
        
        start_date = prediction_dates[0].strftime('%Y-%m-%d')
        end_date = prediction_dates[-1].strftime('%Y-%m-%d')
        
        prediction_data = {}
        
        for stock_code in stock_codes:
            try:
                stock_data = self.stock_data_manager.get_stock_market_data(
                    stock_code, start_date, end_date
                )
                
                if not stock_data.empty:
                    # 根据预测时间点调整数据
                    if prediction_time == "morning":
                        stock_data = self._prepare_morning_prediction_data(stock_data)
                    elif prediction_time == "afternoon":
                        stock_data = self._prepare_afternoon_prediction_data(stock_data)
                    
                    prediction_data[stock_code] = stock_data
                    logger.info(f"获取 {stock_code} 预测数据: {len(stock_data)} 条记录")
                
            except Exception as e:
                logger.error(f"获取 {stock_code} 预测数据失败: {e}")
        
        return prediction_data


def test_time_aware_data_manager():
    """测试时间感知数据管理器"""
    logger.info("测试时间感知数据管理器")
    
    # 创建管理器
    manager = TimeAwareDataManager()
    
    # 测试1: 按交易日数量分割
    logger.info("测试1: 按交易日数量分割")
    config1 = {
        'train_days': 100,
        'backtest_days': 30,
        'prediction_days': 20
    }
    result1 = manager.split_data_by_trading_days(**config1)
    print(f"训练期间: {result1['summary']['train_start']} 到 {result1['summary']['train_end']}")
    print(f"回测期间: {result1['summary']['backtest_start']} 到 {result1['summary']['backtest_end']}")
    
    # 测试2: 按日期范围分割
    logger.info("测试2: 按日期范围分割")
    config2 = {
        'train_days': ('2023-01-03', '2023-12-29'),
        'backtest_days': 60,
        'prediction_days': 20
    }
    result2 = manager.split_data_by_trading_days(**config2)
    print(f"训练期间: {result2['summary']['train_start']} 到 {result2['summary']['train_end']}")
    print(f"回测期间: {result2['summary']['backtest_start']} 到 {result2['summary']['backtest_end']}")
    
    # 测试3: 时间感知训练数据
    logger.info("测试3: 时间感知训练数据")
    stock_codes = ['000001', '000002']
    
    # 早盘预测数据
    morning_data = manager.get_time_aware_training_data(
        stock_codes, config1, prediction_time="morning"
    )
    print(f"早盘预测数据: {len(morning_data['data'])} 只股票")
    
    # 尾盘预测数据
    afternoon_data = manager.get_time_aware_training_data(
        stock_codes, config1, prediction_time="afternoon"
    )
    print(f"尾盘预测数据: {len(afternoon_data['data'])} 只股票")
    
    logger.info("时间感知数据管理器测试完成")


if __name__ == "__main__":
    test_time_aware_data_manager()
