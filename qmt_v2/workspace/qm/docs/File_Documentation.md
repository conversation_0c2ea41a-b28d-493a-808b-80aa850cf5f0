# 📁 文件功能说明文档

## 🎯 项目概述

量化交易系统v2.0是一个基于机器学习的股票量化交易系统，集成了量化因子工程、策略回测和现代化Web界面。

## 📂 目录结构和文件说明

### 🗂️ 根目录文件

#### `main.py` - 主程序入口
- **功能**: 系统主入口，支持多种运行模式
- **模式**: test（测试）、train（训练）、predict（预测）、data（数据）
- **用法**: `python main.py --mode test`

#### `memory.py` - 开发记录
- **功能**: 详细的项目开发历史和技术文档
- **内容**: 项目概述、架构设计、开发过程、测试结果、v2.0更新记录
- **用途**: 新开发者了解项目全貌的重要参考

#### `start.sh` - 交互式启动脚本
- **功能**: 提供交互式菜单，方便用户选择不同功能
- **特点**: 用户友好的命令行界面

#### `requirements.txt` - 依赖包列表
- **功能**: 定义项目所需的Python包及版本
- **用法**: `pip install -r requirements.txt`

### 🗂️ config/ - 配置文件目录

#### `features.yaml` - 特征配置
- **功能**: 定义所有特征类型和参数
- **内容**: 基础特征、技术指标、时序特征、量化因子配置
- **特点**: 配置驱动，支持动态启用/禁用特征

#### `models.yaml` - 模型配置
- **功能**: 定义模型参数和训练配置
- **内容**: XGBoost参数、时序模型参数、训练策略
- **特点**: 支持模型参数调优和A/B测试

#### `database.yaml` - 数据库配置
- **功能**: 定义数据库表结构和数据管理策略
- **内容**: 表结构定义、索引配置、数据保留策略
- **特点**: 轻量级SQLite配置

### 🗂️ data/ - 数据层

#### `data_source.py` - 数据源接口
- **功能**: adata数据源接口封装，支持多数据源故障转移
- **特点**: 统一数据接口、自动重试、错误处理
- **支持**: A股股票列表、历史行情、实时数据

#### `stock_data_manager.py` - A股数据管理器 (v2.0新增)
- **功能**: 专门的A股数据管理器，基于adata数据源
- **特点**: 支持最近2年数据、自动更新、数据质量验证
- **设计**: 轻量级存储，实时获取，按需计算

#### `database.py` - 数据库管理器
- **功能**: SQLite数据库操作封装
- **特点**: 自动建表、数据插入/查询、连接池管理
- **设计**: 轻量级存储，不保存大量历史数据

### 🗂️ features/ - 特征工程层

#### `base_features.py` - 基础特征
- **功能**: 计算基础特征（价格、成交量、收益率、波动率）
- **特征**: 开高低收、成交量、收益率、波动率、动量等
- **设计**: 高性能pandas操作，支持批量计算

#### `technical_indicators.py` - 技术指标
- **功能**: 计算技术指标（SMA、MACD、RSI、布林带、KDJ等）
- **指标**: 移动平均、MACD、RSI、布林带、KDJ、ATR、OBV
- **特点**: 标准化技术指标实现，支持参数配置

#### `time_series_features.py` - 时序特征
- **功能**: 计算时序特征（滞后、滚动统计、季节性）
- **特征**: 滞后特征、滚动统计、季节性特征、趋势特征
- **设计**: 时间序列专用特征工程

#### `quantitative_factors.py` - 量化因子计算器 (v2.0新增)
- **功能**: 实现20+个专业量化因子
- **分类**: 动量、均值回归、成交量、波动率、趋势、技术信号
- **特点**: 高度可扩展、支持动态注册、因子重要性分析

#### `feature_engine.py` - 特征引擎
- **功能**: 统一管理所有特征计算和预处理
- **特点**: 高度可扩展、配置驱动、支持特征选择
- **设计**: 模块化架构，易于添加新特征类型

### 🗂️ models/ - 模型层

#### `xgboost_classifier.py` - XGBoost分类模型
- **功能**: 10日拉升概率预测
- **目标**: 预测未来10日是否出现明显拉升
- **特点**: 多分类问题，输出概率值

#### `xgboost_binary.py` - XGBoost二分类模型
- **功能**: 明日涨跌方向预测
- **目标**: 预测明日开盘价相对今日收盘价的涨跌
- **特点**: 二分类问题，输出方向和概率

#### `time_series_model.py` - 时序模型
- **功能**: 明日价格预测
- **目标**: 预测明日开盘价、收盘价、最高价
- **架构**: LSTM/Transformer（可选）

#### `model_manager.py` - 模型管理器
- **功能**: 统一管理所有模型的训练、预测、评估
- **特点**: 支持因子组合训练、模型性能对比、因子推荐
- **设计**: 高度抽象，易于添加新模型

### 🗂️ backtest/ - 回测层 (v2.0新增)

#### `backtest_engine.py` - 策略回测引擎
- **功能**: 完整的量化策略回测系统
- **指标**: 年化收益率、最大回撤、夏普比率、索提诺比率等
- **特点**: 交易成本模拟、风险控制、回测历史管理
- **设计**: 模块化设计，支持多种回测策略

### 🗂️ scheduler/ - 调度层

#### `prediction_scheduler.py` - 预测调度器
- **功能**: 管理定时预测任务（9:35和14:50）
- **特点**: 自动执行、错误处理、结果存储
- **设计**: 基于schedule库，支持灵活的时间配置

### 🗂️ web/ - Web界面层

#### `app.py` - Flask Web应用
- **功能**: 提供Web界面和RESTful API接口
- **API**: 系统状态、模型训练、预测执行、因子管理、回测运行
- **特点**: 增强API接口，支持因子组合管理

#### `templates/index.html` - 经典Web界面
- **功能**: 传统的Web管理界面
- **特点**: 简洁实用，功能完整

#### `templates/index_modern.html` - 现代化Web界面 (v2.0新增)
- **功能**: 现代化设计的Web界面
- **特色**: 深色主题、动态背景、玻璃拟态、流畅动画
- **技术**: CSS3、响应式设计、智能通知系统

### 🗂️ utils/ - 工具层

#### `logger.py` - 日志配置
- **功能**: 统一的日志配置和管理
- **特点**: 多级别日志、文件输出、格式化

#### `helpers.py` - 辅助函数
- **功能**: 数据生成、验证、格式化等辅助功能
- **特点**: 测试数据生成、数据验证、工具函数

### 🗂️ docs/ - 文档目录 (v2.0新增)

#### `UI_Design_Guide.md` - 界面设计指南
- **功能**: 现代化界面的设计理念和技术实现
- **内容**: 设计原则、色彩系统、动画效果、响应式设计

### 🗂️ 启动脚本 (v2.0新增)

#### `start_modern_ui.py` - 现代化界面启动脚本
- **功能**: 专门启动现代化Web界面
- **特点**: 自动打开浏览器、显示启动信息

#### `demo_modern_system.py` - 完整系统演示脚本
- **功能**: 演示系统的完整功能
- **特点**: 功能测试、性能展示、使用指南

#### `launch.py` - 统一启动器
- **功能**: 提供多种启动方式的选择菜单
- **选项**: 现代化界面、经典界面、完整演示

#### `test_enhanced_system.py` - 增强系统测试脚本
- **功能**: 测试v2.0新增功能
- **测试**: 量化因子、回测引擎、Web API接口

## 🔧 文件依赖关系

```
main.py
├── data/
│   ├── stock_data_manager.py
│   ├── data_source.py
│   └── database.py
├── features/
│   ├── quantitative_factors.py
│   ├── feature_engine.py
│   ├── base_features.py
│   ├── technical_indicators.py
│   └── time_series_features.py
├── models/
│   ├── model_manager.py
│   ├── xgboost_classifier.py
│   ├── xgboost_binary.py
│   └── time_series_model.py
├── backtest/
│   └── backtest_engine.py
├── scheduler/
│   └── prediction_scheduler.py
└── web/
    ├── app.py
    └── templates/
        ├── index.html
        └── index_modern.html
```

## 📝 开发指南

### 添加新功能
1. **新特征**: 在`features/`目录下添加新模块
2. **新模型**: 在`models/`目录下添加新模型类
3. **新因子**: 在`quantitative_factors.py`中注册新因子
4. **新API**: 在`web/app.py`中添加新接口

### 配置管理
- 所有配置都在`config/`目录下的YAML文件中
- 支持动态配置，无需重启系统
- 配置驱动的设计，易于调整参数

### 测试和调试
- 使用`test_enhanced_system.py`进行完整测试
- 查看`memory.py`了解系统设计和历史
- 使用`demo_modern_system.py`进行功能演示

---

