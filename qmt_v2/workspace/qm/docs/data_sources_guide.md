# A股免费数据源对比指南

## 📊 数据源概览

本量化交易系统支持多个免费的A股数据源，提供自动故障转移功能，确保数据获取的稳定性。

### 支持的数据源

| 数据源 | 状态 | 优先级 | 特点 | 推荐度 |
|--------|------|--------|------|--------|
| **adata** | 主要 | 1 | 专为A股设计，API简洁 | ⭐⭐⭐⭐⭐ |
| **akshare** | 备用 | 2 | 功能最全面，更新频繁 | ⭐⭐⭐⭐⭐ |
| **baostock** | 备用 | 3 | 历史数据稳定，完全免费 | ⭐⭐⭐⭐ |
| **yfinance** | 可选 | 4 | 国际化，支持港股美股 | ⭐⭐⭐ |

## 🔍 详细对比

### 1. adata
- **GitHub**: https://github.com/1nchaos/adata
- **优点**:
  - 专门针对A股市场设计
  - API接口简洁易用
  - 数据质量稳定
  - 支持实时和历史数据
- **缺点**:
  - 功能相对有限
  - 社区规模较小
- **适用场景**: 主要数据源，日常量化分析

### 2. akshare
- **GitHub**: https://github.com/akfamily/akshare
- **优点**:
  - 数据源最全面（股票、期货、基金、债券、指数）
  - 更新频繁，社区活跃
  - 支持新闻、公告、财务分析
  - 文档详细
- **缺点**:
  - 包体积较大
  - 部分接口可能不稳定
- **适用场景**: 备用数据源，全面数据分析

### 3. baostock
- **官网**: http://baostock.com/
- **优点**:
  - 完全免费，无限制
  - 历史数据质量高
  - 接口稳定
  - 支持分钟级数据
- **缺点**:
  - 不支持实时数据
  - 功能相对简单
  - 更新较慢
- **适用场景**: 历史数据回测，长期数据存储

### 4. yfinance
- **GitHub**: https://github.com/ranaroussi/yfinance
- **优点**:
  - 支持全球市场
  - 包含港股、美股
  - 接口标准化
- **缺点**:
  - A股数据有限
  - 主要面向海外市场
- **适用场景**: 国际化投资组合分析

## 🚀 快速开始

### 安装数据源

```bash
# 方法1: 使用安装脚本（推荐）
python install_data_sources.py

# 方法2: 手动安装
pip install adata akshare baostock yfinance
```

### 基本使用

```python
from data.multi_data_source import MultiDataSourceManager

# 创建多数据源管理器
manager = MultiDataSourceManager()

# 获取股票列表（自动故障转移）
stock_list = manager.get_stock_list()

# 获取行情数据（自动故障转移）
market_data = manager.get_stock_market_data('000001', '2024-01-01', '2024-12-31')

# 获取实时行情（自动故障转移）
current_data = manager.get_current_market_data(['000001', '000002'])

# 测试所有数据源
test_results = manager.test_all_sources()
```

## ⚙️ 配置说明

### 数据源优先级配置

在 `config/database.yaml` 中配置：

```yaml
data_source:
  priority:
    primary: "adata"              # 主数据源
    fallback: ["akshare", "baostock"]  # 备用数据源
    auto_failover: true           # 自动故障转移
```

### 各数据源详细配置

```yaml
data_source:
  adata:
    enabled: true
    request_interval: 0.1
    max_retries: 3
    timeout: 30
    
  akshare:
    enabled: true
    request_interval: 0.2
    use_cache: true
    cache_expire_minutes: 5
    
  baostock:
    enabled: true
    auto_login: true
    
  yfinance:
    enabled: false
    supported_markets: ["HK", "US"]
```

## 🔧 故障转移机制

系统实现了智能的故障转移机制：

1. **自动检测**: 实时检测数据源可用性
2. **优先级切换**: 按配置的优先级自动切换
3. **错误恢复**: 主数据源恢复后自动切回
4. **日志记录**: 详细记录切换过程

### 故障转移流程

```
请求数据 → 尝试主数据源 → 失败？ → 尝试备用数据源1 → 失败？ → 尝试备用数据源2 → ...
```

## 📈 性能优化建议

### 1. 数据源选择策略
- **实时数据**: adata > akshare
- **历史数据**: baostock > adata > akshare
- **全面数据**: akshare > adata
- **国际数据**: yfinance

### 2. 请求频率控制
```python
# 在配置文件中设置合适的请求间隔
request_interval: 0.1  # adata
request_interval: 0.2  # akshare
request_interval: 0.1  # baostock
```

### 3. 缓存策略
```python
# akshare支持缓存
use_cache: true
cache_expire_minutes: 5
```

## 🛠️ 故障排除

### 常见问题

1. **数据源安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 使用国内镜像
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ adata
   ```

2. **网络连接问题**
   ```yaml
   # 配置代理
   adata:
     use_proxy: true
     proxy:
       ip: "127.0.0.1"
       port: "7890"
   ```

3. **数据获取失败**
   - 检查网络连接
   - 验证股票代码格式
   - 查看日志文件
   - 尝试其他数据源

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试单个数据源
manager = MultiDataSourceManager()
results = manager.test_all_sources()
print(results)
```

## 📝 最佳实践

1. **多数据源配置**: 至少配置2个数据源确保稳定性
2. **定期测试**: 定期运行数据源测试确保可用性
3. **监控日志**: 关注数据源切换日志
4. **合理限频**: 设置合适的请求间隔避免被限制
5. **数据验证**: 对获取的数据进行基本验证

## 🔮 未来扩展

计划支持的数据源：
- **tushare**: 老牌数据源
- **聚宽**: JoinQuant数据
- **米筐**: RiceQuant数据
- **Wind**: 万得数据（付费）

## 📞 技术支持

如果遇到问题：
1. 查看日志文件
2. 运行数据源测试
3. 检查网络连接
4. 更新数据源包版本

---

*本指南会随着系统更新持续完善，建议定期查看最新版本。*
