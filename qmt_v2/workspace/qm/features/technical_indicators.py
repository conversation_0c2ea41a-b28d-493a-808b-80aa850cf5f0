"""
技术指标模块 - 计算各种技术分析指标
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logger.warning("talib库未安装，将使用自定义实现")


class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.talib_available = TALIB_AVAILABLE
    
    def calculate_sma(self, df: pd.DataFrame, windows: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
        """
        计算简单移动平均线
        
        Args:
            df: 包含价格数据的DataFrame
            windows: 移动平均窗口列表
            
        Returns:
            包含SMA指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            for window in windows:
                if self.talib_available:
                    result_df[f'sma_{window}'] = talib.SMA(result_df['close'].values, timeperiod=window)
                else:
                    result_df[f'sma_{window}'] = result_df['close'].rolling(window=window).mean()
                
                # 计算价格相对于移动平均线的位置
                result_df[f'close_sma_{window}_ratio'] = result_df['close'] / result_df[f'sma_{window}']
                
                logger.debug(f"计算 SMA_{window} 完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算SMA失败: {e}")
            return df
    
    def calculate_ema(self, df: pd.DataFrame, windows: List[int] = [12, 26, 50]) -> pd.DataFrame:
        """
        计算指数移动平均线
        
        Args:
            df: 包含价格数据的DataFrame
            windows: EMA窗口列表
            
        Returns:
            包含EMA指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            for window in windows:
                if self.talib_available:
                    result_df[f'ema_{window}'] = talib.EMA(result_df['close'].values, timeperiod=window)
                else:
                    result_df[f'ema_{window}'] = result_df['close'].ewm(span=window).mean()
                
                # 计算价格相对于EMA的位置
                result_df[f'close_ema_{window}_ratio'] = result_df['close'] / result_df[f'ema_{window}']
                
                logger.debug(f"计算 EMA_{window} 完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算EMA失败: {e}")
            return df
    
    def calculate_macd(self, df: pd.DataFrame, 
                      fast_period: int = 12, 
                      slow_period: int = 26, 
                      signal_period: int = 9) -> pd.DataFrame:
        """
        计算MACD指标
        
        Args:
            df: 包含价格数据的DataFrame
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            
        Returns:
            包含MACD指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            if self.talib_available:
                macd, macd_signal, macd_hist = talib.MACD(
                    result_df['close'].values,
                    fastperiod=fast_period,
                    slowperiod=slow_period,
                    signalperiod=signal_period
                )
                result_df['macd'] = macd
                result_df['macd_signal'] = macd_signal
                result_df['macd_histogram'] = macd_hist
            else:
                # 自定义MACD计算
                ema_fast = result_df['close'].ewm(span=fast_period).mean()
                ema_slow = result_df['close'].ewm(span=slow_period).mean()
                result_df['macd'] = ema_fast - ema_slow
                result_df['macd_signal'] = result_df['macd'].ewm(span=signal_period).mean()
                result_df['macd_histogram'] = result_df['macd'] - result_df['macd_signal']
            
            # MACD金叉死叉信号
            result_df['macd_golden_cross'] = (
                (result_df['macd'] > result_df['macd_signal']) & 
                (result_df['macd'].shift(1) <= result_df['macd_signal'].shift(1))
            ).astype(int)
            
            result_df['macd_death_cross'] = (
                (result_df['macd'] < result_df['macd_signal']) & 
                (result_df['macd'].shift(1) >= result_df['macd_signal'].shift(1))
            ).astype(int)
            
            logger.debug("计算MACD完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算MACD失败: {e}")
            return df
    
    def calculate_rsi(self, df: pd.DataFrame, windows: List[int] = [6, 14, 21]) -> pd.DataFrame:
        """
        计算RSI指标
        
        Args:
            df: 包含价格数据的DataFrame
            windows: RSI窗口列表
            
        Returns:
            包含RSI指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            for window in windows:
                if self.talib_available:
                    result_df[f'rsi_{window}'] = talib.RSI(result_df['close'].values, timeperiod=window)
                else:
                    # 自定义RSI计算
                    delta = result_df['close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
                    rs = gain / loss
                    result_df[f'rsi_{window}'] = 100 - (100 / (1 + rs))
                
                # RSI超买超卖信号
                result_df[f'rsi_{window}_overbought'] = (result_df[f'rsi_{window}'] > 70).astype(int)
                result_df[f'rsi_{window}_oversold'] = (result_df[f'rsi_{window}'] < 30).astype(int)
                
                logger.debug(f"计算 RSI_{window} 完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算RSI失败: {e}")
            return df
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, 
                                 window: int = 20, 
                                 std_dev: float = 2) -> pd.DataFrame:
        """
        计算布林带指标
        
        Args:
            df: 包含价格数据的DataFrame
            window: 移动平均窗口
            std_dev: 标准差倍数
            
        Returns:
            包含布林带指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            if self.talib_available:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(
                    result_df['close'].values,
                    timeperiod=window,
                    nbdevup=std_dev,
                    nbdevdn=std_dev,
                    matype=0
                )
                result_df['bb_upper'] = bb_upper
                result_df['bb_middle'] = bb_middle
                result_df['bb_lower'] = bb_lower
            else:
                # 自定义布林带计算
                result_df['bb_middle'] = result_df['close'].rolling(window=window).mean()
                bb_std = result_df['close'].rolling(window=window).std()
                result_df['bb_upper'] = result_df['bb_middle'] + (bb_std * std_dev)
                result_df['bb_lower'] = result_df['bb_middle'] - (bb_std * std_dev)
            
            # 布林带相关指标
            result_df['bb_width'] = (result_df['bb_upper'] - result_df['bb_lower']) / result_df['bb_middle']
            result_df['bb_position'] = (result_df['close'] - result_df['bb_lower']) / (result_df['bb_upper'] - result_df['bb_lower'])
            
            # 布林带突破信号
            result_df['bb_upper_break'] = (result_df['close'] > result_df['bb_upper']).astype(int)
            result_df['bb_lower_break'] = (result_df['close'] < result_df['bb_lower']).astype(int)
            
            logger.debug("计算布林带完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算布林带失败: {e}")
            return df
    
    def calculate_kdj(self, df: pd.DataFrame, 
                     k_period: int = 9, 
                     d_period: int = 3, 
                     j_period: int = 3) -> pd.DataFrame:
        """
        计算KDJ指标
        
        Args:
            df: 包含OHLC数据的DataFrame
            k_period: K值计算周期
            d_period: D值平滑周期
            j_period: J值计算周期
            
        Returns:
            包含KDJ指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 计算RSV
            low_min = result_df['low'].rolling(window=k_period).min()
            high_max = result_df['high'].rolling(window=k_period).max()
            rsv = (result_df['close'] - low_min) / (high_max - low_min) * 100
            
            # 计算K值
            result_df['kdj_k'] = rsv.ewm(alpha=1/d_period).mean()
            
            # 计算D值
            result_df['kdj_d'] = result_df['kdj_k'].ewm(alpha=1/d_period).mean()
            
            # 计算J值
            result_df['kdj_j'] = 3 * result_df['kdj_k'] - 2 * result_df['kdj_d']
            
            # KDJ金叉死叉信号
            result_df['kdj_golden_cross'] = (
                (result_df['kdj_k'] > result_df['kdj_d']) & 
                (result_df['kdj_k'].shift(1) <= result_df['kdj_d'].shift(1))
            ).astype(int)
            
            result_df['kdj_death_cross'] = (
                (result_df['kdj_k'] < result_df['kdj_d']) & 
                (result_df['kdj_k'].shift(1) >= result_df['kdj_d'].shift(1))
            ).astype(int)
            
            logger.debug("计算KDJ完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算KDJ失败: {e}")
            return df
    
    def calculate_atr(self, df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
        """
        计算平均真实波幅(ATR)
        
        Args:
            df: 包含OHLC数据的DataFrame
            window: 计算窗口
            
        Returns:
            包含ATR指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            if self.talib_available:
                result_df['atr'] = talib.ATR(
                    result_df['high'].values,
                    result_df['low'].values,
                    result_df['close'].values,
                    timeperiod=window
                )
            else:
                # 自定义ATR计算
                high_low = result_df['high'] - result_df['low']
                high_close_prev = np.abs(result_df['high'] - result_df['close'].shift(1))
                low_close_prev = np.abs(result_df['low'] - result_df['close'].shift(1))
                
                true_range = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
                result_df['atr'] = true_range.rolling(window=window).mean()
            
            # ATR相对值
            result_df['atr_ratio'] = result_df['atr'] / result_df['close']
            
            logger.debug("计算ATR完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算ATR失败: {e}")
            return df
    
    def calculate_obv(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算能量潮指标(OBV)
        
        Args:
            df: 包含价格和成交量数据的DataFrame
            
        Returns:
            包含OBV指标的DataFrame
        """
        result_df = df.copy()
        
        try:
            if self.talib_available:
                result_df['obv'] = talib.OBV(result_df['close'].values, result_df['volume'].values)
            else:
                # 自定义OBV计算
                price_change = result_df['close'].diff()
                obv_change = np.where(price_change > 0, result_df['volume'],
                                    np.where(price_change < 0, -result_df['volume'], 0))
                result_df['obv'] = obv_change.cumsum()
            
            # OBV移动平均
            result_df['obv_ma_10'] = result_df['obv'].rolling(window=10).mean()
            result_df['obv_ma_20'] = result_df['obv'].rolling(window=20).mean()
            
            logger.debug("计算OBV完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算OBV失败: {e}")
            return df
    
    def calculate_all_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有技术指标的DataFrame
        """
        try:
            logger.info("开始计算技术指标...")
            
            result_df = df.copy()
            
            # 确保数据按日期排序
            if 'trade_date' in result_df.columns:
                result_df = result_df.sort_values('trade_date').reset_index(drop=True)
            
            # 计算各类技术指标
            result_df = self.calculate_sma(result_df)
            result_df = self.calculate_ema(result_df)
            result_df = self.calculate_macd(result_df)
            result_df = self.calculate_rsi(result_df)
            result_df = self.calculate_bollinger_bands(result_df)
            result_df = self.calculate_kdj(result_df)
            result_df = self.calculate_atr(result_df)
            
            if 'volume' in result_df.columns:
                result_df = self.calculate_obv(result_df)
            
            logger.info(f"技术指标计算完成，共生成 {len(result_df.columns) - len(df.columns)} 个新指标")
            return result_df
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return df


# 测试函数
def test_technical_indicators():
    """测试技术指标计算"""
    logger.info("开始测试技术指标计算...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 生成更真实的价格数据
    price = 10
    prices = [price]
    for i in range(99):
        price = price * (1 + np.random.normal(0, 0.02))
        prices.append(price)
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'close': prices,
        'volume': 1000000 + np.random.randint(-200000, 200000, 100)
    })
    
    # 生成OHLC数据
    test_data['open'] = test_data['close'].shift(1).fillna(test_data['close'].iloc[0])
    test_data['high'] = test_data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.02, 100))
    test_data['low'] = test_data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.02, 100))
    
    # 计算技术指标
    ti = TechnicalIndicators()
    result = ti.calculate_all_technical_indicators(test_data)
    
    logger.info(f"测试完成，原始数据 {len(test_data.columns)} 列，指标数据 {len(result.columns)} 列")
    logger.info(f"新增指标: {list(set(result.columns) - set(test_data.columns))}")
    
    # 显示部分结果
    print(result[['trade_date', 'close', 'sma_5', 'sma_20', 'rsi_14', 'macd']].head(10))


if __name__ == "__main__":
    test_technical_indicators()
