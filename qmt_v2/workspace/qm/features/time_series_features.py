"""
时序特征模块 - 计算时间序列相关特征
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from loguru import logger


class TimeSeriesFeatures:
    """时序特征计算类"""
    
    def __init__(self):
        """初始化时序特征计算器"""
        pass
    
    def calculate_lag_features(self, df: pd.DataFrame, 
                              columns: List[str] = ['close', 'volume'],
                              lags: List[int] = [1, 3, 5, 10]) -> pd.DataFrame:
        """
        计算滞后特征
        
        Args:
            df: 输入数据DataFrame
            columns: 需要计算滞后的列名
            lags: 滞后期数列表
            
        Returns:
            包含滞后特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            for column in columns:
                if column not in result_df.columns:
                    logger.warning(f"列 {column} 不存在，跳过")
                    continue
                    
                for lag in lags:
                    result_df[f'{column}_lag_{lag}'] = result_df[column].shift(lag)
                    
                    # 计算滞后变化率
                    if lag == 1:
                        result_df[f'{column}_lag_{lag}_change'] = result_df[column].pct_change(periods=lag)
                    
                    logger.debug(f"计算 {column} 滞后 {lag} 期特征完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算滞后特征失败: {e}")
            return df
    
    def calculate_rolling_features(self, df: pd.DataFrame,
                                  columns: List[str] = ['close', 'volume'],
                                  windows: List[int] = [3, 5, 10, 20],
                                  functions: List[str] = ['mean', 'std', 'min', 'max']) -> pd.DataFrame:
        """
        计算滚动统计特征
        
        Args:
            df: 输入数据DataFrame
            columns: 需要计算滚动统计的列名
            windows: 滚动窗口列表
            functions: 统计函数列表
            
        Returns:
            包含滚动统计特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            for column in columns:
                if column not in result_df.columns:
                    logger.warning(f"列 {column} 不存在，跳过")
                    continue
                    
                for window in windows:
                    rolling_obj = result_df[column].rolling(window=window)
                    
                    for func in functions:
                        if hasattr(rolling_obj, func):
                            result_df[f'{column}_rolling_{func}_{window}'] = getattr(rolling_obj, func)()
                        
                        logger.debug(f"计算 {column} 滚动 {func}_{window} 完成")
                    
                    # 计算相对于滚动均值的位置
                    if 'mean' in functions:
                        result_df[f'{column}_rolling_position_{window}'] = (
                            result_df[column] / result_df[f'{column}_rolling_mean_{window}']
                        )
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算滚动统计特征失败: {e}")
            return df
    
    def calculate_seasonal_features(self, df: pd.DataFrame, 
                                   date_column: str = 'trade_date') -> pd.DataFrame:
        """
        计算季节性特征
        
        Args:
            df: 输入数据DataFrame
            date_column: 日期列名
            
        Returns:
            包含季节性特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            if date_column not in result_df.columns:
                logger.warning(f"日期列 {date_column} 不存在")
                return result_df
            
            # 确保日期列是datetime类型
            if not pd.api.types.is_datetime64_any_dtype(result_df[date_column]):
                result_df[date_column] = pd.to_datetime(result_df[date_column])
            
            # 提取时间特征
            result_df['year'] = result_df[date_column].dt.year
            result_df['month'] = result_df[date_column].dt.month
            result_df['day'] = result_df[date_column].dt.day
            result_df['day_of_week'] = result_df[date_column].dt.dayofweek  # 0=Monday
            result_df['day_of_year'] = result_df[date_column].dt.dayofyear
            result_df['week_of_year'] = result_df[date_column].dt.isocalendar().week
            result_df['quarter'] = result_df[date_column].dt.quarter
            
            # 是否为月初/月末
            result_df['is_month_start'] = result_df[date_column].dt.is_month_start.astype(int)
            result_df['is_month_end'] = result_df[date_column].dt.is_month_end.astype(int)
            result_df['is_quarter_start'] = result_df[date_column].dt.is_quarter_start.astype(int)
            result_df['is_quarter_end'] = result_df[date_column].dt.is_quarter_end.astype(int)
            result_df['is_year_start'] = result_df[date_column].dt.is_year_start.astype(int)
            result_df['is_year_end'] = result_df[date_column].dt.is_year_end.astype(int)
            
            # 周期性编码（使用三角函数）
            result_df['month_sin'] = np.sin(2 * np.pi * result_df['month'] / 12)
            result_df['month_cos'] = np.cos(2 * np.pi * result_df['month'] / 12)
            result_df['day_of_week_sin'] = np.sin(2 * np.pi * result_df['day_of_week'] / 7)
            result_df['day_of_week_cos'] = np.cos(2 * np.pi * result_df['day_of_week'] / 7)
            result_df['day_of_year_sin'] = np.sin(2 * np.pi * result_df['day_of_year'] / 365)
            result_df['day_of_year_cos'] = np.cos(2 * np.pi * result_df['day_of_year'] / 365)
            
            logger.debug("计算季节性特征完成")
            return result_df

        except Exception as e:
            logger.error(f"计算季节性特征失败: {e}")
            return df

    def calculate_all_time_series_features(self, df: pd.DataFrame,
                                          sequence_length: int = 20) -> pd.DataFrame:
        """
        计算所有时序特征

        Args:
            df: 输入数据DataFrame
            sequence_length: 序列长度（用于时序模型）

        Returns:
            包含所有时序特征的DataFrame
        """
        try:
            logger.info("开始计算时序特征...")

            result_df = df.copy()

            # 确保数据按日期排序
            if 'trade_date' in result_df.columns:
                result_df = result_df.sort_values('trade_date').reset_index(drop=True)

            # 计算各类时序特征
            result_df = self.calculate_lag_features(result_df)
            result_df = self.calculate_rolling_features(result_df)
            result_df = self.calculate_seasonal_features(result_df)

            logger.info(f"时序特征计算完成，共生成 {len(result_df.columns) - len(df.columns)} 个新特征")
            return result_df

        except Exception as e:
            logger.error(f"计算时序特征失败: {e}")
            return df


# 测试函数
def test_time_series_features():
    """测试时序特征计算"""
    logger.info("开始测试时序特征计算...")

    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)

    # 生成带趋势的价格数据
    trend = np.linspace(10, 12, 100)
    noise = np.random.normal(0, 0.1, 100)
    prices = trend + noise

    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'close': prices,
        'volume': 1000000 + np.random.randint(-200000, 200000, 100)
    })

    # 计算时序特征
    tsf = TimeSeriesFeatures()
    result = tsf.calculate_all_time_series_features(test_data, sequence_length=10)

    logger.info(f"测试完成，原始数据 {len(test_data.columns)} 列，特征数据 {len(result.columns)} 列")
    logger.info(f"新增特征数量: {len(result.columns) - len(test_data.columns)}")

    # 显示部分结果
    feature_cols = ['trade_date', 'close', 'close_lag_1', 'close_rolling_mean_5', 'day_of_week']
    available_cols = [col for col in feature_cols if col in result.columns]
    print(result[available_cols].head(10))


if __name__ == "__main__":
    test_time_series_features()
    
    def calculate_trend_features(self, df: pd.DataFrame,
                                columns: List[str] = ['close'],
                                windows: List[int] = [5, 10, 20]) -> pd.DataFrame:
        """
        计算趋势特征
        
        Args:
            df: 输入数据DataFrame
            columns: 需要计算趋势的列名
            windows: 趋势计算窗口
            
        Returns:
            包含趋势特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            for column in columns:
                if column not in result_df.columns:
                    logger.warning(f"列 {column} 不存在，跳过")
                    continue
                
                for window in windows:
                    # 线性回归斜率（趋势强度）
                    def calculate_slope(series):
                        if len(series) < 2:
                            return np.nan
                        x = np.arange(len(series))
                        y = series.values
                        if np.all(np.isnan(y)):
                            return np.nan
                        slope = np.polyfit(x, y, 1)[0]
                        return slope
                    
                    result_df[f'{column}_trend_slope_{window}'] = (
                        result_df[column].rolling(window=window).apply(calculate_slope, raw=False)
                    )
                    
                    # 趋势方向（上升/下降/横盘）
                    result_df[f'{column}_trend_direction_{window}'] = np.where(
                        result_df[f'{column}_trend_slope_{window}'] > 0.001, 1,  # 上升
                        np.where(result_df[f'{column}_trend_slope_{window}'] < -0.001, -1, 0)  # 下降/横盘
                    )
                    
                    # 趋势强度（R²）
                    def calculate_r_squared(series):
                        if len(series) < 2:
                            return np.nan
                        x = np.arange(len(series))
                        y = series.values
                        if np.all(np.isnan(y)):
                            return np.nan
                        try:
                            correlation_matrix = np.corrcoef(x, y)
                            correlation = correlation_matrix[0, 1]
                            r_squared = correlation ** 2
                            return r_squared
                        except:
                            return np.nan
                    
                    result_df[f'{column}_trend_strength_{window}'] = (
                        result_df[column].rolling(window=window).apply(calculate_r_squared, raw=False)
                    )
                    
                    logger.debug(f"计算 {column} 趋势特征_{window} 完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算趋势特征失败: {e}")
            return df
    
    def calculate_autocorrelation_features(self, df: pd.DataFrame,
                                          columns: List[str] = ['close'],
                                          lags: List[int] = [1, 5, 10]) -> pd.DataFrame:
        """
        计算自相关特征
        
        Args:
            df: 输入数据DataFrame
            columns: 需要计算自相关的列名
            lags: 自相关滞后期
            
        Returns:
            包含自相关特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            for column in columns:
                if column not in result_df.columns:
                    logger.warning(f"列 {column} 不存在，跳过")
                    continue
                
                # 计算收益率序列
                returns = result_df[column].pct_change().dropna()
                
                for lag in lags:
                    # 滚动自相关
                    def rolling_autocorr(series, lag_period):
                        if len(series) < lag_period + 10:  # 需要足够的数据点
                            return np.nan
                        try:
                            return series.autocorr(lag=lag_period)
                        except:
                            return np.nan
                    
                    result_df[f'{column}_autocorr_{lag}'] = (
                        returns.rolling(window=30).apply(lambda x: rolling_autocorr(x, lag), raw=False)
                    )
                    
                    logger.debug(f"计算 {column} 自相关_{lag} 完成")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算自相关特征失败: {e}")
            return df
    
    def calculate_sequence_features(self, df: pd.DataFrame,
                                   sequence_length: int = 20) -> pd.DataFrame:
        """
        为时序模型准备序列特征
        
        Args:
            df: 输入数据DataFrame
            sequence_length: 序列长度
            
        Returns:
            包含序列特征的DataFrame
        """
        result_df = df.copy()
        
        try:
            # 为每个数值列创建序列特征
            numeric_columns = result_df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in ['year', 'month', 'day', 'day_of_week']:  # 跳过时间特征
                    continue
                    
                # 创建滑动窗口序列
                for i in range(1, sequence_length + 1):
                    result_df[f'{col}_seq_{i}'] = result_df[col].shift(i)
            
            # 序列统计特征
            for window in [5, 10, sequence_length]:
                if window <= sequence_length:
                    result_df[f'close_seq_mean_{window}'] = result_df['close'].rolling(window=window).mean()
                    result_df[f'close_seq_std_{window}'] = result_df['close'].rolling(window=window).std()
                    result_df[f'close_seq_min_{window}'] = result_df['close'].rolling(window=window).min()
                    result_df[f'close_seq_max_{window}'] = result_df['close'].rolling(window=window).max()
            
            logger.debug(f"计算序列特征完成，序列长度: {sequence_length}")
            return result_df
            
        except Exception as e:
            logger.error(f"计算序列特征失败: {e}")
            return df
    
    def calculate_all_time_series_features(self, df: pd.DataFrame,
                                          sequence_length: int = 20) -> pd.DataFrame:
        """
        计算所有时序特征
        
        Args:
            df: 输入数据DataFrame
            sequence_length: 序列长度（用于时序模型）
            
        Returns:
            包含所有时序特征的DataFrame
        """
        try:
            logger.info("开始计算时序特征...")
            
            result_df = df.copy()
            
            # 确保数据按日期排序
            if 'trade_date' in result_df.columns:
                result_df = result_df.sort_values('trade_date').reset_index(drop=True)
            
            # 计算各类时序特征
            result_df = self.calculate_lag_features(result_df)
            result_df = self.calculate_rolling_features(result_df)
            result_df = self.calculate_seasonal_features(result_df)
            result_df = self.calculate_trend_features(result_df)
            result_df = self.calculate_autocorrelation_features(result_df)
            
            # 如果需要序列特征（用于时序模型）
            if sequence_length > 0:
                result_df = self.calculate_sequence_features(result_df, sequence_length)
            
            logger.info(f"时序特征计算完成，共生成 {len(result_df.columns) - len(df.columns)} 个新特征")
            return result_df
            
        except Exception as e:
            logger.error(f"计算时序特征失败: {e}")
            return df


# 测试函数
def test_time_series_features():
    """测试时序特征计算"""
    logger.info("开始测试时序特征计算...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 生成带趋势的价格数据
    trend = np.linspace(10, 12, 100)
    noise = np.random.normal(0, 0.1, 100)
    prices = trend + noise
    
    test_data = pd.DataFrame({
        'trade_date': dates,
        'stock_code': '000001',
        'close': prices,
        'volume': 1000000 + np.random.randint(-200000, 200000, 100)
    })
    
    # 计算时序特征
    tsf = TimeSeriesFeatures()
    result = tsf.calculate_all_time_series_features(test_data, sequence_length=10)
    
    logger.info(f"测试完成，原始数据 {len(test_data.columns)} 列，特征数据 {len(result.columns)} 列")
    logger.info(f"新增特征数量: {len(result.columns) - len(test_data.columns)}")
    
    # 显示部分结果
    feature_cols = ['trade_date', 'close', 'close_lag_1', 'close_rolling_mean_5', 'day_of_week', 'close_trend_slope_10']
    available_cols = [col for col in feature_cols if col in result.columns]
    print(result[available_cols].head(10))


if __name__ == "__main__":
    test_time_series_features()
