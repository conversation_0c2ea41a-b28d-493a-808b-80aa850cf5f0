#!/usr/bin/env python3
"""
数据源安装脚本
自动安装和配置A股免费数据源
"""
import subprocess
import sys
import os
from loguru import logger

# 数据源包信息
DATA_SOURCES = {
    'adata': {
        'package': 'adata',
        'description': 'A股数据获取工具 - 主要数据源',
        'install_cmd': 'pip install adata',
        'test_import': 'import adata',
        'priority': 1,
        'required': True
    },
    'akshare': {
        'package': 'akshare',
        'description': 'A股数据获取工具 - 最全面的数据源',
        'install_cmd': 'pip install akshare',
        'test_import': 'import akshare',
        'priority': 2,
        'required': False
    },
    'baostock': {
        'package': 'baostock',
        'description': 'A股历史数据获取工具 - 免费稳定',
        'install_cmd': 'pip install baostock',
        'test_import': 'import baostock',
        'priority': 3,
        'required': False
    },
    'yfinance': {
        'package': 'yfinance',
        'description': '全球股票数据获取工具 - 支持港股美股',
        'install_cmd': 'pip install yfinance',
        'test_import': 'import yfinance',
        'priority': 4,
        'required': False
    }
}


def check_package_installed(package_name: str) -> bool:
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def install_package(package_info: dict) -> bool:
    """安装数据源包"""
    package_name = package_info['package']
    install_cmd = package_info['install_cmd']
    
    logger.info(f"正在安装 {package_name}...")
    logger.info(f"描述: {package_info['description']}")
    
    try:
        # 执行安装命令
        result = subprocess.run(
            install_cmd.split(),
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            logger.success(f"{package_name} 安装成功")
            return True
        else:
            logger.error(f"{package_name} 安装失败:")
            logger.error(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"{package_name} 安装超时")
        return False
    except Exception as e:
        logger.error(f"{package_name} 安装异常: {e}")
        return False


def test_package_import(package_info: dict) -> bool:
    """测试包导入"""
    package_name = package_info['package']
    test_import = package_info['test_import']
    
    try:
        exec(test_import)
        logger.success(f"{package_name} 导入测试成功")
        return True
    except Exception as e:
        logger.error(f"{package_name} 导入测试失败: {e}")
        return False


def show_installation_status():
    """显示安装状态"""
    print("\n" + "="*60)
    print("A股数据源安装状态")
    print("="*60)
    
    for name, info in DATA_SOURCES.items():
        status = "✅ 已安装" if check_package_installed(info['package']) else "❌ 未安装"
        required = "必需" if info['required'] else "可选"
        print(f"{name:12} | {status:8} | {required:4} | {info['description']}")
    
    print("="*60)


def install_all_sources(install_optional: bool = True):
    """安装所有数据源"""
    logger.info("开始安装A股数据源...")
    
    installed_count = 0
    failed_count = 0
    
    # 按优先级排序
    sorted_sources = sorted(DATA_SOURCES.items(), key=lambda x: x[1]['priority'])
    
    for name, info in sorted_sources:
        # 检查是否已安装
        if check_package_installed(info['package']):
            logger.info(f"{name} 已安装，跳过")
            installed_count += 1
            continue
        
        # 如果是可选包且用户不想安装可选包，跳过
        if not info['required'] and not install_optional:
            logger.info(f"{name} 是可选包，跳过安装")
            continue
        
        # 安装包
        if install_package(info):
            # 测试导入
            if test_package_import(info):
                installed_count += 1
            else:
                failed_count += 1
        else:
            failed_count += 1
    
    # 显示安装结果
    logger.info(f"安装完成: 成功 {installed_count} 个，失败 {failed_count} 个")
    
    return installed_count, failed_count


def interactive_install():
    """交互式安装"""
    print("\n🚀 A股量化交易系统 - 数据源安装工具")
    print("="*50)
    
    # 显示当前状态
    show_installation_status()
    
    print("\n选择安装模式:")
    print("1. 安装所有数据源（推荐）")
    print("2. 只安装必需数据源")
    print("3. 自定义安装")
    print("4. 检查安装状态")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            logger.info("选择: 安装所有数据源")
            install_all_sources(install_optional=True)
            break
        elif choice == '2':
            logger.info("选择: 只安装必需数据源")
            install_all_sources(install_optional=False)
            break
        elif choice == '3':
            logger.info("选择: 自定义安装")
            custom_install()
            break
        elif choice == '4':
            show_installation_status()
            continue
        elif choice == '5':
            logger.info("退出安装程序")
            break
        else:
            print("无效选择，请重新输入")


def custom_install():
    """自定义安装"""
    print("\n自定义安装模式:")
    print("请选择要安装的数据源 (多选用空格分隔，如: 1 2 3)")
    
    # 显示选项
    options = []
    for i, (name, info) in enumerate(DATA_SOURCES.items(), 1):
        status = "已安装" if check_package_installed(info['package']) else "未安装"
        required = "必需" if info['required'] else "可选"
        print(f"{i}. {name:12} | {status:8} | {required:4} | {info['description']}")
        options.append((name, info))
    
    # 获取用户选择
    while True:
        try:
            choices = input("\n请选择要安装的数据源编号: ").strip().split()
            if not choices:
                print("未选择任何数据源")
                return
            
            selected_indices = [int(choice) - 1 for choice in choices]
            
            # 验证选择
            if all(0 <= idx < len(options) for idx in selected_indices):
                break
            else:
                print("选择超出范围，请重新输入")
        except ValueError:
            print("输入格式错误，请输入数字")
    
    # 安装选中的数据源
    installed_count = 0
    failed_count = 0
    
    for idx in selected_indices:
        name, info = options[idx]
        
        if check_package_installed(info['package']):
            logger.info(f"{name} 已安装，跳过")
            installed_count += 1
            continue
        
        if install_package(info):
            if test_package_import(info):
                installed_count += 1
            else:
                failed_count += 1
        else:
            failed_count += 1
    
    logger.info(f"自定义安装完成: 成功 {installed_count} 个，失败 {failed_count} 个")


def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 7):
        logger.error("需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查pip
    try:
        subprocess.run(['pip', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("pip未找到，请先安装pip")
        sys.exit(1)
    
    # 运行交互式安装
    interactive_install()
    
    # 最终状态检查
    print("\n最终安装状态:")
    show_installation_status()
    
    # 给出使用建议
    print("\n💡 使用建议:")
    print("1. adata 是主要数据源，建议优先使用")
    print("2. akshare 功能最全面，可作为备用数据源")
    print("3. baostock 适合获取历史数据，免费稳定")
    print("4. yfinance 支持港股美股，适合国际化需求")
    print("\n系统会自动进行故障转移，确保数据获取的稳定性。")


if __name__ == "__main__":
    main()
