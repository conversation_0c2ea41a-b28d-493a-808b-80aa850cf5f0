"""
量化交易系统主程序
"""
import os
import sys
import argparse
from datetime import datetime
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logger
from utils.helpers import create_sample_data, ensure_dir
from data.data_source import DataSource
from data.database import DatabaseManager
from features.feature_engine import FeatureEngine
from models.model_manager import ModelManager

# 设置日志
logger = setup_logger()


def test_basic_functionality():
    """测试基本功能"""
    logger.info("开始测试基本功能...")
    
    try:
        # 1. 测试样本数据生成
        logger.info("1. 测试样本数据生成...")
        sample_data = create_sample_data(num_days=50)
        logger.info(f"生成样本数据: {len(sample_data)} 行")
        print(sample_data.head())
        
        # 2. 测试数据库
        logger.info("2. 测试数据库...")
        db = DatabaseManager()
        
        # 插入样本数据
        db.insert_market_data(sample_data)
        
        # 查询数据
        market_data = db.get_market_data('000001', limit=10)
        logger.info(f"从数据库查询到 {len(market_data)} 条数据")
        
        # 3. 测试特征工程
        logger.info("3. 测试特征工程...")
        fe = FeatureEngine()
        
        # 计算特征
        features_data = fe.calculate_features(sample_data, feature_types=['basic'])
        logger.info(f"计算特征完成: {len(features_data.columns)} 列")
        
        # 4. 测试模型（只测试XGBoost二分类，因为它最简单）
        logger.info("4. 测试模型...")
        model_manager = ModelManager()
        
        # 训练模型
        training_result = model_manager.train_model('xgboost_next_day_direction', features_data)
        logger.info(f"模型训练完成，验证AUC: {training_result['validation_metrics'].get('auc', 'N/A')}")
        
        # 预测
        predictions = model_manager.predict('xgboost_next_day_direction', features_data.head(10))
        logger.info(f"预测完成: {len(predictions)} 行")
        
        # 显示预测结果
        pred_cols = [col for col in predictions.columns if col.startswith('prediction_')]
        if pred_cols:
            print("\n预测结果:")
            print(predictions[['trade_date', 'close'] + pred_cols].head())
        
        logger.info("基本功能测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_source():
    """测试数据源（少量数据）"""
    logger.info("开始测试数据源...")
    
    try:
        ds = DataSource()
        
        # 只获取少量测试数据
        logger.info("获取少量股票列表进行测试...")
        
        # 创建模拟数据而不是真实下载
        test_stocks = ['000001', '000002', '600000']
        logger.info(f"使用测试股票代码: {test_stocks}")
        
        # 为每个股票生成少量测试数据
        for stock_code in test_stocks[:1]:  # 只测试第一个
            logger.info(f"为股票 {stock_code} 生成测试数据...")
            sample_data = create_sample_data(num_days=30)
            sample_data['stock_code'] = stock_code
            
            logger.info(f"股票 {stock_code} 测试数据生成完成: {len(sample_data)} 行")
            print(sample_data.head())
            break  # 只测试一个股票
        
        logger.info("数据源测试完成（使用模拟数据）")
        return True
        
    except Exception as e:
        logger.error(f"数据源测试失败: {e}")
        return False


def run_training_pipeline():
    """运行训练流水线"""
    logger.info("开始运行训练流水线...")
    
    try:
        # 1. 准备数据
        logger.info("1. 准备训练数据...")
        training_data = create_sample_data(num_days=200)  # 生成更多数据用于训练
        
        # 2. 计算特征
        logger.info("2. 计算特征...")
        fe = FeatureEngine()
        features_data = fe.calculate_features(training_data)
        
        # 3. 训练所有模型
        logger.info("3. 训练所有模型...")
        model_manager = ModelManager()
        
        # 只训练XGBoost模型（跳过时序模型，因为需要TensorFlow）
        models_to_train = ['xgboost_10d_surge', 'xgboost_next_day_direction']
        
        training_results = {}
        for model_name in models_to_train:
            try:
                logger.info(f"训练模型: {model_name}")
                result = model_manager.train_model(model_name, features_data)
                training_results[model_name] = result
                
                # 保存模型
                model_manager.save_model(model_name)
                
            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")
                training_results[model_name] = {'error': str(e)}
        
        # 4. 显示训练结果
        logger.info("4. 训练结果汇总:")
        for model_name, result in training_results.items():
            if 'error' in result:
                logger.error(f"{model_name}: {result['error']}")
            else:
                metrics = result.get('validation_metrics', {})
                logger.info(f"{model_name}: AUC={metrics.get('auc', 'N/A'):.4f}, "
                          f"Accuracy={metrics.get('accuracy', 'N/A'):.4f}")
        
        logger.info("训练流水线完成！")
        return True
        
    except Exception as e:
        logger.error(f"训练流水线失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_prediction_pipeline():
    """运行预测流水线"""
    logger.info("开始运行预测流水线...")
    
    try:
        # 1. 准备预测数据
        logger.info("1. 准备预测数据...")
        prediction_data = create_sample_data(num_days=30)
        
        # 2. 计算特征
        logger.info("2. 计算特征...")
        fe = FeatureEngine()
        features_data = fe.calculate_features(prediction_data)
        
        # 3. 加载模型并预测
        logger.info("3. 加载模型并预测...")
        model_manager = ModelManager()
        
        # 获取合并预测结果
        try:
            combined_predictions = model_manager.get_combined_predictions(features_data)
            
            # 显示预测结果
            pred_cols = [col for col in combined_predictions.columns if 'prediction' in col]
            if pred_cols:
                logger.info("预测结果:")
                display_cols = ['trade_date', 'stock_code', 'close'] + pred_cols[:5]  # 只显示前5个预测列
                available_cols = [col for col in display_cols if col in combined_predictions.columns]
                print(combined_predictions[available_cols].head(10))
            
        except Exception as e:
            logger.warning(f"合并预测失败，尝试单独预测: {e}")
            
            # 单独预测每个模型
            models_to_predict = ['xgboost_next_day_direction']
            for model_name in models_to_predict:
                try:
                    predictions = model_manager.predict(model_name, features_data)
                    pred_cols = [col for col in predictions.columns if col.startswith('prediction_')]
                    if pred_cols:
                        logger.info(f"{model_name} 预测结果:")
                        print(predictions[['trade_date', 'close'] + pred_cols].head())
                except Exception as e:
                    logger.error(f"模型 {model_name} 预测失败: {e}")
        
        logger.info("预测流水线完成！")
        return True
        
    except Exception as e:
        logger.error(f"预测流水线失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='量化交易系统')
    parser.add_argument('--mode', choices=['test', 'train', 'predict', 'data'], 
                       default='test', help='运行模式')
    
    args = parser.parse_args()
    
    # 确保必要目录存在
    ensure_dir('data')
    ensure_dir('models')
    ensure_dir('results')
    ensure_dir('logs')
    
    logger.info(f"量化交易系统启动，模式: {args.mode}")
    
    success = False
    
    if args.mode == 'test':
        success = test_basic_functionality()
    elif args.mode == 'data':
        success = test_data_source()
    elif args.mode == 'train':
        success = run_training_pipeline()
    elif args.mode == 'predict':
        success = run_prediction_pipeline()
    
    if success:
        logger.info("程序执行成功！")
    else:
        logger.error("程序执行失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
