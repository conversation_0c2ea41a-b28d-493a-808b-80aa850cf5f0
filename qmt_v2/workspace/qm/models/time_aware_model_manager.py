"""
时间感知模型管理器 (Time-Aware Model Manager)
==========================================

本模块实现了时间感知的模型管理功能，支持早盘和尾盘的不同预测模型。

🕐 主要功能:
- 早盘模型（9:35）：基于前一交易日数据预测
- 尾盘模型（14:50）：基于当日盘中数据预测
- 灵活的训练数据配置（交易日数量或日期范围）
- 时间感知的特征工程
- 模型性能对比和选择

📊 模型类型:
1. 早盘预测模型：
   - 基于前一交易日收盘数据
   - 适合隔夜策略和开盘前决策
   - 预测次日开盘后的表现

2. 尾盘预测模型：
   - 基于当日盘中数据
   - 适合日内策略和尾盘决策
   - 预测当日收盘或次日表现

🔧 核心特性:
- 支持按交易日数量或日期范围训练
- 自动数据分割（训练/回测/预测）
- 时间感知的特征工程
- 模型版本管理和对比

🏷️ 模型命名规范:
- 早盘模型：morning_* (如 morning_next_day_direction)
- 尾盘模型：afternoon_* (如 afternoon_next_day_direction)
- 模型文件：保存在 models/time_aware/ 目录下

📝 使用示例:
```python
# 创建时间感知模型管理器
manager = TimeAwareModelManager()

# 训练时间感知模型
results = manager.train_time_aware_models(
    model_names=['xgboost_next_day_direction'],
    stock_codes=['000001', '000002'],
    train_config={'train_days': 252, 'backtest_days': 63},
    train_both_times=True  # 同时训练早盘和尾盘模型
)

# 时间感知预测
predictions = manager.predict_with_time_awareness(
    stock_codes=['000001', '000002'],
    prediction_time='morning',  # 或 'afternoon'
    model_names=['xgboost_next_day_direction']
)

# 获取模型性能对比
performance = manager.get_model_performance_comparison()
```
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Any
from loguru import logger
import joblib
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.model_manager import ModelManager
from data.time_aware_data_manager import TimeAwareDataManager
from features.feature_engine import FeatureEngine


class TimeAwareModelManager:
    """
    时间感知模型管理器
    
    支持早盘和尾盘的不同预测模型，以及灵活的训练数据配置
    """
    
    def __init__(self, 
                 time_data_manager: TimeAwareDataManager = None,
                 feature_engine: FeatureEngine = None,
                 model_manager: ModelManager = None):
        """
        初始化时间感知模型管理器
        
        Args:
            time_data_manager: 时间感知数据管理器
            feature_engine: 特征引擎
            model_manager: 基础模型管理器
        """
        self.time_data_manager = time_data_manager or TimeAwareDataManager()
        self.feature_engine = feature_engine or FeatureEngine()
        self.model_manager = model_manager or ModelManager()
        
        # 模型存储路径
        self.models_dir = "models/time_aware"
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 时间感知模型配置
        self.time_aware_models = {
            'morning': {
                'xgboost_next_day_direction': 'morning_next_day_direction',
                'xgboost_10d_surge': 'morning_10d_surge',
                'time_series_price_prediction': 'morning_price_prediction'
            },
            'afternoon': {
                'xgboost_next_day_direction': 'afternoon_next_day_direction',
                'xgboost_10d_surge': 'afternoon_10d_surge',
                'time_series_price_prediction': 'afternoon_price_prediction'
            }
        }
    
    def train_time_aware_models(self,
                               model_names: List[str],
                               stock_codes: List[str],
                               train_config: Dict,
                               factor_names: List[str] = None,
                               train_both_times: bool = True) -> Dict[str, Any]:
        """
        训练时间感知模型
        
        Args:
            model_names: 要训练的模型名称列表
            stock_codes: 股票代码列表
            train_config: 训练配置
                - train_days: int 或 (start_date, end_date) 训练数据
                - backtest_days: int 回测数据天数
                - prediction_days: int 预测特征计算天数
            factor_names: 量化因子名称列表
            train_both_times: 是否同时训练早盘和尾盘模型
            
        Returns:
            训练结果字典
        """
        results = {}
        
        # 确定要训练的时间点
        time_points = ['morning', 'afternoon'] if train_both_times else ['morning']
        
        for time_point in time_points:
            logger.info(f"开始训练 {time_point} 模型...")
            
            # 获取时间感知的训练数据
            training_data = self.time_data_manager.get_time_aware_training_data(
                stock_codes=stock_codes,
                train_config=train_config,
                prediction_time=time_point
            )
            
            if not training_data['data']:
                logger.error(f"没有获取到 {time_point} 的训练数据")
                continue
            
            # 为每个模型训练时间感知版本
            for model_name in model_names:
                time_aware_model_name = self.time_aware_models[time_point].get(model_name)
                if not time_aware_model_name:
                    logger.warning(f"模型 {model_name} 没有 {time_point} 版本配置")
                    continue
                
                try:
                    # 准备训练数据
                    combined_data = self._combine_stock_data(training_data['data'])
                    
                    if combined_data.empty:
                        logger.error(f"合并后的训练数据为空")
                        continue
                    
                    # 计算特征（包括时间感知特征）
                    features_df = self._calculate_time_aware_features(
                        combined_data, 
                        factor_names, 
                        time_point
                    )
                    
                    if features_df.empty:
                        logger.error(f"特征计算结果为空")
                        continue
                    
                    # 训练模型
                    training_result = self.model_manager.train_model(
                        model_name=model_name,
                        features_df=features_df,
                        factor_names=factor_names
                    )
                    
                    if training_result and training_result.get('success', False):
                        # 保存时间感知模型
                        model_path = os.path.join(self.models_dir, f"{time_aware_model_name}.joblib")
                        
                        # 获取训练好的模型
                        trained_model = self.model_manager.models.get(model_name)
                        if trained_model:
                            # 保存模型和元数据
                            model_metadata = {
                                'model': trained_model,
                                'model_name': time_aware_model_name,
                                'base_model_name': model_name,
                                'time_point': time_point,
                                'train_config': train_config,
                                'factor_names': factor_names,
                                'training_result': training_result,
                                'train_date': datetime.now().isoformat(),
                                'data_summary': training_data['date_split']['summary']
                            }
                            
                            joblib.dump(model_metadata, model_path)
                            logger.info(f"保存时间感知模型: {model_path}")
                            
                            # 记录结果
                            results[time_aware_model_name] = {
                                'success': True,
                                'model_path': model_path,
                                'training_result': training_result,
                                'time_point': time_point,
                                'data_summary': training_data['date_split']['summary']
                            }
                        else:
                            logger.error(f"无法获取训练好的模型: {model_name}")
                            results[time_aware_model_name] = {
                                'success': False,
                                'error': 'Model not found after training'
                            }
                    else:
                        logger.error(f"模型 {model_name} 训练失败")
                        results[time_aware_model_name] = {
                            'success': False,
                            'error': 'Training failed',
                            'training_result': training_result
                        }
                
                except Exception as e:
                    logger.error(f"训练 {time_aware_model_name} 时出错: {e}")
                    results[time_aware_model_name] = {
                        'success': False,
                        'error': str(e)
                    }
        
        return results
    
    def _combine_stock_data(self, stock_data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        合并多只股票的数据
        
        Args:
            stock_data_dict: 股票数据字典
            
        Returns:
            合并后的数据框
        """
        combined_data = []
        
        for stock_code, data in stock_data_dict.items():
            if not data.empty:
                data_copy = data.copy()
                data_copy['stock_code'] = stock_code
                combined_data.append(data_copy)
        
        if combined_data:
            return pd.concat(combined_data, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _calculate_time_aware_features(self, 
                                     data: pd.DataFrame, 
                                     factor_names: List[str] = None,
                                     time_point: str = 'morning') -> pd.DataFrame:
        """
        计算时间感知特征
        
        Args:
            data: 原始数据
            factor_names: 因子名称列表
            time_point: 时间点
            
        Returns:
            包含特征的数据框
        """
        try:
            # 基础特征计算
            features_df = self.feature_engine.calculate_features(
                data=data,
                feature_types=['basic', 'technical', 'quantitative'],
                factor_names=factor_names
            )
            
            # 添加时间感知特征
            if time_point == 'morning':
                # 早盘特征：更多依赖前一日数据
                features_df = self._add_morning_features(features_df)
            elif time_point == 'afternoon':
                # 尾盘特征：可以使用当日盘中数据
                features_df = self._add_afternoon_features(features_df)
            
            return features_df
            
        except Exception as e:
            logger.error(f"计算时间感知特征失败: {e}")
            return pd.DataFrame()
    
    def _add_morning_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        添加早盘特征
        
        早盘预测主要基于前一交易日的数据
        """
        if 'close' in features_df.columns:
            # 隔夜跳空特征
            features_df['overnight_gap'] = (features_df['open'] - features_df['close'].shift(1)) / features_df['close'].shift(1)
            
            # 前一日强度特征
            features_df['prev_day_strength'] = (features_df['close'].shift(1) - features_df['open'].shift(1)) / features_df['open'].shift(1)
            
            # 前一日成交量相对强度
            if 'volume' in features_df.columns:
                features_df['prev_volume_ratio'] = features_df['volume'].shift(1) / features_df['volume'].shift(1).rolling(5).mean()
        
        # 添加时间标识
        features_df['prediction_time'] = 'morning'
        
        return features_df
    
    def _add_afternoon_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        添加尾盘特征
        
        尾盘预测可以使用当日盘中数据
        """
        if 'close' in features_df.columns and 'open' in features_df.columns:
            # 当日涨跌幅
            features_df['intraday_return'] = (features_df['close'] - features_df['open']) / features_df['open']
            
            # 当日振幅
            if 'high' in features_df.columns and 'low' in features_df.columns:
                features_df['intraday_amplitude'] = (features_df['high'] - features_df['low']) / features_df['open']
            
            # 当日相对成交量
            if 'volume' in features_df.columns:
                features_df['current_volume_ratio'] = features_df['volume'] / features_df['volume'].rolling(5).mean()
        
        # 添加时间标识
        features_df['prediction_time'] = 'afternoon'
        
        return features_df
    
    def predict_with_time_awareness(self,
                                  stock_codes: List[str],
                                  prediction_time: str = 'morning',
                                  model_names: List[str] = None,
                                  prediction_days: int = 20) -> Dict[str, Any]:
        """
        使用时间感知模型进行预测
        
        Args:
            stock_codes: 股票代码列表
            prediction_time: 预测时间点 ('morning' 或 'afternoon')
            model_names: 模型名称列表
            prediction_days: 预测用的历史数据天数
            
        Returns:
            预测结果字典
        """
        if model_names is None:
            model_names = ['xgboost_next_day_direction', 'xgboost_10d_surge']
        
        # 获取预测数据
        prediction_data = self.time_data_manager.get_prediction_data(
            stock_codes=stock_codes,
            prediction_days=prediction_days,
            prediction_time=prediction_time
        )
        
        if not prediction_data:
            logger.error("没有获取到预测数据")
            return {}
        
        # 合并数据
        combined_data = self._combine_stock_data(prediction_data)
        if combined_data.empty:
            logger.error("合并后的预测数据为空")
            return {}
        
        predictions = {}
        
        for model_name in model_names:
            time_aware_model_name = self.time_aware_models[prediction_time].get(model_name)
            if not time_aware_model_name:
                logger.warning(f"模型 {model_name} 没有 {prediction_time} 版本")
                continue
            
            try:
                # 加载时间感知模型
                model_path = os.path.join(self.models_dir, f"{time_aware_model_name}.joblib")
                
                if not os.path.exists(model_path):
                    logger.warning(f"时间感知模型不存在: {model_path}")
                    continue
                
                model_metadata = joblib.load(model_path)
                model = model_metadata['model']
                factor_names = model_metadata.get('factor_names', [])
                
                # 计算特征
                features_df = self._calculate_time_aware_features(
                    combined_data, 
                    factor_names, 
                    prediction_time
                )
                
                if features_df.empty:
                    logger.error(f"特征计算结果为空")
                    continue
                
                # 执行预测
                prediction_result = self.model_manager.predict_with_model(
                    model_name=model_name,
                    features_df=features_df,
                    model=model
                )
                
                if prediction_result:
                    predictions[time_aware_model_name] = {
                        'predictions': prediction_result,
                        'model_metadata': model_metadata,
                        'prediction_time': prediction_time,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    logger.info(f"使用 {time_aware_model_name} 完成预测")
                
            except Exception as e:
                logger.error(f"使用 {time_aware_model_name} 预测时出错: {e}")
        
        return predictions
    
    def get_model_performance_comparison(self) -> Dict[str, Any]:
        """
        获取模型性能对比
        
        Returns:
            模型性能对比结果
        """
        comparison = {
            'morning_models': {},
            'afternoon_models': {},
            'comparison_summary': {}
        }
        
        for time_point in ['morning', 'afternoon']:
            for base_model, time_aware_model in self.time_aware_models[time_point].items():
                model_path = os.path.join(self.models_dir, f"{time_aware_model}.joblib")
                
                if os.path.exists(model_path):
                    try:
                        model_metadata = joblib.load(model_path)
                        training_result = model_metadata.get('training_result', {})
                        
                        comparison[f'{time_point}_models'][time_aware_model] = {
                            'base_model': base_model,
                            'metrics': training_result.get('validation_metrics', {}),
                            'train_date': model_metadata.get('train_date'),
                            'data_summary': model_metadata.get('data_summary', {})
                        }
                        
                    except Exception as e:
                        logger.error(f"加载模型元数据失败 {model_path}: {e}")
        
        return comparison


def test_time_aware_model_manager():
    """测试时间感知模型管理器"""
    logger.info("测试时间感知模型管理器")
    
    # 创建管理器
    manager = TimeAwareModelManager()
    
    # 测试配置
    stock_codes = ['000001', '000002']
    train_config = {
        'train_days': 100,
        'backtest_days': 30,
        'prediction_days': 20
    }
    
    # 测试训练
    logger.info("测试时间感知模型训练")
    training_results = manager.train_time_aware_models(
        model_names=['xgboost_next_day_direction'],
        stock_codes=stock_codes,
        train_config=train_config,
        factor_names=['momentum_10d', 'volume_ratio'],
        train_both_times=True
    )
    
    print(f"训练结果: {list(training_results.keys())}")
    
    # 测试预测
    logger.info("测试时间感知预测")
    morning_predictions = manager.predict_with_time_awareness(
        stock_codes=stock_codes,
        prediction_time='morning',
        model_names=['xgboost_next_day_direction']
    )
    
    print(f"早盘预测结果: {list(morning_predictions.keys())}")
    
    # 测试性能对比
    logger.info("测试模型性能对比")
    performance = manager.get_model_performance_comparison()
    print(f"性能对比: {list(performance.keys())}")
    
    logger.info("时间感知模型管理器测试完成")


if __name__ == "__main__":
    test_time_aware_model_manager()
