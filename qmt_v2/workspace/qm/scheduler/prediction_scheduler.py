"""
预测定时任务调度器
"""
import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any
from loguru import logger
import pandas as pd

from data.data_source import DataSource
from features.feature_engine import FeatureEngine
from models.model_manager import ModelManager
from data.database import DatabaseManager
from utils.helpers import create_sample_data, is_trading_day


class PredictionScheduler:
    """预测定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.data_source = DataSource()
        self.feature_engine = FeatureEngine()
        self.model_manager = ModelManager()
        self.database = DatabaseManager()
        
        self.is_running = False
        self.scheduler_thread = None
        
        # 预测时间配置
        self.prediction_times = ["09:35", "14:50"]
        
        # 股票池配置
        self.stock_pool = ['000001', '000002', '600000', '600036']  # 测试用少量股票
        
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置定时预测任务...")
        
        # 清除现有任务
        schedule.clear()
        
        # 设置预测任务
        for time_str in self.prediction_times:
            schedule.every().monday.at(time_str).do(self.run_prediction_job)
            schedule.every().tuesday.at(time_str).do(self.run_prediction_job)
            schedule.every().wednesday.at(time_str).do(self.run_prediction_job)
            schedule.every().thursday.at(time_str).do(self.run_prediction_job)
            schedule.every().friday.at(time_str).do(self.run_prediction_job)
            
            logger.info(f"已设置定时任务: 每个交易日 {time_str}")
        
        # 设置每日数据更新任务（早上8点）
        schedule.every().monday.at("08:00").do(self.update_data_job)
        schedule.every().tuesday.at("08:00").do(self.update_data_job)
        schedule.every().wednesday.at("08:00").do(self.update_data_job)
        schedule.every().thursday.at("08:00").do(self.update_data_job)
        schedule.every().friday.at("08:00").do(self.update_data_job)
        
        logger.info("定时任务设置完成")
    
    def run_prediction_job(self):
        """执行预测任务"""
        try:
            current_time = datetime.now()
            logger.info(f"开始执行预测任务: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 检查是否为交易日
            if not is_trading_day(current_time):
                logger.info("今日非交易日，跳过预测任务")
                return
            
            # 获取预测数据
            prediction_results = []
            
            for stock_code in self.stock_pool:
                try:
                    logger.info(f"为股票 {stock_code} 执行预测...")
                    
                    # 获取股票数据（这里使用模拟数据，实际应该从adata获取）
                    stock_data = create_sample_data(num_days=60)
                    stock_data['stock_code'] = stock_code
                    
                    # 计算特征
                    features_data = self.feature_engine.calculate_features(stock_data)
                    
                    # 执行预测
                    predictions = self.model_manager.get_combined_predictions(features_data.tail(1))
                    
                    if not predictions.empty:
                        # 提取预测结果
                        latest_prediction = predictions.iloc[-1]
                        
                        # 保存预测结果到数据库
                        for col in predictions.columns:
                            if 'prediction' in col:
                                prediction_record = {
                                    'stock_code': stock_code,
                                    'model_name': col.split('_')[0] + '_' + col.split('_')[1],
                                    'prediction_date': current_time.strftime('%Y-%m-%d'),
                                    'prediction_time': current_time.strftime('%H:%M:%S'),
                                    'target_date': (current_time + timedelta(days=1)).strftime('%Y-%m-%d'),
                                    'prediction_value': latest_prediction[col],
                                    'confidence': latest_prediction.get(col.replace('prediction', 'confidence'), 0.5)
                                }
                                
                                self.database.insert_prediction(prediction_record)
                        
                        prediction_results.append({
                            'stock_code': stock_code,
                            'predictions': latest_prediction.to_dict()
                        })
                        
                        logger.info(f"股票 {stock_code} 预测完成")
                    
                except Exception as e:
                    logger.error(f"股票 {stock_code} 预测失败: {e}")
                    continue
            
            logger.info(f"预测任务完成，共处理 {len(prediction_results)} 只股票")
            
            # 生成预测报告
            self.generate_prediction_report(prediction_results, current_time)
            
        except Exception as e:
            logger.error(f"预测任务执行失败: {e}")
    
    def update_data_job(self):
        """更新数据任务"""
        try:
            current_time = datetime.now()
            logger.info(f"开始执行数据更新任务: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 检查是否为交易日
            if not is_trading_day(current_time):
                logger.info("今日非交易日，跳过数据更新任务")
                return
            
            # 更新股票数据（这里使用模拟数据）
            for stock_code in self.stock_pool:
                try:
                    logger.info(f"更新股票 {stock_code} 数据...")
                    
                    # 生成最新数据（实际应该从adata获取）
                    latest_data = create_sample_data(num_days=1)
                    latest_data['stock_code'] = stock_code
                    latest_data['trade_date'] = current_time.strftime('%Y-%m-%d')
                    
                    # 保存到数据库
                    self.database.insert_market_data(latest_data)
                    
                    logger.info(f"股票 {stock_code} 数据更新完成")
                    
                except Exception as e:
                    logger.error(f"更新股票 {stock_code} 数据失败: {e}")
                    continue
            
            logger.info("数据更新任务完成")
            
        except Exception as e:
            logger.error(f"数据更新任务执行失败: {e}")
    
    def generate_prediction_report(self, prediction_results: List[Dict], timestamp: datetime):
        """生成预测报告"""
        try:
            logger.info("生成预测报告...")
            
            report_data = []
            for result in prediction_results:
                stock_code = result['stock_code']
                predictions = result['predictions']
                
                # 提取主要预测结果
                surge_prob = predictions.get('xgboost_10d_surge_prediction_10d_surge_prob', 0)
                direction_prob = predictions.get('xgboost_next_day_direction_prediction_next_day_prob', 0)
                direction = predictions.get('xgboost_next_day_direction_prediction_next_day_direction', 0)
                
                report_data.append({
                    'stock_code': stock_code,
                    '10日拉升概率': f"{surge_prob:.3f}",
                    '明日上涨概率': f"{direction_prob:.3f}",
                    '明日方向预测': "上涨" if direction == 1 else "下跌",
                    '预测时间': timestamp.strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 创建报告DataFrame
            report_df = pd.DataFrame(report_data)
            
            # 保存报告
            report_file = f"results/prediction_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.csv"
            report_df.to_csv(report_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"预测报告已保存: {report_file}")
            
            # 显示报告摘要
            logger.info("预测报告摘要:")
            print(report_df.to_string(index=False))
            
        except Exception as e:
            logger.error(f"生成预测报告失败: {e}")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        logger.info("启动预测调度器...")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("预测调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        logger.info("停止预测调度器...")
        
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("预测调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("调度器主循环开始...")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
        
        logger.info("调度器主循环结束")
    
    def run_manual_prediction(self):
        """手动执行一次预测"""
        logger.info("手动执行预测任务...")
        self.run_prediction_job()
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'prediction_times': self.prediction_times,
            'stock_pool': self.stock_pool,
            'next_jobs': [str(job) for job in schedule.jobs],
            'model_status': self.model_manager.get_model_status()
        }


# 测试函数
def test_scheduler():
    """测试调度器"""
    logger.info("开始测试预测调度器...")
    
    scheduler = PredictionScheduler()
    
    # 获取状态
    status = scheduler.get_status()
    logger.info(f"调度器状态: {status}")
    
    # 手动执行一次预测
    scheduler.run_manual_prediction()
    
    logger.info("调度器测试完成")


if __name__ == "__main__":
    test_scheduler()
