"""
时间感知调度器 (Time-Aware Scheduler)
===================================

本模块实现了时间感知的预测调度功能，支持早盘和尾盘的定时预测。

🕐 主要功能:
- 早盘预测调度（9:35）：基于前一交易日数据
- 尾盘预测调度（14:50）：基于当日盘中数据
- 智能交易日检测：自动跳过非交易日
- 预测结果存储和管理
- 异常处理和重试机制

⏰ 调度策略:
1. 早盘调度（9:35）：
   - 检查是否为交易日
   - 使用早盘模型进行预测
   - 基于前一交易日收盘数据
   - 适合开盘前决策

2. 尾盘调度（14:50）：
   - 检查是否为交易日
   - 使用尾盘模型进行预测
   - 基于当日盘中数据
   - 适合尾盘决策

🔧 核心特性:
- 自动交易日检测
- 模型健康检查
- 预测结果验证
- 异常恢复机制
- 性能监控

📊 数据库表结构:
- time_aware_predictions: 存储时间感知预测结果
- scheduler_logs: 记录调度器运行日志

📝 使用示例:
```python
# 创建时间感知调度器
scheduler = TimeAwareScheduler()

# 启动调度器（自动在9:35和14:50执行预测）
scheduler.start_scheduler()

# 手动执行预测
result = scheduler.manual_prediction('morning')  # 或 'afternoon'

# 获取调度器状态
status = scheduler.get_scheduler_status()

# 停止调度器
scheduler.stop_scheduler()
```

🚨 注意事项:
- 调度器会自动检测交易日，非交易日不执行预测
- 预测结果会自动保存到数据库
- 支持异常处理和自动重试
- 可以通过配置文件调整调度时间和参数

"""

import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import yaml
import os
import sys
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.time_aware_model_manager import TimeAwareModelManager
from data.time_aware_data_manager import TimeAwareDataManager
from data.database import DatabaseManager


class TimeAwareScheduler:
    """
    时间感知调度器
    
    支持早盘和尾盘的定时预测调度
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化时间感知调度器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.model_manager = TimeAwareModelManager()
        self.data_manager = TimeAwareDataManager()
        self.db_manager = DatabaseManager()
        
        # 调度状态
        self.is_running = False
        self.scheduler_thread = None
        
        # 预测历史
        self.prediction_history = []
        
        # 初始化数据库表
        self._init_prediction_tables()
        
        logger.info("时间感知调度器初始化完成")
    
    def _load_config(self, config_path: str = None) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = "config/time_aware_training.yaml"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'prediction_config': {
                'morning_prediction': {
                    'enabled': True,
                    'time': '09:35',
                    'models': ['morning_next_day_direction'],
                    'prediction_days': 20,
                    'stock_pool': 'default'
                },
                'afternoon_prediction': {
                    'enabled': True,
                    'time': '14:50',
                    'models': ['afternoon_next_day_direction'],
                    'prediction_days': 20,
                    'stock_pool': 'default'
                }
            },
            'data_config': {
                'stock_pool': {
                    'default': ['000001', '000002', '600000']
                }
            }
        }
    
    def _init_prediction_tables(self):
        """初始化预测结果表"""
        try:
            # 创建预测结果表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS time_aware_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                prediction_time TEXT NOT NULL,
                prediction_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                model_name TEXT NOT NULL,
                prediction_value REAL,
                confidence REAL,
                features TEXT,
                created_at TEXT NOT NULL,
                trade_date TEXT NOT NULL
            )
            """
            
            self.db_manager.execute_query(create_table_sql)
            
            # 创建调度日志表
            create_log_table_sql = """
            CREATE TABLE IF NOT EXISTS scheduler_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_time TEXT NOT NULL,
                log_type TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                created_at TEXT NOT NULL
            )
            """
            
            self.db_manager.execute_query(create_log_table_sql)
            
            logger.info("预测结果表初始化完成")
            
        except Exception as e:
            logger.error(f"初始化预测结果表失败: {e}")
    
    def is_trading_day(self, date: datetime = None) -> bool:
        """
        检查是否为交易日
        
        Args:
            date: 检查的日期，默认为今天
            
        Returns:
            是否为交易日
        """
        if date is None:
            date = datetime.now()
        
        # 检查是否为周末
        if date.weekday() >= 5:  # 周六=5, 周日=6
            return False
        
        # 简化版节假日检查（实际应用中应使用完整的交易日历）
        date_str = date.strftime('%Y-%m-%d')
        
        # 主要节假日列表（简化版）
        holidays_2024 = [
            '2024-01-01', '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', 
            '2024-02-14', '2024-02-15', '2024-02-16', '2024-02-17',
            '2024-04-04', '2024-04-05', '2024-04-06',
            '2024-05-01', '2024-05-02', '2024-05-03',
            '2024-06-10', '2024-09-15', '2024-09-16', '2024-09-17',
            '2024-10-01', '2024-10-02', '2024-10-03', '2024-10-04', '2024-10-07'
        ]
        
        return date_str not in holidays_2024
    
    def morning_prediction_job(self):
        """早盘预测任务（9:35）"""
        try:
            current_time = datetime.now()
            
            # 检查是否为交易日
            if not self.is_trading_day(current_time):
                logger.info(f"今日非交易日，跳过早盘预测")
                self._log_scheduler_event("SKIP", "非交易日，跳过早盘预测")
                return
            
            logger.info("开始执行早盘预测任务")
            self._log_scheduler_event("START", "开始早盘预测")
            
            # 获取配置
            morning_config = self.config['prediction_config']['morning_prediction']
            
            if not morning_config.get('enabled', True):
                logger.info("早盘预测已禁用")
                return
            
            # 获取股票池
            stock_pool_name = morning_config.get('stock_pool', 'default')
            stock_codes = self.config['data_config']['stock_pool'].get(stock_pool_name, ['000001', '000002'])
            
            # 执行预测
            predictions = self.model_manager.predict_with_time_awareness(
                stock_codes=stock_codes,
                prediction_time='morning',
                model_names=morning_config.get('models', ['morning_next_day_direction']),
                prediction_days=morning_config.get('prediction_days', 20)
            )
            
            # 保存预测结果
            if predictions:
                self._save_predictions(predictions, 'morning', current_time)
                logger.info(f"早盘预测完成，预测 {len(stock_codes)} 只股票")
                self._log_scheduler_event("SUCCESS", f"早盘预测完成，预测 {len(stock_codes)} 只股票")
            else:
                logger.warning("早盘预测未产生结果")
                self._log_scheduler_event("WARNING", "早盘预测未产生结果")
            
        except Exception as e:
            logger.error(f"早盘预测任务执行失败: {e}")
            self._log_scheduler_event("ERROR", f"早盘预测失败: {str(e)}")
    
    def afternoon_prediction_job(self):
        """尾盘预测任务（14:50）"""
        try:
            current_time = datetime.now()
            
            # 检查是否为交易日
            if not self.is_trading_day(current_time):
                logger.info(f"今日非交易日，跳过尾盘预测")
                self._log_scheduler_event("SKIP", "非交易日，跳过尾盘预测")
                return
            
            logger.info("开始执行尾盘预测任务")
            self._log_scheduler_event("START", "开始尾盘预测")
            
            # 获取配置
            afternoon_config = self.config['prediction_config']['afternoon_prediction']
            
            if not afternoon_config.get('enabled', True):
                logger.info("尾盘预测已禁用")
                return
            
            # 获取股票池
            stock_pool_name = afternoon_config.get('stock_pool', 'default')
            stock_codes = self.config['data_config']['stock_pool'].get(stock_pool_name, ['000001', '000002'])
            
            # 执行预测
            predictions = self.model_manager.predict_with_time_awareness(
                stock_codes=stock_codes,
                prediction_time='afternoon',
                model_names=afternoon_config.get('models', ['afternoon_next_day_direction']),
                prediction_days=afternoon_config.get('prediction_days', 20)
            )
            
            # 保存预测结果
            if predictions:
                self._save_predictions(predictions, 'afternoon', current_time)
                logger.info(f"尾盘预测完成，预测 {len(stock_codes)} 只股票")
                self._log_scheduler_event("SUCCESS", f"尾盘预测完成，预测 {len(stock_codes)} 只股票")
            else:
                logger.warning("尾盘预测未产生结果")
                self._log_scheduler_event("WARNING", "尾盘预测未产生结果")
            
        except Exception as e:
            logger.error(f"尾盘预测任务执行失败: {e}")
            self._log_scheduler_event("ERROR", f"尾盘预测失败: {str(e)}")
    
    def _save_predictions(self, predictions: Dict[str, Any], prediction_type: str, prediction_time: datetime):
        """
        保存预测结果
        
        Args:
            predictions: 预测结果字典
            prediction_type: 预测类型 ('morning' 或 'afternoon')
            prediction_time: 预测时间
        """
        try:
            trade_date = prediction_time.strftime('%Y-%m-%d')
            
            for model_name, model_predictions in predictions.items():
                prediction_results = model_predictions.get('predictions', {})
                
                for stock_code, result in prediction_results.items():
                    # 插入预测结果
                    insert_sql = """
                    INSERT INTO time_aware_predictions 
                    (prediction_time, prediction_type, stock_code, model_name, 
                     prediction_value, confidence, features, created_at, trade_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    values = (
                        prediction_time.strftime('%H:%M:%S'),
                        prediction_type,
                        stock_code,
                        model_name,
                        result.get('prediction', 0),
                        result.get('confidence', 0),
                        str(result.get('features', {})),
                        datetime.now().isoformat(),
                        trade_date
                    )
                    
                    self.db_manager.execute_query(insert_sql, values)
            
            logger.info(f"保存 {prediction_type} 预测结果到数据库")
            
        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
    
    def _log_scheduler_event(self, log_type: str, message: str, details: str = None):
        """
        记录调度事件
        
        Args:
            log_type: 日志类型
            message: 日志消息
            details: 详细信息
        """
        try:
            insert_sql = """
            INSERT INTO scheduler_logs (log_time, log_type, message, details, created_at)
            VALUES (?, ?, ?, ?, ?)
            """
            
            values = (
                datetime.now().strftime('%H:%M:%S'),
                log_type,
                message,
                details,
                datetime.now().isoformat()
            )
            
            self.db_manager.execute_query(insert_sql, values)
            
        except Exception as e:
            logger.error(f"记录调度事件失败: {e}")
    
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        try:
            # 清除之前的调度
            schedule.clear()
            
            # 设置早盘预测调度
            morning_time = self.config['prediction_config']['morning_prediction'].get('time', '09:35')
            schedule.every().day.at(morning_time).do(self.morning_prediction_job)
            logger.info(f"设置早盘预测调度: {morning_time}")
            
            # 设置尾盘预测调度
            afternoon_time = self.config['prediction_config']['afternoon_prediction'].get('time', '14:50')
            schedule.every().day.at(afternoon_time).do(self.afternoon_prediction_job)
            logger.info(f"设置尾盘预测调度: {afternoon_time}")
            
            # 启动调度线程
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            logger.info("时间感知调度器启动成功")
            self._log_scheduler_event("START", "调度器启动成功")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            self.is_running = False
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        try:
            self.is_running = False
            schedule.clear()
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            logger.info("时间感知调度器停止成功")
            self._log_scheduler_event("STOP", "调度器停止成功")
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("调度器主循环开始")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(5)  # 异常后等待5秒再继续
        
        logger.info("调度器主循环结束")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'scheduled_jobs': len(schedule.jobs),
            'next_morning_run': self._get_next_run_time('09:35'),
            'next_afternoon_run': self._get_next_run_time('14:50'),
            'last_predictions': self._get_recent_predictions(5)
        }
    
    def _get_next_run_time(self, time_str: str) -> str:
        """获取下次运行时间"""
        try:
            for job in schedule.jobs:
                if job.at_time.strftime('%H:%M') == time_str:
                    return job.next_run.isoformat() if job.next_run else "未设置"
            return "未找到"
        except:
            return "未知"
    
    def _get_recent_predictions(self, limit: int = 5) -> List[Dict]:
        """获取最近的预测记录"""
        try:
            query_sql = """
            SELECT prediction_time, prediction_type, stock_code, model_name, 
                   prediction_value, confidence, trade_date
            FROM time_aware_predictions 
            ORDER BY created_at DESC 
            LIMIT ?
            """
            
            results = self.db_manager.fetch_all(query_sql, (limit,))
            
            return [
                {
                    'prediction_time': row[0],
                    'prediction_type': row[1],
                    'stock_code': row[2],
                    'model_name': row[3],
                    'prediction_value': row[4],
                    'confidence': row[5],
                    'trade_date': row[6]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取最近预测记录失败: {e}")
            return []
    
    def manual_prediction(self, prediction_type: str = 'morning') -> Dict[str, Any]:
        """
        手动执行预测
        
        Args:
            prediction_type: 预测类型 ('morning' 或 'afternoon')
            
        Returns:
            预测结果
        """
        try:
            logger.info(f"手动执行 {prediction_type} 预测")
            
            if prediction_type == 'morning':
                self.morning_prediction_job()
            elif prediction_type == 'afternoon':
                self.afternoon_prediction_job()
            else:
                raise ValueError(f"不支持的预测类型: {prediction_type}")
            
            return {'success': True, 'message': f'{prediction_type} 预测执行完成'}
            
        except Exception as e:
            logger.error(f"手动预测执行失败: {e}")
            return {'success': False, 'error': str(e)}


def test_time_aware_scheduler():
    """测试时间感知调度器"""
    logger.info("测试时间感知调度器")
    
    # 创建调度器
    scheduler = TimeAwareScheduler()
    
    # 测试交易日检查
    logger.info("测试交易日检查")
    today = datetime.now()
    is_trading = scheduler.is_trading_day(today)
    print(f"今日是否为交易日: {is_trading}")
    
    # 测试手动预测
    logger.info("测试手动预测")
    result = scheduler.manual_prediction('morning')
    print(f"手动预测结果: {result}")
    
    # 测试调度器状态
    logger.info("测试调度器状态")
    status = scheduler.get_scheduler_status()
    print(f"调度器状态: {status}")
    
    logger.info("时间感知调度器测试完成")


if __name__ == "__main__":
    test_time_aware_scheduler()
