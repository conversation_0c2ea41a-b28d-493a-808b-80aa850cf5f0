#!/usr/bin/env python3
"""
增强量化交易系统测试脚本
测试新增的量化因子、回测功能和Web界面
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入系统组件
from data.stock_data_manager import StockDataManager
from features.quantitative_factors import QuantitativeFactors
from features.feature_engine import FeatureEngine
from models.model_manager import ModelManager
from backtest.backtest_engine import BacktestEngine
from utils.helpers import create_sample_data


def test_stock_data_manager():
    """测试股票数据管理器"""
    logger.info("=" * 50)
    logger.info("测试股票数据管理器")
    logger.info("=" * 50)
    
    try:
        # 创建数据管理器
        data_manager = StockDataManager()
        
        # 获取股票池（测试用，只获取5只）
        stock_codes = data_manager.get_stock_universe(limit=5)
        logger.info(f"获取股票池: {stock_codes}")
        
        # 获取数据概览
        summary = data_manager.get_data_summary()
        logger.info(f"数据概览: {summary}")
        
        logger.info("✅ 股票数据管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 股票数据管理器测试失败: {e}")
        return False


def test_quantitative_factors():
    """测试量化因子"""
    logger.info("=" * 50)
    logger.info("测试量化因子")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_data = create_sample_data(num_days=100)
        
        # 创建量化因子计算器
        qf = QuantitativeFactors()
        
        # 测试单个因子计算
        momentum_factor = qf.calculate_factor(test_data, 'momentum_10d')
        logger.info(f"动量因子计算结果: {momentum_factor.describe()}")
        
        # 测试批量因子计算
        factors_df = qf.calculate_factors(test_data, categories=['momentum', 'volume'])
        factor_columns = [col for col in factors_df.columns if col.startswith('factor_')]
        logger.info(f"计算的因子: {factor_columns}")
        
        # 测试因子重要性
        factors_df['target'] = factors_df['close'].shift(-1)
        importance_df = qf.get_factor_importance(factors_df.dropna(), 'target')
        if not importance_df.empty:
            logger.info(f"因子重要性前5: {importance_df.head()['factor'].tolist()}")
        
        logger.info("✅ 量化因子测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 量化因子测试失败: {e}")
        return False


def test_enhanced_feature_engine():
    """测试增强的特征引擎"""
    logger.info("=" * 50)
    logger.info("测试增强的特征引擎")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_data = create_sample_data(num_days=100)
        
        # 创建特征引擎
        feature_engine = FeatureEngine()
        
        # 测试包含量化因子的特征计算
        features_df = feature_engine.calculate_features(
            test_data, 
            feature_types=['basic', 'technical', 'quantitative'],
            factor_categories=['momentum', 'volume']
        )
        
        logger.info(f"特征数据形状: {features_df.shape}")
        
        # 测试因子管理功能
        available_factors = feature_engine.get_available_factors()
        logger.info(f"可用因子分类: {list(available_factors.keys())}")
        
        # 设置因子组合
        test_factors = ['momentum_10d', 'volume_ratio', 'bollinger_position']
        feature_engine.set_factor_combination(test_factors, 'test_combination')
        
        enabled_factors = feature_engine.get_enabled_factors()
        logger.info(f"启用的因子: {enabled_factors}")
        
        logger.info("✅ 增强特征引擎测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强特征引擎测试失败: {e}")
        return False


def test_model_manager_with_factors():
    """测试带因子管理的模型管理器"""
    logger.info("=" * 50)
    logger.info("测试带因子管理的模型管理器")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_data = create_sample_data(num_days=200)
        
        # 创建特征引擎和模型管理器
        feature_engine = FeatureEngine()
        model_manager = ModelManager()
        
        # 计算特征（包括量化因子）
        features_df = feature_engine.calculate_features(
            test_data, 
            feature_types=['basic', 'technical', 'quantitative'],
            factor_names=['momentum_10d', 'volume_ratio', 'bollinger_position']
        )
        
        # 训练模型（带因子信息）
        factor_names = ['momentum_10d', 'volume_ratio', 'bollinger_position']
        training_result = model_manager.train_model(
            'xgboost_next_day_direction', 
            features_df,
            factor_names=factor_names
        )
        
        logger.info(f"训练结果: {training_result.get('validation_metrics', {})}")
        
        # 测试因子组合管理
        current_factors = model_manager.get_current_factor_combination('xgboost_next_day_direction')
        logger.info(f"当前因子组合: {current_factors}")
        
        # 测试因子推荐
        all_factors = []
        for factors in feature_engine.get_available_factors().values():
            all_factors.extend(factors)
        
        recommended = model_manager.recommend_factor_combination(
            'xgboost_next_day_direction', 
            all_factors, 
            max_factors=10
        )
        logger.info(f"推荐因子: {recommended[:5]}...")
        
        logger.info("✅ 带因子管理的模型管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 带因子管理的模型管理器测试失败: {e}")
        return False


def test_backtest_engine():
    """测试回测引擎"""
    logger.info("=" * 50)
    logger.info("测试回测引擎")
    logger.info("=" * 50)
    
    try:
        # 创建回测引擎
        backtest_engine = BacktestEngine()
        
        # 生成测试数据
        stock_codes = ['000001', '000002', '600000']
        start_date = '2023-01-01'
        end_date = '2023-06-30'
        
        # 生成价格数据
        price_data = {}
        for stock_code in stock_codes:
            dates = pd.date_range(start_date, end_date, freq='D')
            np.random.seed(hash(stock_code) % 1000)
            prices = 10 + np.cumsum(np.random.randn(len(dates)) * 0.02)
            
            price_data[stock_code] = pd.DataFrame({
                'trade_date': dates,
                'open': prices * (1 + np.random.normal(0, 0.01, len(dates))),
                'close': prices,
                'high': prices * (1 + np.random.uniform(0, 0.03, len(dates))),
                'low': prices * (1 - np.random.uniform(0, 0.03, len(dates))),
                'volume': np.random.randint(1000000, 10000000, len(dates))
            })
        
        # 生成交易信号
        signals = []
        for date in pd.date_range(start_date, end_date, freq='5D'):
            for stock_code in stock_codes:
                if np.random.random() > 0.7:
                    signal = {
                        'trade_date': date,
                        'stock_code': stock_code,
                        'signal': np.random.choice([1, -1]),
                        'confidence': np.random.uniform(0.6, 0.9)
                    }
                    signals.append(signal)
        
        signals_df = pd.DataFrame(signals)
        
        # 运行回测
        backtest_results = backtest_engine.run_backtest(
            signals_df=signals_df,
            price_data=price_data,
            start_date=start_date,
            end_date=end_date
        )
        
        if backtest_results:
            basic_metrics = backtest_results['basic_metrics']
            trading_metrics = backtest_results['trading_metrics']
            
            logger.info(f"总收益率: {basic_metrics['total_return']:.2f}%")
            logger.info(f"年化收益率: {basic_metrics['annual_return']:.2f}%")
            logger.info(f"最大回撤: {basic_metrics['max_drawdown']:.2f}%")
            logger.info(f"夏普比率: {basic_metrics['sharpe_ratio']:.2f}")
            logger.info(f"胜率: {trading_metrics['win_rate']:.2f}%")
            logger.info(f"总交易次数: {trading_metrics['total_trades']}")
        
        logger.info("✅ 回测引擎测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 回测引擎测试失败: {e}")
        return False


def test_web_api_endpoints():
    """测试Web API接口"""
    logger.info("=" * 50)
    logger.info("测试Web API接口")
    logger.info("=" * 50)
    
    try:
        import requests
        import time
        
        # 启动Web服务器（在后台）
        logger.info("请确保Web服务器已启动 (python web/app.py)")
        
        base_url = "http://127.0.0.1:5001/api"
        
        # 测试系统状态接口
        try:
            response = requests.get(f"{base_url}/status", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 系统状态接口正常")
            else:
                logger.warning(f"⚠️ 系统状态接口返回: {response.status_code}")
        except requests.exceptions.RequestException:
            logger.warning("⚠️ 无法连接到Web服务器，请手动启动")
        
        # 测试因子接口
        try:
            response = requests.get(f"{base_url}/factors/available", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 因子管理接口正常")
            else:
                logger.warning(f"⚠️ 因子管理接口返回: {response.status_code}")
        except requests.exceptions.RequestException:
            logger.warning("⚠️ 因子管理接口测试失败")
        
        logger.info("✅ Web API接口测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ Web API接口测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始增强量化交易系统测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("股票数据管理器", test_stock_data_manager),
        ("量化因子", test_quantitative_factors),
        ("增强特征引擎", test_enhanced_feature_engine),
        ("带因子管理的模型管理器", test_model_manager_with_factors),
        ("回测引擎", test_backtest_engine),
        ("Web API接口", test_web_api_endpoints),
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ 测试 {test_name} 出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试总结")
    logger.info("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\n总计: {passed + failed} 项测试")
    logger.info(f"通过: {passed} 项")
    logger.info(f"失败: {failed} 项")
    logger.info(f"成功率: {passed / (passed + failed) * 100:.1f}%")
    
    if failed == 0:
        logger.info("\n🎉 所有测试通过！增强量化交易系统运行正常")
    else:
        logger.warning(f"\n⚠️ 有 {failed} 项测试失败，请检查相关功能")
    
    # 输出使用指南
    logger.info("\n" + "=" * 60)
    logger.info("📖 使用指南")
    logger.info("=" * 60)
    logger.info("1. 启动Web界面: python web/app.py")
    logger.info("2. 访问: http://127.0.0.1:5001")
    logger.info("3. 功能包括:")
    logger.info("   - 量化因子管理和选择")
    logger.info("   - 模型训练（支持因子组合）")
    logger.info("   - 策略回测和可视化")
    logger.info("   - 实时预测和调度")
    logger.info("4. 数据源: 基于adata，支持最近2年A股数据")
    logger.info("5. 回测指标: 年化收益率、最大回撤、夏普比率、胜率等")


if __name__ == "__main__":
    main()
