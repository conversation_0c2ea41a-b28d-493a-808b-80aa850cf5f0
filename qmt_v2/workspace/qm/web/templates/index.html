<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .status-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        
        .status-item.active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-item.inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .results {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        
        .results h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .prediction-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .prediction-item h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .prediction-value {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 2px;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        /* 因子管理样式 */
        .factor-category {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .factor-category h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1em;
        }

        .factor-tag {
            background: #4caf50;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }

        .factor-tag button {
            background: none;
            border: none;
            color: white;
            margin-left: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        /* 回测结果样式 */
        .backtest-metrics {
            margin: 15px 0;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 4px;
        }

        .metric-label {
            font-weight: bold;
            color: #666;
        }

        .metric-value {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .metric-value.positive {
            background: #c8e6c9;
            color: #2e7d32;
        }

        .metric-value.negative {
            background: #ffcdd2;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>量化交易系统 v2.0</h1>
            <p>基于机器学习的股票预测与交易系统 - 支持量化因子和回测分析</p>
        </div>
        
        <div class="dashboard">
            <!-- 系统状态卡片 -->
            <div class="card">
                <h3>系统状态</h3>
                <div id="system-status">
                    <div class="loading">加载中...</div>
                </div>
                <button class="btn" onclick="refreshStatus()">刷新状态</button>
            </div>
            
            <!-- 模型管理卡片 -->
            <div class="card">
                <h3>模型管理</h3>
                <div class="input-group">
                    <label>选择模型:</label>
                    <select id="model-select" multiple>
                        <option value="xgboost_10d_surge">10日拉升预测</option>
                        <option value="xgboost_next_day_direction" selected>明日涨跌预测</option>
                        <option value="time_series_price_prediction">价格预测</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>当前因子组合:</label>
                    <div id="current-factors">未选择因子</div>
                    <button class="btn" onclick="showFactorManager()">管理因子</button>
                </div>
                <button class="btn success" onclick="trainModels()">训练模型</button>
                <div id="training-result"></div>
            </div>

            <!-- 因子管理卡片 -->
            <div class="card">
                <h3>量化因子管理</h3>
                <div class="input-group">
                    <label>因子分类:</label>
                    <select id="factor-category-select" onchange="loadFactorsByCategory()">
                        <option value="">选择分类</option>
                        <option value="momentum">动量因子</option>
                        <option value="mean_reversion">均值回归因子</option>
                        <option value="volume">成交量因子</option>
                        <option value="volatility">波动率因子</option>
                        <option value="trend">趋势因子</option>
                        <option value="technical">技术信号因子</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>可用因子:</label>
                    <div id="available-factors" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                        加载中...
                    </div>
                </div>
                <div class="input-group">
                    <label>已选因子 (<span id="selected-count">0</span>):</label>
                    <div id="selected-factors" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                        无
                    </div>
                </div>
                <button class="btn" onclick="getFactorImportance()">因子重要性分析</button>
                <button class="btn success" onclick="applyFactorSelection()">应用因子选择</button>
                <div id="factor-result"></div>
            </div>

            <!-- 回测管理卡片 -->
            <div class="card">
                <h3>策略回测</h3>
                <div class="input-group">
                    <label>回测模型:</label>
                    <select id="backtest-model-select">
                        <option value="xgboost_next_day_direction">明日涨跌预测</option>
                        <option value="xgboost_10d_surge">10日拉升预测</option>
                        <option value="time_series_price_prediction">价格预测</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>回测期间:</label>
                    <input type="date" id="backtest-start-date" value="2023-01-01">
                    <input type="date" id="backtest-end-date" value="2023-12-31">
                </div>
                <div class="input-group">
                    <label>测试股票:</label>
                    <input type="text" id="backtest-stocks" value="000001,000002,600000" placeholder="股票代码，逗号分隔">
                </div>
                <button class="btn success" onclick="runBacktest()">运行回测</button>
                <button class="btn" onclick="loadBacktestHistory()">历史回测</button>
                <div id="backtest-result"></div>
            </div>

            <!-- 预测管理卡片 -->
            <div class="card">
                <h3>预测管理</h3>
                <div class="input-group">
                    <label>股票代码:</label>
                    <input type="text" id="stock-code" value="000001" placeholder="输入股票代码">
                </div>
                <button class="btn" onclick="runPrediction()">执行预测</button>
                <button class="btn success" onclick="manualPrediction()">手动预测任务</button>
                <div id="prediction-result"></div>
            </div>
            
            <!-- 调度器管理卡片 -->
            <div class="card">
                <h3>调度器管理</h3>
                <div id="scheduler-status">
                    <div class="loading">加载中...</div>
                </div>
                <button class="btn success" onclick="startScheduler()">启动调度器</button>
                <button class="btn danger" onclick="stopScheduler()">停止调度器</button>
                <div id="scheduler-result"></div>
            </div>
        </div>
        
        <!-- 预测结果展示 -->
        <div class="results">
            <h3>最新预测结果</h3>
            <div class="input-group">
                <label>查看股票:</label>
                <input type="text" id="view-stock-code" value="000001" placeholder="输入股票代码">
                <button class="btn" onclick="loadPredictions()">查看预测</button>
            </div>
            <div id="predictions-display">
                <div class="loading">点击"查看预测"加载数据</div>
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/api';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            loadPredictions();
            loadAvailableFactors();
            loadCurrentFactors();
        });
        
        // 刷新系统状态
        async function refreshStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const result = await response.json();
                
                if (result.success) {
                    displaySystemStatus(result.data);
                    displaySchedulerStatus(result.data.scheduler);
                } else {
                    document.getElementById('system-status').innerHTML = 
                        `<div class="error">获取状态失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('system-status').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 显示系统状态
        function displaySystemStatus(data) {
            const models = data.models;
            let html = '<div class="status-grid">';
            
            for (const [modelName, isActive] of Object.entries(models)) {
                const statusClass = isActive ? 'active' : 'inactive';
                const statusText = isActive ? '已训练' : '未训练';
                html += `<div class="status-item ${statusClass}">${modelName}<br>${statusText}</div>`;
            }
            
            html += '</div>';
            html += `<p><strong>更新时间:</strong> ${data.timestamp}</p>`;
            
            document.getElementById('system-status').innerHTML = html;
        }
        
        // 显示调度器状态
        function displaySchedulerStatus(scheduler) {
            const statusClass = scheduler.is_running ? 'active' : 'inactive';
            const statusText = scheduler.is_running ? '运行中' : '已停止';
            
            let html = `<div class="status-item ${statusClass}">调度器状态: ${statusText}</div>`;
            html += `<p><strong>预测时间:</strong> ${scheduler.prediction_times.join(', ')}</p>`;
            html += `<p><strong>股票池:</strong> ${scheduler.stock_pool.join(', ')}</p>`;
            
            document.getElementById('scheduler-status').innerHTML = html;
        }
        
        // 训练模型
        async function trainModels() {
            const select = document.getElementById('model-select');
            const selectedModels = Array.from(select.selectedOptions).map(option => option.value);
            
            if (selectedModels.length === 0) {
                alert('请选择要训练的模型');
                return;
            }
            
            document.getElementById('training-result').innerHTML = '<div class="loading">训练中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/train`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        models: selectedModels
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let html = '<div class="success">训练完成!</div>';
                    for (const [modelName, modelResult] of Object.entries(result.data)) {
                        if (modelResult.success) {
                            const metrics = modelResult.metrics;
                            html += `<div class="prediction-item">
                                <h4>${modelName}</h4>
                                <span class="prediction-value">AUC: ${(metrics.auc || 0).toFixed(4)}</span>
                                <span class="prediction-value">准确率: ${(metrics.accuracy || 0).toFixed(4)}</span>
                            </div>`;
                        } else {
                            html += `<div class="error">${modelName}: ${modelResult.error}</div>`;
                        }
                    }
                    document.getElementById('training-result').innerHTML = html;
                    refreshStatus(); // 刷新状态
                } else {
                    document.getElementById('training-result').innerHTML = 
                        `<div class="error">训练失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('training-result').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 执行预测
        async function runPrediction() {
            const stockCode = document.getElementById('stock-code').value;
            
            if (!stockCode) {
                alert('请输入股票代码');
                return;
            }
            
            document.getElementById('prediction-result').innerHTML = '<div class="loading">预测中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        stock_code: stockCode
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    let html = '<div class="success">预测完成!</div>';
                    html += `<div class="prediction-item">
                        <h4>${data.stock_code} - ${data.prediction_time}</h4>`;
                    
                    for (const [key, value] of Object.entries(data.predictions)) {
                        html += `<span class="prediction-value">${key}: ${value.toFixed(4)}</span>`;
                    }
                    
                    html += '</div>';
                    document.getElementById('prediction-result').innerHTML = html;
                    
                    // 自动刷新预测结果显示
                    loadPredictions();
                } else {
                    document.getElementById('prediction-result').innerHTML = 
                        `<div class="error">预测失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('prediction-result').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 启动调度器
        async function startScheduler() {
            try {
                const response = await fetch(`${API_BASE}/scheduler/start`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="success">${result.message}</div>`;
                    refreshStatus();
                } else {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="error">启动失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('scheduler-result').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 停止调度器
        async function stopScheduler() {
            try {
                const response = await fetch(`${API_BASE}/scheduler/stop`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="success">${result.message}</div>`;
                    refreshStatus();
                } else {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="error">停止失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('scheduler-result').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 手动预测任务
        async function manualPrediction() {
            try {
                const response = await fetch(`${API_BASE}/scheduler/manual`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="success">${result.message}</div>`;
                    loadPredictions();
                } else {
                    document.getElementById('scheduler-result').innerHTML = 
                        `<div class="error">执行失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('scheduler-result').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 加载预测结果
        async function loadPredictions() {
            const stockCode = document.getElementById('view-stock-code').value || '000001';
            
            document.getElementById('predictions-display').innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/predictions?stock_code=${stockCode}&days=7`);
                const result = await response.json();
                
                if (result.success) {
                    displayPredictions(result.data);
                } else {
                    document.getElementById('predictions-display').innerHTML = 
                        `<div class="error">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('predictions-display').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 显示预测结果
        function displayPredictions(predictions) {
            if (predictions.length === 0) {
                document.getElementById('predictions-display').innerHTML = 
                    '<div class="loading">暂无预测数据</div>';
                return;
            }
            
            let html = '';
            const groupedPredictions = {};
            
            // 按日期分组
            predictions.forEach(pred => {
                const key = `${pred.prediction_date} ${pred.prediction_time}`;
                if (!groupedPredictions[key]) {
                    groupedPredictions[key] = [];
                }
                groupedPredictions[key].push(pred);
            });
            
            // 显示分组结果
            for (const [datetime, preds] of Object.entries(groupedPredictions)) {
                html += `<div class="prediction-item">
                    <h4>${preds[0].stock_code} - ${datetime}</h4>`;
                
                preds.forEach(pred => {
                    html += `<span class="prediction-value">
                        ${pred.model_name}: ${pred.prediction_value.toFixed(4)}
                        (置信度: ${pred.confidence.toFixed(3)})
                    </span>`;
                });
                
                html += '</div>';
            }
            
            document.getElementById('predictions-display').innerHTML = html;
        }

        // ==================== 因子管理功能 ====================

        let selectedFactors = [];

        // 加载可用因子
        async function loadAvailableFactors() {
            try {
                const response = await fetch(`${API_BASE}/factors/available`);
                const result = await response.json();

                if (result.success) {
                    displayAvailableFactors(result.data);
                } else {
                    document.getElementById('available-factors').innerHTML =
                        `<div class="error">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('available-factors').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示可用因子
        function displayAvailableFactors(factors) {
            let html = '';
            for (const [category, factorList] of Object.entries(factors)) {
                html += `<div class="factor-category">
                    <h4>${category}</h4>`;

                factorList.forEach(factor => {
                    const isSelected = selectedFactors.includes(factor);
                    const checkedAttr = isSelected ? 'checked' : '';
                    html += `<label style="display: block; margin: 2px 0;">
                        <input type="checkbox" value="${factor}" ${checkedAttr} onchange="toggleFactor('${factor}')">
                        ${factor}
                    </label>`;
                });

                html += '</div>';
            }

            document.getElementById('available-factors').innerHTML = html;
        }

        // 按分类加载因子
        async function loadFactorsByCategory() {
            const category = document.getElementById('factor-category-select').value;

            if (!category) {
                loadAvailableFactors();
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/factors/available`);
                const result = await response.json();

                if (result.success && result.data[category]) {
                    const factors = result.data[category];
                    let html = `<div class="factor-category">
                        <h4>${category}</h4>`;

                    factors.forEach(factor => {
                        const isSelected = selectedFactors.includes(factor);
                        const checkedAttr = isSelected ? 'checked' : '';
                        html += `<label style="display: block; margin: 2px 0;">
                            <input type="checkbox" value="${factor}" ${checkedAttr} onchange="toggleFactor('${factor}')">
                            ${factor}
                        </label>`;
                    });

                    html += '</div>';
                    document.getElementById('available-factors').innerHTML = html;
                }
            } catch (error) {
                document.getElementById('available-factors').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 切换因子选择
        function toggleFactor(factor) {
            const index = selectedFactors.indexOf(factor);
            if (index > -1) {
                selectedFactors.splice(index, 1);
            } else {
                selectedFactors.push(factor);
            }

            updateSelectedFactorsDisplay();
        }

        // 更新已选因子显示
        function updateSelectedFactorsDisplay() {
            const count = selectedFactors.length;
            document.getElementById('selected-count').textContent = count;

            if (count === 0) {
                document.getElementById('selected-factors').innerHTML = '无';
            } else {
                const html = selectedFactors.map(factor =>
                    `<span class="factor-tag">${factor} <button onclick="removeFactor('${factor}')">×</button></span>`
                ).join('');
                document.getElementById('selected-factors').innerHTML = html;
            }
        }

        // 移除因子
        function removeFactor(factor) {
            const index = selectedFactors.indexOf(factor);
            if (index > -1) {
                selectedFactors.splice(index, 1);
                updateSelectedFactorsDisplay();

                // 更新复选框状态
                const checkbox = document.querySelector(`input[value="${factor}"]`);
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        }

        // 加载当前因子组合
        async function loadCurrentFactors() {
            try {
                const response = await fetch(`${API_BASE}/factors/enabled`);
                const result = await response.json();

                if (result.success) {
                    selectedFactors = result.data || [];
                    updateSelectedFactorsDisplay();
                    document.getElementById('current-factors').textContent =
                        selectedFactors.length > 0 ? `${selectedFactors.length} 个因子` : '未选择因子';
                }
            } catch (error) {
                console.error('加载当前因子失败:', error);
            }
        }

        // 应用因子选择
        async function applyFactorSelection() {
            if (selectedFactors.length === 0) {
                alert('请至少选择一个因子');
                return;
            }

            document.getElementById('factor-result').innerHTML = '<div class="loading">应用中...</div>';

            try {
                const response = await fetch(`${API_BASE}/factors/set_combination`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        factors: selectedFactors,
                        name: `Custom_${new Date().toISOString().split('T')[0]}`
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="success">${result.message}</div>`;
                    document.getElementById('current-factors').textContent = `${selectedFactors.length} 个因子`;
                } else {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="error">应用失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('factor-result').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 获取因子重要性
        async function getFactorImportance() {
            document.getElementById('factor-result').innerHTML = '<div class="loading">分析中...</div>';

            try {
                const response = await fetch(`${API_BASE}/factors/importance`);
                const result = await response.json();

                if (result.success) {
                    displayFactorImportance(result.data);
                } else {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="error">分析失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('factor-result').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示因子重要性
        function displayFactorImportance(importance) {
            if (importance.length === 0) {
                document.getElementById('factor-result').innerHTML =
                    '<div class="loading">暂无因子重要性数据</div>';
                return;
            }

            let html = '<div class="success">因子重要性分析结果:</div>';
            html += '<div style="max-height: 200px; overflow-y: auto;">';

            importance.slice(0, 10).forEach((item, index) => {
                html += `<div class="prediction-item">
                    <span>${index + 1}. ${item.factor}</span>
                    <span class="prediction-value">重要性: ${item.combined_importance.toFixed(4)}</span>
                </div>`;
            });

            html += '</div>';
            document.getElementById('factor-result').innerHTML = html;
        }

        // ==================== 回测功能 ====================

        // 运行回测
        async function runBacktest() {
            const modelName = document.getElementById('backtest-model-select').value;
            const startDate = document.getElementById('backtest-start-date').value;
            const endDate = document.getElementById('backtest-end-date').value;
            const stocksInput = document.getElementById('backtest-stocks').value;

            if (!startDate || !endDate) {
                alert('请选择回测期间');
                return;
            }

            if (!stocksInput) {
                alert('请输入测试股票代码');
                return;
            }

            const stockCodes = stocksInput.split(',').map(s => s.trim());

            document.getElementById('backtest-result').innerHTML = '<div class="loading">回测运行中，请稍候...</div>';

            try {
                const response = await fetch(`${API_BASE}/backtest/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model_name: modelName,
                        start_date: startDate,
                        end_date: endDate,
                        stock_codes: stockCodes,
                        factors: selectedFactors
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayBacktestResults(result.data);
                } else {
                    document.getElementById('backtest-result').innerHTML =
                        `<div class="error">回测失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('backtest-result').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示回测结果
        function displayBacktestResults(data) {
            const basic = data.basic_metrics;
            const trading = data.trading_metrics;
            const period = data.trading_period;

            let html = '<div class="success">回测完成!</div>';

            // 基本指标
            html += '<div class="backtest-metrics">';
            html += '<h4>基本指标</h4>';
            html += `<div class="metric-grid">
                <div class="metric-item">
                    <span class="metric-label">总收益率</span>
                    <span class="metric-value ${basic.total_return >= 0 ? 'positive' : 'negative'}">${basic.total_return}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">年化收益率</span>
                    <span class="metric-value ${basic.annual_return >= 0 ? 'positive' : 'negative'}">${basic.annual_return}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">最大回撤</span>
                    <span class="metric-value negative">${basic.max_drawdown}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">夏普比率</span>
                    <span class="metric-value ${basic.sharpe_ratio >= 0 ? 'positive' : 'negative'}">${basic.sharpe_ratio}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">波动率</span>
                    <span class="metric-value">${basic.volatility}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">索提诺比率</span>
                    <span class="metric-value ${basic.sortino_ratio >= 0 ? 'positive' : 'negative'}">${basic.sortino_ratio}</span>
                </div>
            </div>`;

            // 交易指标
            html += '<h4>交易指标</h4>';
            html += `<div class="metric-grid">
                <div class="metric-item">
                    <span class="metric-label">总交易次数</span>
                    <span class="metric-value">${trading.total_trades}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">胜率</span>
                    <span class="metric-value ${trading.win_rate >= 50 ? 'positive' : 'negative'}">${trading.win_rate}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">平均盈亏</span>
                    <span class="metric-value ${trading.avg_profit_loss >= 0 ? 'positive' : 'negative'}">${trading.avg_profit_loss}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">平均盈亏率</span>
                    <span class="metric-value ${trading.avg_profit_loss_pct >= 0 ? 'positive' : 'negative'}">${trading.avg_profit_loss_pct}%</span>
                </div>
            </div>`;

            // 回测期间
            html += '<h4>回测期间</h4>';
            html += `<p><strong>开始日期:</strong> ${period.start_date}</p>`;
            html += `<p><strong>结束日期:</strong> ${period.end_date}</p>`;
            html += `<p><strong>交易日数:</strong> ${period.trading_days}</p>`;
            html += `<p><strong>最终组合价值:</strong> ¥${data.final_portfolio_value.toLocaleString()}</p>`;

            // 收益曲线图表（简化版）
            if (data.portfolio_curve && data.portfolio_curve.length > 0) {
                html += '<h4>收益曲线</h4>';
                html += '<div id="portfolio-chart" style="height: 200px; border: 1px solid #ddd; margin: 10px 0; padding: 10px;">';
                html += '收益曲线图表（需要图表库支持）';
                html += '</div>';
            }

            html += '</div>';

            document.getElementById('backtest-result').innerHTML = html;
        }

        // 加载回测历史
        async function loadBacktestHistory() {
            document.getElementById('backtest-result').innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`${API_BASE}/backtest/history`);
                const result = await response.json();

                if (result.success) {
                    displayBacktestHistory(result.data);
                } else {
                    document.getElementById('backtest-result').innerHTML =
                        `<div class="error">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('backtest-result').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示回测历史
        function displayBacktestHistory(history) {
            if (history.length === 0) {
                document.getElementById('backtest-result').innerHTML =
                    '<div class="loading">暂无回测历史记录</div>';
                return;
            }

            let html = '<div class="success">回测历史记录</div>';
            html += '<div style="max-height: 300px; overflow-y: auto;">';

            history.forEach(record => {
                html += `<div class="prediction-item">
                    <h4>${record.model_name}</h4>
                    <p><strong>期间:</strong> ${record.start_date} 到 ${record.end_date}</p>
                    <div class="metric-grid">
                        <span class="prediction-value">总收益: ${record.total_return}%</span>
                        <span class="prediction-value">年化收益: ${record.annual_return}%</span>
                        <span class="prediction-value">最大回撤: ${record.max_drawdown}%</span>
                        <span class="prediction-value">夏普比率: ${record.sharpe_ratio}</span>
                        <span class="prediction-value">胜率: ${record.win_rate}%</span>
                    </div>
                    <p><small>创建时间: ${record.created_at}</small></p>
                </div>`;
            });

            html += '</div>';
            document.getElementById('backtest-result').innerHTML = html;
        }
    </script>
</body>
</html>
