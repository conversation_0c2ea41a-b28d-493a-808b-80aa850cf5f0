<!DOCTYPE html>
<!--
现代化量化交易系统Web界面 v2.0
Modern Quantitative Trading System Web Interface

🎨 设计特色:
- 深色主题设计，参考fellou.ai风格
- 动态渐变背景，20秒循环动画
- 玻璃拟态卡片，半透明模糊效果
- 流畅微交互，悬停和点击动画
- 响应式布局，适配各种设备
- 智能通知系统，右上角动态提醒

🔧 技术栈:
- HTML5 + CSS3 + JavaScript
- Inter现代字体 + Font Awesome 6.0图标
- CSS Grid + Flexbox布局
- CSS Variables设计系统
- 原生JavaScript，无框架依赖

💡 功能模块:
- 系统状态监控
- 量化因子工程管理
- 模型训练和监控
- 策略回测分析
- 实时预测系统
- 定时调度管理

作者: Augment Agent
日期: 2025-06-17
版本: v2.0
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易系统 v2.0 - AI驱动的智能投资平台</title>
    <!-- 现代字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            
            --bg-dark: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-card: rgba(255, 255, 255, 0.05);
            --bg-card-hover: rgba(255, 255, 255, 0.08);
            
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            
            --border-color: rgba(255, 255, 255, 0.1);
            --border-hover: rgba(255, 255, 255, 0.2);
            
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 16px 64px rgba(0, 0, 0, 0.3);
            
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }
        
        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 导航栏 */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            margin-bottom: 40px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo i {
            font-size: 2rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
            align-items: center;
        }
        
        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover {
            color: var(--text-primary);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-gradient);
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        /* 头部区域 */
        .hero {
            text-align: center;
            margin-bottom: 60px;
            padding: 60px 0;
        }
        
        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: 20px;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }
        
        .hero p {
            font-size: 1.25rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto 40px;
        }
        
        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            background: var(--success-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 4px;
        }
        
        /* 卡片网格 */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 32px;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .card:hover {
            background: var(--bg-card-hover);
            border-color: var(--border-hover);
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .card:hover::before {
            opacity: 1;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-sm);
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .card-content {
            margin-bottom: 24px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: var(--border-hover);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .input-group input::placeholder {
            color: var(--text-muted);
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: var(--radius-sm);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: var(--success-gradient);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }
        
        .btn-warning {
            background: var(--warning-gradient);
            color: white;
        }
        
        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3);
        }
        
        .btn-danger {
            background: var(--danger-gradient);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--border-hover);
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-indicator.active {
            background: rgba(67, 233, 123, 0.2);
            color: #43e97b;
        }
        
        .status-indicator.inactive {
            background: rgba(250, 112, 154, 0.2);
            color: #fa709a;
        }
        
        .status-indicator.warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        
        /* 结果显示 */
        .result-container {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-top: 20px;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .loading i {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .error {
            background: rgba(250, 112, 154, 0.1);
            border: 1px solid rgba(250, 112, 154, 0.3);
            color: #fa709a;
            padding: 16px;
            border-radius: var(--radius-sm);
            margin: 16px 0;
        }
        
        .success {
            background: rgba(67, 233, 123, 0.1);
            border: 1px solid rgba(67, 233, 123, 0.3);
            color: #43e97b;
            padding: 16px;
            border-radius: var(--radius-sm);
            margin: 16px 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .hero-stats {
                gap: 20px;
            }
            
            .nav-links {
                gap: 20px;
            }
            
            .card {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                量化交易系统
            </div>
            <div class="nav-links">
                <a href="#" class="nav-link">仪表板</a>
                <a href="#" class="nav-link">策略</a>
                <a href="#" class="nav-link">回测</a>
                <a href="#" class="nav-link">设置</a>
            </div>
        </nav>

        <!-- 头部区域 -->
        <section class="hero">
            <h1>AI驱动的量化交易平台</h1>
            <p>基于机器学习的A股量化因子工程、策略回测和实时预测系统</p>
            
            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number" id="total-factors">20+</div>
                    <div class="stat-label">量化因子</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="models-count">3</div>
                    <div class="stat-label">ML模型</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="success-rate">85%</div>
                    <div class="stat-label">预测准确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="data-sources">4</div>
                    <div class="stat-label">数据源</div>
                </div>
            </div>
        </section>

        <!-- 主要功能卡片 -->
        <div class="dashboard">
            <!-- 系统状态卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3>系统状态</h3>
                </div>
                <div class="card-content">
                    <div id="system-status">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            加载系统状态...
                        </div>
                    </div>
                </div>
                <button class="btn btn-outline" onclick="refreshStatus()">
                    <i class="fas fa-sync-alt"></i>
                    刷新状态
                </button>
            </div>

            <!-- 量化因子管理卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>量化因子工程</h3>
                </div>
                <div class="card-content">
                    <div class="input-group">
                        <label>因子分类</label>
                        <select id="factor-category-select" onchange="loadFactorsByCategory()">
                            <option value="">选择分类</option>
                            <option value="momentum">动量因子</option>
                            <option value="mean_reversion">均值回归因子</option>
                            <option value="volume">成交量因子</option>
                            <option value="volatility">波动率因子</option>
                            <option value="trend">趋势因子</option>
                            <option value="technical">技术信号因子</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label>可用因子</label>
                        <div id="available-factors" style="max-height: 200px; overflow-y: auto; background: rgba(255,255,255,0.03); border-radius: 8px; padding: 12px;">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                加载因子...
                            </div>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>已选因子 (<span id="selected-count">0</span>)</label>
                        <div id="selected-factors" style="min-height: 60px; background: rgba(255,255,255,0.03); border-radius: 8px; padding: 12px;">
                            <span style="color: var(--text-muted);">未选择因子</span>
                        </div>
                    </div>

                    <div id="factor-result"></div>
                </div>

                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="getFactorImportance()">
                        <i class="fas fa-chart-bar"></i>
                        重要性分析
                    </button>
                    <button class="btn btn-success" onclick="applyFactorSelection()">
                        <i class="fas fa-check"></i>
                        应用选择
                    </button>
                </div>
            </div>

            <!-- 模型训练卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>模型训练</h3>
                </div>
                <div class="card-content">
                    <div class="input-group">
                        <label>选择模型</label>
                        <select id="model-select" multiple style="height: 120px;">
                            <option value="xgboost_10d_surge">10日拉升预测</option>
                            <option value="xgboost_next_day_direction" selected>明日涨跌预测</option>
                            <option value="time_series_price_prediction">价格预测</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label>当前因子组合</label>
                        <div id="current-factors" style="background: rgba(255,255,255,0.03); border-radius: 8px; padding: 12px; color: var(--text-secondary);">
                            未选择因子
                        </div>
                    </div>

                    <div id="training-result"></div>
                </div>

                <button class="btn btn-success" onclick="trainModels()">
                    <i class="fas fa-play"></i>
                    开始训练
                </button>
            </div>

            <!-- 策略回测卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-area"></i>
                    </div>
                    <h3>策略回测</h3>
                </div>
                <div class="card-content">
                    <div class="input-group">
                        <label>回测模型</label>
                        <select id="backtest-model-select">
                            <option value="xgboost_next_day_direction">明日涨跌预测</option>
                            <option value="xgboost_10d_surge">10日拉升预测</option>
                            <option value="time_series_price_prediction">价格预测</option>
                        </select>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                        <div class="input-group">
                            <label>开始日期</label>
                            <input type="date" id="backtest-start-date" value="2023-01-01">
                        </div>
                        <div class="input-group">
                            <label>结束日期</label>
                            <input type="date" id="backtest-end-date" value="2023-12-31">
                        </div>
                    </div>

                    <div class="input-group">
                        <label>测试股票</label>
                        <input type="text" id="backtest-stocks" value="000001,000002,600000" placeholder="股票代码，逗号分隔">
                    </div>

                    <div id="backtest-result"></div>
                </div>

                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-success" onclick="runBacktest()">
                        <i class="fas fa-rocket"></i>
                        运行回测
                    </button>
                    <button class="btn btn-outline" onclick="loadBacktestHistory()">
                        <i class="fas fa-history"></i>
                        历史记录
                    </button>
                </div>
            </div>

            <!-- 预测管理卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-crystal-ball"></i>
                    </div>
                    <h3>预测管理</h3>
                </div>
                <div class="card-content">
                    <div class="input-group">
                        <label>预测模型</label>
                        <select id="prediction-model-select">
                            <option value="all">所有模型</option>
                            <option value="xgboost_next_day_direction">明日涨跌预测</option>
                            <option value="xgboost_10d_surge">10日拉升预测</option>
                            <option value="time_series_price_prediction">价格预测</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label>股票代码</label>
                        <input type="text" id="prediction-stocks" value="000001,000002" placeholder="股票代码，逗号分隔">
                    </div>

                    <div id="predictions-result"></div>
                </div>

                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="runPrediction()">
                        <i class="fas fa-magic"></i>
                        执行预测
                    </button>
                    <button class="btn btn-outline" onclick="loadPredictions()">
                        <i class="fas fa-download"></i>
                        加载结果
                    </button>
                </div>
            </div>

            <!-- 调度器管理卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>定时调度</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; gap: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>早盘预测 (9:35)</span>
                            <span class="status-indicator" id="morning-schedule-status">
                                <i class="fas fa-clock"></i>
                                未设置
                            </span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>午盘预测 (14:50)</span>
                            <span class="status-indicator" id="afternoon-schedule-status">
                                <i class="fas fa-clock"></i>
                                未设置
                            </span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>调度器状态</span>
                            <span class="status-indicator" id="scheduler-status">
                                <i class="fas fa-stop-circle"></i>
                                已停止
                            </span>
                        </div>
                    </div>

                    <div id="scheduler-result"></div>
                </div>

                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-success" onclick="startScheduler()">
                        <i class="fas fa-play"></i>
                        启动调度
                    </button>
                    <button class="btn btn-danger" onclick="stopScheduler()">
                        <i class="fas fa-stop"></i>
                        停止调度
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        
        // 全局变量
        let selectedFactors = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            loadAvailableFactors();
            loadCurrentFactors();
            updateSchedulerStatus();
        });
        
        // 刷新系统状态
        async function refreshStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const result = await response.json();
                
                if (result.success) {
                    displaySystemStatus(result.data);
                } else {
                    document.getElementById('system-status').innerHTML = 
                        `<div class="error">获取状态失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('system-status').innerHTML = 
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 显示系统状态
        function displaySystemStatus(status) {
            const models = status.models || {};
            const scheduler = status.scheduler || {};
            
            let html = '<div style="display: grid; gap: 12px;">';
            
            // 模型状态
            for (const [modelName, isActive] of Object.entries(models)) {
                const statusClass = isActive ? 'active' : 'inactive';
                const statusText = isActive ? '已训练' : '未训练';
                const icon = isActive ? 'check-circle' : 'times-circle';
                
                html += `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${modelName}</span>
                        <span class="status-indicator ${statusClass}">
                            <i class="fas fa-${icon}"></i>
                            ${statusText}
                        </span>
                    </div>
                `;
            }
            
            // 调度器状态
            const schedulerStatus = scheduler.running ? 'active' : 'inactive';
            const schedulerText = scheduler.running ? '运行中' : '已停止';
            const schedulerIcon = scheduler.running ? 'play-circle' : 'stop-circle';
            
            html += `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>预测调度器</span>
                    <span class="status-indicator ${schedulerStatus}">
                        <i class="fas fa-${schedulerIcon}"></i>
                        ${schedulerText}
                    </span>
                </div>
            `;
            
            html += '</div>';
            
            document.getElementById('system-status').innerHTML = html;
        }

        // ==================== 因子管理功能 ====================

        // 加载可用因子
        async function loadAvailableFactors() {
            try {
                const response = await fetch(`${API_BASE}/factors/available`);
                const result = await response.json();

                if (result.success) {
                    displayAvailableFactors(result.data);
                } else {
                    document.getElementById('available-factors').innerHTML =
                        `<div class="error">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('available-factors').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示可用因子
        function displayAvailableFactors(factors) {
            let html = '';
            for (const [category, factorList] of Object.entries(factors)) {
                html += `<div style="margin-bottom: 16px;">
                    <h4 style="color: var(--text-primary); margin-bottom: 8px; font-size: 0.9rem;">${category}</h4>`;

                factorList.forEach(factor => {
                    const isSelected = selectedFactors.includes(factor);
                    const checkedAttr = isSelected ? 'checked' : '';
                    html += `<label style="display: flex; align-items: center; margin: 4px 0; cursor: pointer; color: var(--text-secondary);">
                        <input type="checkbox" value="${factor}" ${checkedAttr} onchange="toggleFactor('${factor}')"
                               style="margin-right: 8px; accent-color: #667eea;">
                        <span style="font-size: 0.85rem;">${factor}</span>
                    </label>`;
                });

                html += '</div>';
            }

            document.getElementById('available-factors').innerHTML = html;
        }

        // 按分类加载因子
        async function loadFactorsByCategory() {
            const category = document.getElementById('factor-category-select').value;

            if (!category) {
                loadAvailableFactors();
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/factors/available`);
                const result = await response.json();

                if (result.success && result.data[category]) {
                    const factors = result.data[category];
                    let html = `<div style="margin-bottom: 16px;">
                        <h4 style="color: var(--text-primary); margin-bottom: 8px; font-size: 0.9rem;">${category}</h4>`;

                    factors.forEach(factor => {
                        const isSelected = selectedFactors.includes(factor);
                        const checkedAttr = isSelected ? 'checked' : '';
                        html += `<label style="display: flex; align-items: center; margin: 4px 0; cursor: pointer; color: var(--text-secondary);">
                            <input type="checkbox" value="${factor}" ${checkedAttr} onchange="toggleFactor('${factor}')"
                                   style="margin-right: 8px; accent-color: #667eea;">
                            <span style="font-size: 0.85rem;">${factor}</span>
                        </label>`;
                    });

                    html += '</div>';
                    document.getElementById('available-factors').innerHTML = html;
                }
            } catch (error) {
                document.getElementById('available-factors').innerHTML =
                    `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 切换因子选择
        function toggleFactor(factor) {
            const index = selectedFactors.indexOf(factor);
            if (index > -1) {
                selectedFactors.splice(index, 1);
            } else {
                selectedFactors.push(factor);
            }

            updateSelectedFactorsDisplay();
        }

        // 更新已选因子显示
        function updateSelectedFactorsDisplay() {
            const count = selectedFactors.length;
            document.getElementById('selected-count').textContent = count;

            if (count === 0) {
                document.getElementById('selected-factors').innerHTML =
                    '<span style="color: var(--text-muted);">未选择因子</span>';
            } else {
                const html = selectedFactors.map(factor =>
                    `<span style="display: inline-flex; align-items: center; background: var(--primary-gradient); color: white; padding: 4px 8px; border-radius: 12px; margin: 2px; font-size: 0.8rem;">
                        ${factor}
                        <button onclick="removeFactor('${factor}')" style="background: none; border: none; color: white; margin-left: 4px; cursor: pointer; font-weight: bold;">×</button>
                    </span>`
                ).join('');
                document.getElementById('selected-factors').innerHTML = html;
            }
        }

        // 移除因子
        function removeFactor(factor) {
            const index = selectedFactors.indexOf(factor);
            if (index > -1) {
                selectedFactors.splice(index, 1);
                updateSelectedFactorsDisplay();

                // 更新复选框状态
                const checkbox = document.querySelector(`input[value="${factor}"]`);
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        }

        // 加载当前因子组合
        async function loadCurrentFactors() {
            try {
                const response = await fetch(`${API_BASE}/factors/enabled`);
                const result = await response.json();

                if (result.success) {
                    selectedFactors = result.data || [];
                    updateSelectedFactorsDisplay();
                    document.getElementById('current-factors').innerHTML =
                        selectedFactors.length > 0 ?
                        `<span style="color: var(--text-primary);">${selectedFactors.length} 个因子已选择</span>` :
                        '<span style="color: var(--text-muted);">未选择因子</span>';
                }
            } catch (error) {
                console.error('加载当前因子失败:', error);
            }
        }

        // 应用因子选择
        async function applyFactorSelection() {
            if (selectedFactors.length === 0) {
                showNotification('请至少选择一个因子', 'warning');
                return;
            }

            document.getElementById('factor-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 应用中...</div>';

            try {
                const response = await fetch(`${API_BASE}/factors/set_combination`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        factors: selectedFactors,
                        name: `Custom_${new Date().toISOString().split('T')[0]}`
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="success"><i class="fas fa-check"></i> ${result.message}</div>`;
                    document.getElementById('current-factors').innerHTML =
                        `<span style="color: var(--text-primary);">${selectedFactors.length} 个因子已应用</span>`;
                    showNotification('因子组合应用成功', 'success');
                } else {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 应用失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('factor-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 获取因子重要性
        async function getFactorImportance() {
            document.getElementById('factor-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 分析中...</div>';

            try {
                const response = await fetch(`${API_BASE}/factors/importance`);
                const result = await response.json();

                if (result.success) {
                    displayFactorImportance(result.data);
                } else {
                    document.getElementById('factor-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 分析失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('factor-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 显示因子重要性
        function displayFactorImportance(importance) {
            if (importance.length === 0) {
                document.getElementById('factor-result').innerHTML =
                    '<div style="color: var(--text-muted); text-align: center; padding: 20px;">暂无因子重要性数据</div>';
                return;
            }

            let html = '<div class="success"><i class="fas fa-chart-bar"></i> 因子重要性分析结果</div>';
            html += '<div style="max-height: 200px; overflow-y: auto; margin-top: 12px;">';

            importance.slice(0, 10).forEach((item, index) => {
                const percentage = (item.combined_importance * 100).toFixed(1);
                html += `<div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid var(--border-color);">
                    <span style="color: var(--text-primary);">${index + 1}. ${item.factor}</span>
                    <span style="background: var(--success-gradient); color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;">
                        ${percentage}%
                    </span>
                </div>`;
            });

            html += '</div>';
            document.getElementById('factor-result').innerHTML = html;
        }

        // ==================== 模型训练功能 ====================

        // 训练模型
        async function trainModels() {
            const modelSelect = document.getElementById('model-select');
            const selectedModels = Array.from(modelSelect.selectedOptions).map(option => option.value);

            if (selectedModels.length === 0) {
                showNotification('请选择至少一个模型', 'warning');
                return;
            }

            document.getElementById('training-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 训练中，请稍候...</div>';

            try {
                const response = await fetch(`${API_BASE}/train`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        models: selectedModels,
                        factors: selectedFactors,
                        factor_categories: []
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayTrainingResults(result.data);
                    refreshStatus(); // 刷新系统状态
                    showNotification('模型训练完成', 'success');
                } else {
                    document.getElementById('training-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 训练失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('training-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 显示训练结果
        function displayTrainingResults(results) {
            let html = '<div class="success"><i class="fas fa-check"></i> 训练完成</div>';
            html += '<div style="margin-top: 12px;">';

            for (const [modelName, result] of Object.entries(results)) {
                const status = result.success ? 'success' : 'error';
                const icon = result.success ? 'check-circle' : 'times-circle';

                html += `<div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid var(--border-color);">
                    <span style="color: var(--text-primary);">${modelName}</span>
                    <span class="status-indicator ${status === 'success' ? 'active' : 'inactive'}">
                        <i class="fas fa-${icon}"></i>
                        ${result.success ? '成功' : '失败'}
                    </span>
                </div>`;

                if (result.success && result.metrics) {
                    html += `<div style="margin-left: 20px; color: var(--text-secondary); font-size: 0.8rem;">`;
                    for (const [metric, value] of Object.entries(result.metrics)) {
                        html += `${metric}: ${typeof value === 'number' ? value.toFixed(4) : value} `;
                    }
                    html += `</div>`;
                }
            }

            html += '</div>';
            document.getElementById('training-result').innerHTML = html;
        }

        // ==================== 回测功能 ====================

        // 运行回测
        async function runBacktest() {
            const modelName = document.getElementById('backtest-model-select').value;
            const startDate = document.getElementById('backtest-start-date').value;
            const endDate = document.getElementById('backtest-end-date').value;
            const stocksInput = document.getElementById('backtest-stocks').value;

            if (!startDate || !endDate) {
                showNotification('请选择回测期间', 'warning');
                return;
            }

            if (!stocksInput) {
                showNotification('请输入测试股票代码', 'warning');
                return;
            }

            const stockCodes = stocksInput.split(',').map(s => s.trim());

            document.getElementById('backtest-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 回测运行中，请稍候...</div>';

            try {
                const response = await fetch(`${API_BASE}/backtest/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model_name: modelName,
                        start_date: startDate,
                        end_date: endDate,
                        stock_codes: stockCodes,
                        factors: selectedFactors
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayBacktestResults(result.data);
                    showNotification('回测完成', 'success');
                } else {
                    document.getElementById('backtest-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 回测失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('backtest-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 显示回测结果
        function displayBacktestResults(data) {
            const basic = data.basic_metrics;
            const trading = data.trading_metrics;
            const period = data.trading_period;

            let html = '<div class="success"><i class="fas fa-chart-line"></i> 回测完成</div>';

            // 关键指标卡片
            html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; margin: 16px 0;">';

            const metrics = [
                { label: '总收益率', value: `${basic.total_return}%`, positive: basic.total_return >= 0 },
                { label: '年化收益率', value: `${basic.annual_return}%`, positive: basic.annual_return >= 0 },
                { label: '最大回撤', value: `${basic.max_drawdown}%`, positive: false },
                { label: '夏普比率', value: basic.sharpe_ratio.toFixed(2), positive: basic.sharpe_ratio >= 0 },
                { label: '胜率', value: `${trading.win_rate}%`, positive: trading.win_rate >= 50 },
                { label: '交易次数', value: trading.total_trades, positive: true }
            ];

            metrics.forEach(metric => {
                const colorClass = metric.positive ? 'success' : 'danger';
                html += `<div style="background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-sm); padding: 12px; text-align: center;">
                    <div style="color: var(--text-secondary); font-size: 0.8rem; margin-bottom: 4px;">${metric.label}</div>
                    <div style="color: var(--text-primary); font-weight: 600; font-size: 1.1rem;">${metric.value}</div>
                </div>`;
            });

            html += '</div>';

            // 回测期间信息
            html += `<div style="background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-sm); padding: 16px; margin-top: 16px;">
                <h4 style="color: var(--text-primary); margin-bottom: 12px;">回测详情</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; color: var(--text-secondary); font-size: 0.9rem;">
                    <div>开始日期: ${period.start_date}</div>
                    <div>结束日期: ${period.end_date}</div>
                    <div>交易日数: ${period.trading_days}</div>
                    <div>最终价值: ¥${data.final_portfolio_value.toLocaleString()}</div>
                </div>
            </div>`;

            document.getElementById('backtest-result').innerHTML = html;
        }

        // 加载回测历史
        async function loadBacktestHistory() {
            document.getElementById('backtest-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 加载中...</div>';

            try {
                const response = await fetch(`${API_BASE}/backtest/history`);
                const result = await response.json();

                if (result.success) {
                    displayBacktestHistory(result.data);
                } else {
                    document.getElementById('backtest-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('backtest-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 显示回测历史
        function displayBacktestHistory(history) {
            if (history.length === 0) {
                document.getElementById('backtest-result').innerHTML =
                    '<div style="color: var(--text-muted); text-align: center; padding: 40px;">暂无回测历史记录</div>';
                return;
            }

            let html = '<div class="success"><i class="fas fa-history"></i> 回测历史记录</div>';
            html += '<div style="max-height: 400px; overflow-y: auto; margin-top: 12px;">';

            history.forEach(record => {
                html += `<div style="background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-sm); padding: 16px; margin-bottom: 12px;">
                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">${record.model_name}</h4>
                    <div style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 8px;">
                        ${record.start_date} 到 ${record.end_date}
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px; font-size: 0.8rem;">
                        <span>收益: ${record.total_return}%</span>
                        <span>年化: ${record.annual_return}%</span>
                        <span>回撤: ${record.max_drawdown}%</span>
                        <span>夏普: ${record.sharpe_ratio}</span>
                        <span>胜率: ${record.win_rate}%</span>
                    </div>
                    <div style="color: var(--text-muted); font-size: 0.7rem; margin-top: 8px;">
                        创建时间: ${record.created_at}
                    </div>
                </div>`;
            });

            html += '</div>';
            document.getElementById('backtest-result').innerHTML = html;
        }

        // ==================== 预测管理功能 ====================

        // 执行预测
        async function runPrediction() {
            const modelName = document.getElementById('prediction-model-select').value;
            const stocksInput = document.getElementById('prediction-stocks').value;

            if (!stocksInput) {
                showNotification('请输入股票代码', 'warning');
                return;
            }

            const stockCodes = stocksInput.split(',').map(s => s.trim());

            document.getElementById('predictions-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 预测中...</div>';

            try {
                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model_name: modelName,
                        stock_codes: stockCodes
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayPredictionResults(result.data);
                    showNotification('预测完成', 'success');
                } else {
                    document.getElementById('predictions-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 预测失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('predictions-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 加载预测结果
        async function loadPredictions() {
            document.getElementById('predictions-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 加载中...</div>';

            try {
                const response = await fetch(`${API_BASE}/predictions`);
                const result = await response.json();

                if (result.success) {
                    displayPredictionResults(result.data);
                } else {
                    document.getElementById('predictions-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('predictions-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 显示预测结果
        function displayPredictionResults(predictions) {
            if (!predictions || predictions.length === 0) {
                document.getElementById('predictions-result').innerHTML =
                    '<div style="color: var(--text-muted); text-align: center; padding: 40px;">暂无预测结果</div>';
                return;
            }

            let html = '<div class="success"><i class="fas fa-magic"></i> 预测结果</div>';
            html += '<div style="max-height: 300px; overflow-y: auto; margin-top: 12px;">';

            predictions.forEach(prediction => {
                const confidence = (prediction.confidence * 100).toFixed(1);
                const signalClass = prediction.signal > 0 ? 'success' : prediction.signal < 0 ? 'danger' : 'warning';
                const signalText = prediction.signal > 0 ? '看涨' : prediction.signal < 0 ? '看跌' : '中性';
                const signalIcon = prediction.signal > 0 ? 'arrow-up' : prediction.signal < 0 ? 'arrow-down' : 'minus';

                html += `<div style="background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-sm); padding: 16px; margin-bottom: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="color: var(--text-primary); font-weight: 600;">${prediction.stock_code}</span>
                        <span class="status-indicator ${signalClass === 'success' ? 'active' : signalClass === 'danger' ? 'inactive' : 'warning'}">
                            <i class="fas fa-${signalIcon}"></i>
                            ${signalText}
                        </span>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px; font-size: 0.8rem; color: var(--text-secondary);">
                        <span>模型: ${prediction.model}</span>
                        <span>置信度: ${confidence}%</span>
                        <span>预测时间: ${prediction.prediction_time}</span>
                    </div>
                </div>`;
            });

            html += '</div>';
            document.getElementById('predictions-result').innerHTML = html;
        }

        // ==================== 调度器管理功能 ====================

        // 更新调度器状态
        async function updateSchedulerStatus() {
            try {
                const response = await fetch(`${API_BASE}/scheduler/status`);
                const result = await response.json();

                if (result.success) {
                    const status = result.data;

                    // 更新调度器状态
                    const schedulerStatus = document.getElementById('scheduler-status');
                    if (status.running) {
                        schedulerStatus.className = 'status-indicator active';
                        schedulerStatus.innerHTML = '<i class="fas fa-play-circle"></i> 运行中';
                    } else {
                        schedulerStatus.className = 'status-indicator inactive';
                        schedulerStatus.innerHTML = '<i class="fas fa-stop-circle"></i> 已停止';
                    }

                    // 更新定时任务状态
                    const morningStatus = document.getElementById('morning-schedule-status');
                    const afternoonStatus = document.getElementById('afternoon-schedule-status');

                    if (status.schedules) {
                        morningStatus.className = 'status-indicator active';
                        morningStatus.innerHTML = '<i class="fas fa-clock"></i> 已设置';

                        afternoonStatus.className = 'status-indicator active';
                        afternoonStatus.innerHTML = '<i class="fas fa-clock"></i> 已设置';
                    } else {
                        morningStatus.className = 'status-indicator warning';
                        morningStatus.innerHTML = '<i class="fas fa-clock"></i> 未设置';

                        afternoonStatus.className = 'status-indicator warning';
                        afternoonStatus.innerHTML = '<i class="fas fa-clock"></i> 未设置';
                    }
                }
            } catch (error) {
                console.error('更新调度器状态失败:', error);
            }
        }

        // 启动调度器
        async function startScheduler() {
            document.getElementById('scheduler-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 启动中...</div>';

            try {
                const response = await fetch(`${API_BASE}/scheduler/start`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('scheduler-result').innerHTML =
                        '<div class="success"><i class="fas fa-check"></i> 调度器启动成功</div>';
                    updateSchedulerStatus();
                    showNotification('调度器启动成功', 'success');
                } else {
                    document.getElementById('scheduler-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 启动失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('scheduler-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // 停止调度器
        async function stopScheduler() {
            document.getElementById('scheduler-result').innerHTML =
                '<div class="loading"><i class="fas fa-spinner"></i> 停止中...</div>';

            try {
                const response = await fetch(`${API_BASE}/scheduler/stop`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('scheduler-result').innerHTML =
                        '<div class="success"><i class="fas fa-check"></i> 调度器停止成功</div>';
                    updateSchedulerStatus();
                    showNotification('调度器停止成功', 'success');
                } else {
                    document.getElementById('scheduler-result').innerHTML =
                        `<div class="error"><i class="fas fa-times"></i> 停止失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('scheduler-result').innerHTML =
                    `<div class="error"><i class="fas fa-times"></i> 网络错误: ${error.message}</div>`;
            }
        }

        // ==================== 通用功能 ====================

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 24px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;

            // 设置背景色
            switch (type) {
                case 'success':
                    notification.style.background = 'var(--success-gradient)';
                    break;
                case 'warning':
                    notification.style.background = 'var(--warning-gradient)';
                    break;
                case 'error':
                    notification.style.background = 'var(--danger-gradient)';
                    break;
                default:
                    notification.style.background = 'var(--primary-gradient)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
