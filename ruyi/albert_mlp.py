import tensorflow as tf
from transformers import TFAlbertPreTrainedModel, TFAlbertMainLayer
from transformers.modeling_tf_utils import get_initializer

class TFAlbertForSequenceClassificationMLP(TFAlbertPreTrainedModel):
    """
    ALBERT模型，但将分类头部替换为4层MLP
    原始实现只有一个简单的dense层作为分类器
    这个实现添加了4层dense层，每两层之间有一个非线性激活函数
    """
    
    def __init__(self, config, *inputs, **kwargs):
        super().__init__(config, *inputs, **kwargs)
        self.num_labels = config.num_labels
        
        # 主ALBERT模型
        self.albert = TFAlbertMainLayer(config, name="albert")
        
        # MLP分类器 - 4层dense，每两层之间有激活函数
        # 第一层 - 从隐藏层尺寸到隐藏层尺寸
        self.dense1 = tf.keras.layers.Dense(
            config.hidden_size,
            kernel_initializer=get_initializer(config.initializer_range),
            name="dense1",
        )
        # 第一个激活函数
        self.activation1 = tf.keras.layers.Activation("gelu", name="activation1")
        
        # 第二层 - 保持隐藏层尺寸不变
        self.dense2 = tf.keras.layers.Dense(
            config.hidden_size,
            kernel_initializer=get_initializer(config.initializer_range),
            name="dense2",
        )
        # 第二个激活函数
        self.activation2 = tf.keras.layers.Activation("gelu", name="activation2")
        
        # 第三层 - 保持隐藏层尺寸不变
        self.dense3 = tf.keras.layers.Dense(
            config.hidden_size,
            kernel_initializer=get_initializer(config.initializer_range),
            name="dense3",
        )
        # 第三个激活函数
        self.activation3 = tf.keras.layers.Activation("gelu", name="activation3")
        
        # 输出层 - 从隐藏层尺寸到类别数
        self.classifier = tf.keras.layers.Dense(
            self.num_labels,
            kernel_initializer=get_initializer(config.initializer_range),
            name="classifier",
        )
        
        # 添加dropout以减少过拟合
        self.dropout = tf.keras.layers.Dropout(config.hidden_dropout_prob)

    def call(
        self,
        input_ids=None,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        inputs_embeds=None,
        output_attentions=None,
        output_hidden_states=None,
        labels=None,
        training=False,
    ):
        """
        执行前向传播并计算损失（如果提供了标签）
        参数顺序和命名与transformers 3.4.0版本保持一致
        """
        # 调用ALBERT基础模型
        outputs = self.albert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            training=training,
        )

        # 获取[CLS]对应的池化输出
        pooled_output = outputs[1]
        
        # 应用dropout
        pooled_output = self.dropout(pooled_output, training=training)
        
        # 应用4层MLP
        # 第一层
        hidden1 = self.dense1(pooled_output)
        hidden1 = self.activation1(hidden1)
        hidden1 = self.dropout(hidden1, training=training)
        
        # 第二层
        hidden2 = self.dense2(hidden1)
        hidden2 = self.activation2(hidden2)
        hidden2 = self.dropout(hidden2, training=training)
        
        # 第三层
        hidden3 = self.dense3(hidden2)
        hidden3 = self.activation3(hidden3)
        hidden3 = self.dropout(hidden3, training=training)
        
        # 输出层
        logits = self.classifier(hidden3)

        # 计算损失（如果提供了标签）
        loss = None
        if labels is not None:
            if self.num_labels == 1:
                #  回归任务
                loss = tf.keras.losses.mean_squared_error(tf.reshape(labels, (-1,)), tf.reshape(logits, (-1,)))
            else:
                # 分类任务
                loss = tf.keras.losses.sparse_categorical_crossentropy(
                    labels, logits, from_logits=True
                )
                loss = tf.reduce_mean(loss)

        # 构建输出元组
        hidden_states = outputs[2] if len(outputs) > 2 else None
        attentions = outputs[3] if len(outputs) > 3 else None
        
        # 标准输出格式
        output = (logits,)
        if output_hidden_states:
            output = output + (hidden_states,)
        if output_attentions:
            output = output + (attentions,)
            
        # 添加loss作为第一个元素（如果有）
        return ((loss,) + output) if loss is not None else output 