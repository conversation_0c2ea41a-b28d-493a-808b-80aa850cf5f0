============== Epoch 1 验证集监控 ==============

前30个样本的详细信息:
样本 1:
  输入(input_ids): [ 101 1132 1356  117 1132 1356  117 1079 1063 6235]... (长度: 86)
  标签(label): 1
  logits: [ 0.00767883 -0.11053387]
  预测(prediction): 0
  是否正确: False

样本 2:
  输入(input_ids): [ 101  704 1744 5093 3064 7509 1447 3360 4506 3297]... (长度: 512)
  标签(label): 1
  logits: [0.04981405 0.09695183]
  预测(prediction): 1
  是否正确: True

样本 3:
  输入(input_ids): [ 101 2207 2110 5307 1995  117 8701 2207 5307 1995]... (长度: 121)
  标签(label): 0
  logits: [ 0.01980296 -0.19759002]
  预测(prediction): 0
  是否正确: True

样本 4:
  输入(input_ids): [ 101 7946 2376 4125 2894 4696 2141 6228 7574  117]... (长度: 512)
  标签(label): 1
  logits: [ 0.21615747 -0.2677988 ]
  预测(prediction): 0
  是否正确: False

样本 5:
  输入(input_ids): [ 101 3589  978 3613 3342 5166 3297 3173 3867 2622]... (长度: 512)
  标签(label): 0
  logits: [ 0.12997648 -0.09453388]
  预测(prediction): 0
  是否正确: True

样本 6:
  输入(input_ids): [ 101 1259 2094 7672 1928 2582  720 5892 1348 6763]... (长度: 512)
  标签(label): 0
  logits: [ 0.02636001 -0.08930694]
  预测(prediction): 0
  是否正确: True

样本 7:
  输入(input_ids): [  101  2582   720  2990  1285  2834  7795   966  1168 13251]... (长度: 512)
  标签(label): 0
  logits: [ 0.16403207 -0.18655139]
  预测(prediction): 0
  是否正确: True

样本 8:
  输入(input_ids): [ 101 3351 2255 2773 2514 2015 2015 1070  117  823]... (长度: 512)
  标签(label): 0
  logits: [-0.00346535 -0.27380085]
  预测(prediction): 0
  是否正确: True

样本 9:
  输入(input_ids): [ 101 7716 1520 3118 5296 7231 6814  749  833 2582]... (长度: 512)
  标签(label): 0
  logits: [ 0.10081419 -0.2160999 ]
  预测(prediction): 0
  是否正确: True

样本 10:
  输入(input_ids): [  101 11806  9136   117  7770  5277   924  2128  2360   117]... (长度: 512)
  标签(label): 0
  logits: [ 0.11338247 -0.19628784]
  预测(prediction): 0
  是否正确: True

样本 11:
  输入(input_ids): [ 101 3297 2487 1920 5554 4867 5439 2360  117 6381]... (长度: 512)
  标签(label): 0
  logits: [ 0.09015755 -0.21510057]
  预测(prediction): 0
  是否正确: True

样本 12:
  输入(input_ids): [ 101 7222 1750 7608 3726 2833 7509 1730 6579 1947]... (长度: 512)
  标签(label): 0
  logits: [ 0.23056364 -0.21286003]
  预测(prediction): 0
  是否正确: True

样本 13:
  输入(input_ids): [  101 10245  8329  8169 11188  4991  4989  4607  5587  5592]... (长度: 512)
  标签(label): 0
  logits: [ 0.21075396 -0.27846506]
  预测(prediction): 0
  是否正确: True

样本 14:
  输入(input_ids): [ 101  153 2600  117  153 2600  117 2173 2147 2553]... (长度: 227)
  标签(label): 1
  logits: [ 0.07522512 -0.23366305]
  预测(prediction): 0
  是否正确: False

样本 15:
  输入(input_ids): [ 101 1728  711  671  702 4275 3667 4692  749 3146]... (长度: 211)
  标签(label): 0
  logits: [ 0.05751158 -0.20966855]
  预测(prediction): 0
  是否正确: True

样本 16:
  输入(input_ids): [ 101 2858 3428  123 1426 5439 4267 2221  860 3867]... (长度: 512)
  标签(label): 0
  logits: [ 0.09892496 -0.22155692]
  预测(prediction): 0
  是否正确: True

样本 17:
  输入(input_ids): [ 101 1310 6132 4511 3621 6825 2384  117 4995 5735]... (长度: 512)
  标签(label): 0
  logits: [ 0.14540456 -0.25579682]
  预测(prediction): 0
  是否正确: True

样本 18:
  输入(input_ids): [ 101 2143 4289 3189 2382 3403 7579  117 4510 2512]... (长度: 512)
  标签(label): 1
  logits: [ 0.1617647 -0.2507802]
  预测(prediction): 0
  是否正确: False

样本 19:
  输入(input_ids): [ 101  122  118  124 2259 1036 4997 6663 5659 2408]... (长度: 129)
  标签(label): 1
  logits: [ 0.0404484  -0.13044071]
  预测(prediction): 0
  是否正确: False

样本 20:
  输入(input_ids): [ 101 4635 6137 6136 7270 6153  117 1331 2419  727]... (长度: 512)
  标签(label): 0
  logits: [ 0.2806019 -0.3445007]
  预测(prediction): 0
  是否正确: True

样本 21:
  输入(input_ids): [ 101 2476 7959 4696 4638 3221 5307 1073 3736 3851]... (长度: 512)
  标签(label): 1
  logits: [ 0.04763861 -0.17365696]
  预测(prediction): 0
  是否正确: False

样本 22:
  输入(input_ids): [ 101  758 4130 5544  117 4374 2207 7946 1386 7434]... (长度: 512)
  标签(label): 0
  logits: [-0.04144691 -0.1637871 ]
  预测(prediction): 0
  是否正确: True

样本 23:
  输入(input_ids): [ 101 3736 5722 1298 6858 2157 5293  117 5988 6501]... (长度: 512)
  标签(label): 0
  logits: [ 0.16508245 -0.22470984]
  预测(prediction): 0
  是否正确: True

样本 24:
  输入(input_ids): [ 101 4095 7345 2356 1963 2157 2571 2949 6983 2421]... (长度: 512)
  标签(label): 1
  logits: [ 0.2608209  -0.21686561]
  预测(prediction): 0
  是否正确: False

样本 25:
  输入(input_ids): [ 101  730 6810  722 1599 6913 6435 1141  117 4511]... (长度: 512)
  标签(label): 0
  logits: [ 0.13676244 -0.120405  ]
  预测(prediction): 0
  是否正确: True

样本 26:
  输入(input_ids): [ 101  791 1921 3209 3209 3221 2769 4638 2042 4851]... (长度: 512)
  标签(label): 0
  logits: [ 0.09718815 -0.24571988]
  预测(prediction): 0
  是否正确: True

样本 27:
  输入(input_ids): [ 101 7270 3309 1391 4800 7000 3705 3714 1414 3419]... (长度: 126)
  标签(label): 0
  logits: [ 0.04870696 -0.11976006]
  预测(prediction): 0
  是否正确: True

样本 28:
  输入(input_ids): [ 101 4457 8280 4649 5502  117 1366 4811 3297 1962]... (长度: 202)
  标签(label): 1
  logits: [ 0.08925267 -0.2245557 ]
  预测(prediction): 0
  是否正确: False

样本 29:
  输入(input_ids): [ 101 4640 4377 1923  782 3815 2145 3819 6132  117]... (长度: 512)
  标签(label): 1
  logits: [ 0.17979509 -0.27735385]
  预测(prediction): 0
  是否正确: False

样本 30:
  输入(input_ids): [ 101 1075 4495 1901  117  704 5790 4203 4215 1901]... (长度: 370)
  标签(label): 1
  logits: [ 0.24682298 -0.20314524]
  预测(prediction): 0
  是否正确: False


验证集统计 (随机采样的32个样本):
总体准确率: 0.6562
标签分布: 类别0: 20, 类别1: 12
预测分布: 类别0: 31, 类别1: 1
类别0准确率: 1.0000, 类别1准确率: 0.0833
Logits均值: [ 0.12046729 -0.19787794]
Logits标准差: [0.08287924 0.07843316]
连续相同预测的比例: 0.9355
Epoch 2/10
257/257 [==============================] - 221s 860ms/step - loss: 0.8130 - sparse_categorical_accuracy: 0.6129
Epoch 2 - Metrics and model saved. Val Loss: 0.8357, Val Acc: 0.5888

============== Epoch 2 验证集监控 ==============

前30个样本的详细信息:
样本 1:
  输入(input_ids): [ 101  912 1164 2421 3119 7213 5143 5320  117 2861]... (长度: 512)
  标签(label): 1
  logits: [ 0.24552175 -0.32920825]
  预测(prediction): 0
  是否正确: False

样本 2:
  输入(input_ids): [ 101 3172 1132 1936 1957 7490  117 3172 1132 1936]... (长度: 512)
  标签(label): 0
  logits: [ 0.27666816 -0.43127492]
  预测(prediction): 0
  是否正确: True

样本 3:
  输入(input_ids): [ 101  122  118  124 2259 1036 4997 6663 5659 2408]... (长度: 129)
  标签(label): 1
  logits: [ 0.13150999 -0.13049814]
  预测(prediction): 0
  是否正确: False

样本 4:
  输入(input_ids): [ 101 1282  674 3805 2861 6428 3082  868  743 1908]... (长度: 512)
  标签(label): 0
  logits: [ 0.25900567 -0.39545095]
  预测(prediction): 0
  是否正确: True

样本 5:
  输入(input_ids): [ 101 7350 4904 1995  117 6651 7030 2805  117 4658]... (长度: 512)
  标签(label): 1
  logits: [ 0.2145414 -0.3893343]
  预测(prediction): 0
  是否正确: False

样本 6:
  输入(input_ids): [ 101 5918 7911 2292 4696 1399 3300 7434 2135 2146]... (长度: 512)
  标签(label): 0
  logits: [ 0.22085653 -0.39401543]
  预测(prediction): 0
  是否正确: True

样本 7:
  输入(input_ids): [ 101 1290 5812 1894  117  679  711 6443 5445  868]... (长度: 512)
  标签(label): 0
  logits: [ 0.24456023 -0.28568092]
  预测(prediction): 0
  是否正确: True

样本 8:
  输入(input_ids): [ 101 7306 2042 5439  845 3221 6498 7305  117 1159]... (长度: 429)
  标签(label): 0
  logits: [ 0.32012507 -0.30247688]
  预测(prediction): 0
  是否正确: True

样本 9:
  输入(input_ids): [ 101 1409  707 1921  678  117 7566 1046  168 8311]... (长度: 512)
  标签(label): 0
  logits: [ 0.23286328 -0.30129126]
  预测(prediction): 0
  是否正确: True

样本 10:
  输入(input_ids): [ 101 2382 2548 7959 1814 1277 3719 2128 2207 2110]... (长度: 79)
  标签(label): 1
  logits: [ 0.14592057 -0.11122293]
  预测(prediction): 0
  是否正确: False

样本 11:
  输入(input_ids): [ 101 4649 2209 4294 3753 1923 6825 6571 1946 1225]... (长度: 512)
  标签(label): 1
  logits: [ 0.26441845 -0.37486807]
  预测(prediction): 0
  是否正确: False

样本 12:
  输入(input_ids): [ 101 1959 7987  117 1678 1678 1678 2207 1759 6486]... (长度: 512)
  标签(label): 1
  logits: [ 0.2640395  -0.27739093]
  预测(prediction): 0
  是否正确: False

样本 13:
  输入(input_ids): [ 101 5574 7000 5850 1301 4638  976 3791 1920 1059]... (长度: 382)
  标签(label): 0
  logits: [ 0.30200025 -0.32505473]
  预测(prediction): 0
  是否正确: True

样本 14:
  输入(input_ids): [ 101 1220 4514  117 1220 4514  117 6564  727 5988]... (长度: 512)
  标签(label): 0
  logits: [0.19870599 0.00876427]
  预测(prediction): 0
  是否正确: True

样本 15:
  输入(input_ids): [ 101  671 6662 3300  872  117  691 1266 6356 2175]... (长度: 512)
  标签(label): 1
  logits: [ 0.2839226 -0.3099089]
  预测(prediction): 0
  是否正确: False

样本 16:
  输入(input_ids): [ 101 4007 3736 1093 6588 2356 1767 7353 6818 7481]... (长度: 92)
  标签(label): 1
  logits: [ 0.27392876 -0.16377738]
  预测(prediction): 0
  是否正确: False

样本 17:
  输入(input_ids): [ 101 3342 5428 5709  117 5523 1520 3300 3336  117]... (长度: 512)
  标签(label): 0
  logits: [ 0.12992933 -0.35411897]
  预测(prediction): 0
  是否正确: True

样本 18:
  输入(input_ids): [ 101  150 8204 8171 1333 4635 7458  117 1036 4997]... (长度: 512)
  标签(label): 1
  logits: [ 0.3153653 -0.3064608]
  预测(prediction): 0
  是否正确: False

样本 19:
  输入(input_ids): [ 101 6932 2626  117 4263 3187  817  117 1957 2094]... (长度: 323)
  标签(label): 0
  logits: [ 0.15704626 -0.15544496]
  预测(prediction): 0
  是否正确: True

样本 20:
  输入(input_ids): [ 101 5318 1456 7890 5556  130  119  130 1947 7623]... (长度: 512)
  标签(label): 0
  logits: [ 0.22034849 -0.03952254]
  预测(prediction): 0
  是否正确: True

样本 21:
  输入(input_ids): [ 101 1453 2411 5401 7608  117 1453 2411 1367 7252]... (长度: 512)
  标签(label): 0
  logits: [ 0.3202215  -0.33189476]
  预测(prediction): 0
  是否正确: True

样本 22:
  输入(input_ids): [ 101 1383 4392 1391 6028 5130 1333 6228 7574  117]... (长度: 512)
  标签(label): 0
  logits: [ 0.2940846  -0.35492352]
  预测(prediction): 0
  是否正确: True

样本 23:
  输入(input_ids): [ 101 1290 2487 3198  807 2408 1767  117 1290 2487]... (长度: 512)
  标签(label): 0
  logits: [ 0.14886189 -0.32148796]
  预测(prediction): 0
  是否正确: True

样本 24:
  输入(input_ids): [ 101 2414 1807  117 2608 6898 3496 3473 2414 1807]... (长度: 64)
  标签(label): 1
  logits: [ 0.04240952 -0.2062926 ]
  预测(prediction): 0
  是否正确: False

样本 25:
  输入(input_ids): [ 101 7440 1092 3018 5010 1399 1767 7481  117 1453]... (长度: 512)
  标签(label): 1
  logits: [ 0.23291361 -0.2828599 ]
  预测(prediction): 0
  是否正确: False

样本 26:
  输入(input_ids): [ 101 3040 2209 1814 6983 2421  117 3040 2209 1814]... (长度: 512)
  标签(label): 1
  logits: [ 0.14159417 -0.39596298]
  预测(prediction): 0
  是否正确: False

样本 27:
  输入(input_ids): [ 101 2769 4638 6225 2512 2845 1440  102]... (长度: 8)
  标签(label): 1
  logits: [-0.02313652 -0.01114132]
  预测(prediction): 1
  是否正确: True

样本 28:
  输入(input_ids): [ 101 1599  676 5523 7506 1744 5632 1221 4171 5489]... (长度: 512)
  标签(label): 0
  logits: [ 0.3072033  -0.38714945]
  预测(prediction): 0
  是否正确: True

样本 29:
  输入(input_ids): [ 101 1290 3217 5816 3235 2190 3683 1745  117 2207]... (长度: 270)
  标签(label): 1
  logits: [ 0.23869641 -0.24650598]
  预测(prediction): 0
  是否正确: False

样本 30:
  输入(input_ids): [ 101 2828 2157 3119 2896 2397 1112 4638 4964 7305]... (长度: 512)
  标签(label): 0
  logits: [ 0.3342413 -0.3254312]
  预测(prediction): 0
  是否正确: True


验证集统计 (随机采样的32个样本):
总体准确率: 0.5625
标签分布: 类别0: 17, 类别1: 15
预测分布: 类别0: 31, 类别1: 1
类别0准确率: 1.0000, 类别1准确率: 0.0667
Logits均值: [ 0.22142704 -0.25706923]
Logits标准差: [0.08132648 0.13262874]
连续相同预测的比例: 0.9355
Epoch 3/10
257/257 [==============================] - 1s 2ms/step - loss: 0.7769 - sparse_categorical_accuracy: 0.6670
Epoch 3 - Metrics and model saved. Val Loss: 0.8112, Val Acc: 0.6125

============== Epoch 3 验证集监控 ==============

前30个样本的详细信息:
样本 1:
  输入(input_ids): [ 101 1599 1995 4156  706  117 1599 1995 4156  706]... (长度: 512)
  标签(label): 1
  logits: [ 0.1690228  -0.29708055]
  预测(prediction): 0
  是否正确: False

样本 2:
  输入(input_ids): [ 101 3736 5847 1744 7354 1952 3144 3683 6612 1400]... (长度: 512)
  标签(label): 1
  logits: [ 0.20469126 -0.26354173]
  预测(prediction): 0
  是否正确: False

样本 3:
  输入(input_ids): [ 101  673 2428 4958 7313 6158 3284 1045  117 4510]... (长度: 512)
  标签(label): 0
  logits: [ 0.30488205 -0.33446684]
  预测(prediction): 0
  是否正确: True

样本 4:
  输入(input_ids): [ 101 2769 3221 2255 5790 8107  117 5653 6831 2357]... (长度: 512)
  标签(label): 1
  logits: [ 0.27280024 -0.38965556]
  预测(prediction): 0
  是否正确: False

样本 5:
  输入(input_ids): [ 101 5918 7911 2292 4696 1399 3300 7434 2135 2146]... (长度: 512)
  标签(label): 0
  logits: [ 0.23360111 -0.3962194 ]
  预测(prediction): 0
  是否正确: True

样本 6:
  输入(input_ids): [ 101  691 1266 7433 1995 3297 3173 1726 2418  117]... (长度: 512)
  标签(label): 0
  logits: [ 0.26497573 -0.25848207]
  预测(prediction): 0
  是否正确: True

样本 7:
  输入(input_ids): [ 101 2467 1921  117 1297 5831 1920 1968 4994 3221]... (长度: 512)
  标签(label): 0
  logits: [ 0.18144485 -0.28601235]
  预测(prediction): 0
  是否正确: True

样本 8:
  输入(input_ids): [ 101 3956 1062 3777 1920 3428 3330 4989 5408  117]... (长度: 512)
  标签(label): 0
  logits: [ 0.23904514 -0.29184738]
  预测(prediction): 0
  是否正确: True

样本 9:
  输入(input_ids): [ 101 1914 5489 3490 4289 2821 1355 1825 1765 4684]... (长度: 395)
  标签(label): 0
  logits: [ 0.26977676 -0.32544574]
  预测(prediction): 0
  是否正确: True

样本 10:
  输入(input_ids): [ 101 3342 5428 5709  117 5523 1520 3300 3336  117]... (长度: 512)
  标签(label): 0
  logits: [ 0.16712314 -0.3620418 ]
  预测(prediction): 0
  是否正确: True

样本 11:
  输入(input_ids): [ 101 7350 4904 1995  117 6651 7030 2805  117 4658]... (长度: 512)
  标签(label): 1
  logits: [ 0.23260312 -0.40706414]
  预测(prediction): 0
  是否正确: False

样本 12:
  输入(input_ids): [ 101 3736 5722 1298 6858 2157 5293  117 5988 6501]... (长度: 512)
  标签(label): 0
  logits: [ 0.265345  -0.3563972]
  预测(prediction): 0
  是否正确: True

样本 13:
  输入(input_ids): [ 101 5468 1394 1744 1920  833 1392 1744  757 2601]... (长度: 188)
  标签(label): 1
  logits: [ 0.09424497 -0.15251079]
  预测(prediction): 0
  是否正确: False

样本 14:
  输入(input_ids): [ 101 3805 2861 4448  762 2582  720 4381  794 7439]... (长度: 512)
  标签(label): 0
  logits: [ 0.22336589 -0.2777482 ]
  预测(prediction): 0
  是否正确: True

样本 15:
  输入(input_ids): [ 101 7357 1724 3862 2970 2521 7566 2193 2658 5688]... (长度: 512)
  标签(label): 0
  logits: [ 0.36434177 -0.27712566]
  预测(prediction): 0
  是否正确: True

样本 16:
  输入(input_ids): [ 101  671 2233 3696 6117 3890  704 3389 1139 7962]... (长度: 512)
  标签(label): 0
  logits: [ 0.2412805  -0.32121673]
  预测(prediction): 0
  是否正确: True

样本 17:
  输入(input_ids): [ 101 4767 3534 4767  117 1921 7716 1139 4385 7564]... (长度: 512)
  标签(label): 1
  logits: [ 0.18356323 -0.07640722]
  预测(prediction): 0
  是否正确: False

样本 18:
  输入(input_ids): [ 101 1743 1993 5276  833  117 1962 7890 6131 2336]... (长度: 512)
  标签(label): 0
  logits: [ 0.08672871 -0.2348418 ]
  预测(prediction): 0
  是否正确: True

样本 19:
  输入(input_ids): [ 101 3862 2419 2937  117 7716 2767 1730  117 5344]... (长度: 428)
  标签(label): 0
  logits: [ 0.28805622 -0.34540156]
  预测(prediction): 0
  是否正确: True

样本 20:
  输入(input_ids): [ 101 7490 2094 1957 3621  117 5799 5799 5799 5799]... (长度: 512)
  标签(label): 0
  logits: [ 0.25846326 -0.34061036]
  预测(prediction): 0
  是否正确: True

样本 21:
  输入(input_ids): [ 101 1420 7599 4638 6014  117 6484 2456 5705 6397]... (长度: 512)
  标签(label): 1
  logits: [ 0.27345294 -0.32068184]
  预测(prediction): 0
  是否正确: False

样本 22:
  输入(input_ids): [ 101 1947 4385 3322 3221  784  720 2692 2590 1377]... (长度: 512)
  标签(label): 0
  logits: [ 0.25544262 -0.3162872 ]
  预测(prediction): 0
  是否正确: True

样本 23:
  输入(input_ids): [ 101 4374 6662 5425  117  676 6235 3828 1525  702]... (长度: 512)
  标签(label): 0
  logits: [ 0.24360652 -0.373505  ]
  预测(prediction): 0
  是否正确: True

样本 24:
  输入(input_ids): [ 101 7481  117 5106 7350 1987 2849 5384 5106 1730]... (长度: 512)
  标签(label): 1
  logits: [ 0.28355482 -0.1837654 ]
  预测(prediction): 0
  是否正确: False

样本 25:
  输入(input_ids): [ 101 3171 6843 7471 3217 4263 7231  782 2130 3146]... (长度: 512)
  标签(label): 1
  logits: [ 0.27476653 -0.326697  ]
  预测(prediction): 0
  是否正确: False

样本 26:
  输入(input_ids): [ 101 2507 1520 1343 3952 3807 7667 2823 1157 2094]... (长度: 512)
  标签(label): 1
  logits: [ 0.28940365 -0.33385742]
  预测(prediction): 0
  是否正确: False

样本 27:
  输入(input_ids): [ 101 2477 4522  722 1744 4638 4263  714  692 5018]... (长度: 512)
  标签(label): 0
  logits: [ 0.2799693  -0.24655049]
  预测(prediction): 0
  是否正确: True

样本 28:
  输入(input_ids): [ 101 7004  740 3696 6469 1333 1548 2130 3146 4276]... (长度: 512)
  标签(label): 0
  logits: [ 0.21615796 -0.2618329 ]
  预测(prediction): 0
  是否正确: True

样本 29:
  输入(input_ids): [ 101 3918 1766 3187  782 3322 6134 4028 1744 1912]... (长度: 512)
  标签(label): 1
  logits: [ 0.16246165 -0.34293786]
  预测(prediction): 0
  是否正确: False

样本 30:
  输入(input_ids): [ 101 4767 4242 3959  117 7946 4344 1762 4344 4518]... (长度: 512)
  标签(label): 1
  logits: [ 0.21076217 -0.2506546 ]
  预测(prediction): 0
  是否正确: False


验证集统计 (随机采样的32个样本):
总体准确率: 0.5938
标签分布: 类别0: 19, 类别1: 13
预测分布: 类别0: 32, 类别1: 0
类别0准确率: 1.0000, 类别1准确率: 0.0000
Logits均值: [ 0.23144606 -0.29546103]
Logits标准差: [0.05839972 0.07801647]
连续相同预测的比例: 1.0000

警告: 模型对所有样本预测为同一类别!
预测的类别: 0
所有logits的平均值: [ 0.23144606 -0.29546103]
logits的最小值: [ 0.08672871 -0.40706414]
logits的最大值: [ 0.36434177 -0.07640722]
训练完成。