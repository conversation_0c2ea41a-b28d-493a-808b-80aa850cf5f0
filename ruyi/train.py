import tensorflow as tf
import transformers
print(f"transformers 库的版本为: {transformers.__version__}")
print(f"TensorFlow 版本为: {tf.__version__}")
import os

# =====================
# 设置随机种子，保证实验可复现
# =====================
seed = 42
os.environ['PYTHONHASHSEED'] = str(seed)  # 控制Python哈希种子
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 只显示TF的warning和error

# =====================
# 导入常用库
# =====================
import random
import numpy as np
import pandas as pd
import datetime
import logging
import time
import matplotlib.image as mpimg 
import matplotlib.pyplot as plt 
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.metrics import roc_auc_score, log_loss, f1_score, accuracy_score, roc_curve, precision_score, recall_score
import tensorflow as tf
from transformers import BertTokenizer, TFAlbertForSequenceClassification, BertTokenizerFast
import tensorflow.keras.backend as K
from BaseModel import gruModel 
# from bert import BertModel
from cc_utils import get_dataloader
from functools import partial 

# =====================
# 再次设置所有随机种子，保证各库一致性
# =====================
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

# =====================
# 禁用TensorFlow日志，减少无关输出
# =====================
logging.getLogger('tensorflow').disabled = True
logging.getLogger('tensorflow').setLevel(logging.ERROR)

# =====================
# 打印TF和GPU信息，便于调试
# =====================
print(tf.__version__)
print(tf.config.list_physical_devices('GPU'))
print('Using TensorFlow version',tf.__version__)

# =====================
# 模型与数据参数配置
# =====================
seq_len = 512  # 输入序列最大长度
batch_size = 16  # 单卡批量大小
label_name = 'label'  # 标签字段名
mode = "test"  # 当前模式（可切换train/test）

# =====================
# 数据集字段定义
# =====================
basic_cols = ['user_id', 'date']  # 基础信息列
feature_cols = ['input_ids','attention_mask']  # BERT输入特征列

# =====================
# 数据筛选条件（未直接用到）
# =====================
train_use = "train_flag == 'train'" 
valid_use = "train_flag == 'test'" 

# =====================
# TFRecord数据路径
# =====================
train_tfrecord_path = f"hdfs://haruna/user/tiger/xql/liuruyi/bmodel/data/search_albert_ids_bcard_90d_1000w_valid_tfrecord" 
valid_tfrecord_path = f"hdfs://haruna/user/tiger/xql/liuruyi/bmodel/data/{mode}_valid_tfrecord" 

# =====================
# 数据加载相关参数
# =====================
buffer_size = 6  # 预取/缓存数据条数
shuffle_size = 6  # shuffle缓存大小
num_parallel_calls = -1  # map并行线程数，-1为自动
num_parallel_reads = -1  # 读文件并行数，-1为自动

# =====================
# TFRecord解析函数
# =====================
def example_parser(example, feature_description, evaluate_flag):
    """
    解析TFRecord数据示例，将原始example解析为模型输入格式。
    Args:
        example: 一个或一批TFRecord原始数据
        feature_description: 特征描述字典，定义每个字段的类型
        evaluate_flag: 是否为评估模式（影响输出内容）
    Returns:
        一个字典（包含input_ids和attention_mask）和标签label
    """
    feature = tf.io.parse_example(example, feature_description)  # 解析example
    input_ids = feature['input_ids']  # 取出input_ids
    attention_mask = feature['attention_mask']  # 取出attention_mask
    label = feature['label']  # 取出标签
    # 评估时可扩展输出更多信息
    return {'input_ids': input_ids, 'attention_mask': attention_mask}, label

# =====================
# 构建数据加载器
# =====================
def get_dataloader(data_path, seq_len, batch_size, evaluate_flag=False):
    """
    创建TensorFlow数据加载器，支持训练和评估。
    Args:
        data_path: TFRecord文件目录
        seq_len: 输入序列长度
        batch_size: 批量大小
        evaluate_flag: 是否为评估模式
    Returns:
        tf.data.Dataset对象
    """
    # 构建特征描述字典
    feature_description = {} 
    for key in [label_name] + basic_cols + feature_cols:
        if key == label_name:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.int64, default_value=[0])}) 
        elif key in basic_cols:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.string, default_value=[""])}) 
        else:
            feature_description.update({key: tf.io.FixedLenFeature([seq_len], tf.int64, default_value=[0]*seq_len)}) 
    # 获取所有.gz文件路径
    filelist = tf.io.gfile.glob(os.path.join(data_path, "*.gz"))

    # 设置数据集读取选项
    options = tf.data.Options()
    options.experimental_deterministic = True if evaluate_flag is True else False  # 评估时顺序固定
    options.experimental_threading.private_threadpool_size = 5
    options.experimental_threading.max_intra_op_parallelism = 1
    autotune = tf.data.experimental.AUTOTUNE
    # 创建TFRecord数据集
    dataloader = tf.data.TFRecordDataset(
        filelist,
        compression_type="GZIP",
        num_parallel_reads=num_parallel_reads if num_parallel_reads is not None else autotune
    )
    dataloader = dataloader.with_options(options)
    dataloader = dataloader.batch(batch_size)
    # 解析example
    dataloader = dataloader.map(
        partial(example_parser, feature_description=feature_description, evaluate_flag=evaluate_flag),
        num_parallel_calls=num_parallel_calls if num_parallel_calls is not None else autotune
    )
    # 训练时打乱数据
    if not evaluate_flag:
        dataloader = dataloader.shuffle(buffer_size=shuffle_size, reshuffle_each_iteration=True)
    # 预取数据提升性能
    dataloader = dataloader.prefetch(buffer_size=buffer_size)
    return dataloader 

# =====================
# 自定义回调：每个epoch保存模型和验证指标
# =====================
class SaveMetricsAndModelCallback(tf.keras.callbacks.Callback):
    """
    每个epoch结束后，评估验证集并保存模型和指标到指定目录。
    Args:
        save_dir: 保存目录
        val_dataset: 验证集dataloader
    """
    def __init__(self, save_dir, val_dataset):
        self.save_dir = save_dir
        self.val_dataset = val_dataset
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

    def on_epoch_end(self, epoch, logs=None):
        # 评估验证集，返回损失和准确率
        val_loss, val_acc = self.model.evaluate(self.val_dataset, verbose=0)
        # 保存指标到txt文件
        metrics_file = os.path.join(self.save_dir, f'metrics_epoch_{epoch + 1}.txt')
        with open(metrics_file, 'w') as f:
            f.write(f'Epoch {epoch + 1} - Val Loss: {val_loss}, Val Acc: {val_acc}\n')
        # 保存模型权重
        model_path = os.path.join(self.save_dir, f'model_epoch_{epoch + 1}')
        self.model.save_pretrained(model_path)
        print(f'Epoch {epoch + 1} - Metrics and model saved.')

# =====================
# 创建训练和验证数据加载器
# =====================
train_dataloader = get_dataloader(train_tfrecord_path, seq_len=seq_len, batch_size=batch_size, evaluate_flag=False) 
valid_dataloader = get_dataloader(valid_tfrecord_path, seq_len=seq_len, batch_size=batch_size, evaluate_flag=True) 

# =====================
# 分布式训练策略（多GPU）
# =====================
strategy = tf.distribute.MirroredStrategy(devices=['/device:GPU:0', '/device:GPU:1',
                                                  '/device:GPU:2', '/device:GPU:3',
                                                  '/device:GPU:4', '/device:GPU:5',
                                                  '/device:GPU:6', '/device:GPU:7']) 
print(f'Number of devices: {strategy.num_replicas_in_sync}') 
 
# 根据GPU数量调整全局批量大小
GLOBAL_BATCH_SIZE = 16 * strategy.num_replicas_in_sync  
 
# 启用设备日志，显示操作在哪个设备上执行
# 便于调试分布式训练
# =====================
tf.debugging.set_log_device_placement(True) 
 
# =====================
# 在分布式策略范围内创建和编译模型
# =====================
with strategy.scope(): 
    # 从之前训练的检查点加载ALBERT模型
    model = TFAlbertForSequenceClassification.from_pretrained('training_results_5/model_epoch_1', num_labels=2)
    # 设置所有层为可训练
    model.trainable = True
    for layer in model.layers:
        layer.trainable = True
    
    # =====================
    # 打印模型关键信息
    # =====================
    # 方法1: 打印模型概要
    print("\n==================== 模型概要 ====================")
    model.summary(line_length=120)
    
    # 方法2: 查看分类器部分结构
    if hasattr(model, 'classifier'):
        print("\n==================== 分类器结构 ====================")
        if hasattr(model.classifier, 'summary'):
            model.classifier.summary()
        else:
            print(f"分类器类型: {model.classifier.__class__.__name__}")
            print(f"分类器权重形状: {[w.shape for w in model.classifier.weights]}")
    
    
    # =====================
    # 结束模型信息打印
    # =====================
    
    # 优化器和编译
    optimizer = tf.keras.optimizers.Adam(learning_rate=2e-5)
    loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    model.compile(optimizer=optimizer, loss=loss, metrics=['sparse_categorical_accuracy'])

# =====================
# 分发数据集到各个设备
# =====================
dist_dataset = strategy.experimental_distribute_dataset(train_dataloader) 

# =====================
# 设置训练样本总量和每轮步数
# =====================
total_train_samples = 9000000  # 总训练样本数
# total_train_samples = 365233
steps_per_epoch = total_train_samples // GLOBAL_BATCH_SIZE  # 每个epoch的步数

# =====================
# 设置保存路径和回调函数
# =====================
save_dir = 'training_results_6'
save_callback = SaveMetricsAndModelCallback(save_dir,valid_dataloader)

# =====================
# 添加参数检查回调
# =====================
class ModelUpdateCallback(tf.keras.callbacks.Callback):
    def __init__(self, model):
        super().__init__()
        self.model = model
        # 记录初始参数
        self.initial_weights = [w.numpy() for w in model.trainable_variables]
        
    def on_epoch_end(self, epoch, logs=None):
        # 获取当前参数
        current_weights = [w.numpy() for w in self.model.trainable_variables]
        
        # 计算参数变化
        weight_changes = []
        for init_w, curr_w in zip(self.initial_weights, current_weights):
            change = np.mean(np.abs(init_w - curr_w))
            weight_changes.append(change)
            
        print(f"\nEpoch {epoch + 1} 参数变化统计:")
        for i, change in enumerate(weight_changes):
            print(f"参数 {i}: 平均变化 {change:.6f}")
            
        # 检查不同GPU上的参数是否一致
        if hasattr(self.model, 'distribute_strategy'):
            for var in self.model.trainable_variables:
                if hasattr(var, 'values'):
                    # 检查所有副本的参数是否一致
                    values = var.values
                    if len(values) > 1:
                        for i in range(1, len(values)):
                            diff = np.mean(np.abs(values[0].numpy() - values[i].numpy()))
                            print(f"参数 {var.name} 在GPU 0和GPU {i}之间的差异: {diff:.6f}")

# =====================
# 添加验证监控回调类
# =====================
class ValidationMonitorCallback(tf.keras.callbacks.Callback):
    """
    监控验证过程中的数据和模型输出
    """
    def __init__(self, val_dataset, num_samples=20):
        super().__init__()
        self.val_dataset = val_dataset
        self.num_samples = num_samples  # 每个epoch打印的样本数
        
    def on_epoch_end(self, epoch, logs=None):
        print(f"\n============== Epoch {epoch + 1} 验证集监控 ==============")
        
        # 存储所有标签和预测值，用于分析分布
        all_labels = []
        all_predictions = []
        all_logits = []
        
        # 存储前N个样本详细信息
        detailed_samples = []
        samples_collected = 0
        
        # 遍历部分验证数据
        for batch_idx, (inputs, labels) in enumerate(self.val_dataset):
            if batch_idx >= 10:  # 只检查前10个batch
                break
                
            # 获取模型输出
            outputs = self.model(inputs, training=False)
            if isinstance(outputs, tuple):
                logits = outputs[0]
            else:
                logits = outputs.logits
                
            # 转换为numpy数组
            input_ids = inputs['input_ids'].numpy()
            attention_mask = inputs['attention_mask'].numpy()
            labels_np = labels.numpy()
            logits_np = logits.numpy()
            predictions = np.argmax(logits_np, axis=-1)
            
            # 添加到汇总列表
            all_labels.extend(labels_np)
            all_predictions.extend(predictions)
            all_logits.extend(logits_np)
            
            # 收集详细样本信息
            for i in range(len(labels_np)):
                if samples_collected < self.num_samples:
                    # 为了节省空间，只保存input_ids的有效部分（非padding部分）
                    valid_length = np.sum(attention_mask[i])
                    samples_collected += 1
                    detailed_samples.append({
                        "样本索引": samples_collected,
                        "input_ids": input_ids[i][:valid_length],
                        "label": labels_np[i],
                        "logits": logits_np[i],
                        "prediction": predictions[i],
                        "is_correct": predictions[i] == labels_np[i]
                    })
                else:
                    break
            
            if samples_collected >= self.num_samples:
                break
        
        # 打印详细样本信息
        print(f"\n前{len(detailed_samples)}个样本的详细信息:")
        for sample in detailed_samples:
            print(f"样本 {sample['样本索引']}:")
            print(f"  输入(input_ids): {sample['input_ids'][:10]}... (长度: {len(sample['input_ids'])})")
            print(f"  标签(label): {sample['label']}")
            print(f"  logits: {sample['logits']}")
            print(f"  预测(prediction): {sample['prediction']}")
            print(f"  是否正确: {sample['is_correct']}")
            print()
        
        # 转换为numpy数组以便统计
        all_labels = np.array(all_labels)
        all_predictions = np.array(all_predictions)
        all_logits = np.array(all_logits)
        
        # 计算统计信息
        accuracy = np.mean(all_predictions == all_labels)
        
        # 分析类别分布
        label_counts = np.bincount(all_labels, minlength=2)
        pred_counts = np.bincount(all_predictions, minlength=2)
        
        # 分析每个类别的准确率
        class_accuracy = []
        for cls in range(2):
            if np.sum(all_labels == cls) > 0:
                cls_acc = np.mean(all_predictions[all_labels == cls] == cls)
                class_accuracy.append(cls_acc)
            else:
                class_accuracy.append(0)
        
        # 分析logits分布
        logits_mean = np.mean(all_logits, axis=0)
        logits_std = np.std(all_logits, axis=0)
        
        # 打印统计结果
        print(f"\n验证集统计 (前{len(all_labels)}个样本):")
        print(f"总体准确率: {accuracy:.4f}")
        print(f"标签分布: 类别0: {label_counts[0]}, 类别1: {label_counts[1]}")
        print(f"预测分布: 类别0: {pred_counts[0]}, 类别1: {pred_counts[1]}")
        print(f"类别0准确率: {class_accuracy[0]:.4f}, 类别1准确率: {class_accuracy[1]:.4f}")
        print(f"Logits均值: {logits_mean}")
        print(f"Logits标准差: {logits_std}")
        
        # 查看连续相同预测的情况
        same_pred_count = 0
        for i in range(1, len(all_predictions)):
            if all_predictions[i] == all_predictions[i-1]:
                same_pred_count += 1
        print(f"连续相同预测的比例: {same_pred_count/(len(all_predictions)-1):.4f}")
        
        # 检查模型是否总是预测同一类别
        if np.min(pred_counts) == 0:
            print("\n警告: 模型对所有样本预测为同一类别!")
            print(f"预测的类别: {np.argmax(pred_counts)}")
            print(f"所有logits的平均值: {np.mean(all_logits, axis=0)}")
            print(f"logits的最小值: {np.min(all_logits, axis=0)}")
            print(f"logits的最大值: {np.max(all_logits, axis=0)}")
        
        # 保存详细样本信息到文件
        details_file = os.path.join(save_dir, f'val_details_epoch_{epoch + 1}.txt')
        with open(details_file, 'w') as f:
            f.write(f"Epoch {epoch + 1} 验证样本详情:\n\n")
            for sample in detailed_samples:
                f.write(f"样本 {sample['样本索引']}:\n")
                f.write(f"  输入: {sample['input_ids'][:10]}...\n")
                f.write(f"  标签: {sample['label']}\n")
                f.write(f"  logits: {sample['logits']}\n")
                f.write(f"  预测: {sample['prediction']}\n")
                f.write(f"  是否正确: {sample['is_correct']}\n\n")

# =====================
# 添加回调
# =====================
val_monitor_callback = ValidationMonitorCallback(valid_dataloader, num_samples=20)
model.fit(dist_dataset, epochs=10, steps_per_epoch=steps_per_epoch, 
         callbacks=[save_callback, val_monitor_callback])

print("训练完成。")