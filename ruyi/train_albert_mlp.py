import tensorflow as tf
import transformers
print(f"transformers 库的版本为: {transformers.__version__}")
print(f"TensorFlow 版本为: {tf.__version__}")
import os

# =====================
# 设置随机种子，保证实验可复现
# =====================
seed = 42
os.environ['PYTHONHASHSEED'] = str(seed)  # 控制Python哈希种子
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 只显示TF的warning和error

# =====================
# 导入常用库
# =====================
import random
import numpy as np
import pandas as pd
import datetime
import logging
import time
import matplotlib.image as mpimg 
import matplotlib.pyplot as plt 
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.metrics import roc_auc_score, log_loss, f1_score, accuracy_score, roc_curve, precision_score, recall_score
import tensorflow as tf
from transformers import BertTokenizer, TFAlbertForSequenceClassification, BertTokenizerFast
import tensorflow.keras.backend as K
from BaseModel import gruModel 
# from bert import BertModel
from cc_utils import get_dataloader
from functools import partial 
# 导入自定义MLP模型
from albert_mlp import TFAlbertForSequenceClassificationMLP

# =====================
# 再次设置所有随机种子，保证各库一致性
# =====================
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

# =====================
# 禁用TensorFlow日志，减少无关输出
# =====================
logging.getLogger('tensorflow').disabled = True
logging.getLogger('tensorflow').setLevel(logging.ERROR)

# =====================
# 打印TF和GPU信息，便于调试
# =====================
print(tf.__version__)
print(tf.config.list_physical_devices('GPU'))
print('Using TensorFlow version',tf.__version__)

# =====================
# 模型与数据参数配置
# =====================
seq_len = 512  # 输入序列最大长度
batch_size = 16  # 单卡批量大小
label_name = 'label'  # 标签字段名
mode = "albert_base_500w"  # 当前模式（可切换train/test）

# =====================
# 数据集字段定义
# =====================
basic_cols = ['user_id', 'date']  # 基础信息列
feature_cols = ['input_ids','attention_mask']  # BERT输入特征列

# =====================
# 数据筛选条件（未直接用到）
# =====================
train_use = "train_flag == 'train'" 
valid_use = "train_flag == 'test'" 

# =====================
# TFRecord数据路径
# =====================
train_tfrecord_path = f"hdfs://haruna/user/tiger/xql/liuruyi/bmodel/data/{mode}_train_tfrecord" 
valid_tfrecord_path = f"hdfs://haruna/user/tiger/xql/liuruyi/bmodel/data/{mode}_valid_tfrecord"

# =====================
# 数据加载相关参数
# =====================
buffer_size = 64  # 预取/缓存数据条数，增加以提高效率
shuffle_size = 1024  # 增加shuffle缓存大小，提高随机性
num_parallel_calls = -1  # map并行线程数，-1为自动
num_parallel_reads = -1  # 读文件并行数，-1为自动

# =====================
# TFRecord解析函数
# =====================
def example_parser(example, feature_description, evaluate_flag):
    """
    解析TFRecord数据示例，将原始example解析为模型输入格式。
    Args:
        example: 一个或一批TFRecord原始数据
        feature_description: 特征描述字典，定义每个字段的类型
        evaluate_flag: 是否为评估模式（影响输出内容）
    Returns:
        一个字典（包含input_ids和attention_mask）和标签label
    """
    feature = tf.io.parse_example(example, feature_description)  # 解析example
    input_ids = feature['input_ids']  # 取出input_ids
    attention_mask = feature['attention_mask']  # 取出attention_mask
    label = feature['label']  # 取出标签
    # 评估时可扩展输出更多信息
    return {'input_ids': input_ids, 'attention_mask': attention_mask}, label

# =====================
# 构建数据加载器
# =====================
def get_dataloader(data_path, seq_len, batch_size, evaluate_flag=False):
    """
    创建TensorFlow数据加载器，支持训练和评估。
    Args:
        data_path: TFRecord文件目录
        seq_len: 输入序列长度
        batch_size: 批量大小
        evaluate_flag: 是否为评估模式
    Returns:
        tf.data.Dataset对象
    """
    # 构建特征描述字典
    feature_description = {} 
    for key in [label_name] + basic_cols + feature_cols:
        if key == label_name:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.int64, default_value=[0])}) 
        elif key in basic_cols:
            feature_description.update({key: tf.io.FixedLenFeature([], tf.string, default_value=[""])}) 
        else:
            feature_description.update({key: tf.io.FixedLenFeature([seq_len], tf.int64, default_value=[0]*seq_len)}) 
    # 获取所有.gz文件路径
    filelist = tf.io.gfile.glob(os.path.join(data_path, "*.gz"))
    
    # 随机打乱文件列表顺序，增加随机性（训练集和验证集都打乱）
    random.shuffle(filelist)
    print(f"已随机打乱{len(filelist)}个TFRecord文件的顺序")

    # 设置数据集读取选项
    options = tf.data.Options()
    # 为了更好的随机性，我们允许验证集也是非确定性的
    options.experimental_deterministic = False  
    options.experimental_threading.private_threadpool_size = 5
    options.experimental_threading.max_intra_op_parallelism = 1
    autotune = tf.data.experimental.AUTOTUNE
    # 创建TFRecord数据集
    dataloader = tf.data.TFRecordDataset(
        filelist,
        compression_type="GZIP",
        num_parallel_reads=num_parallel_reads if num_parallel_reads is not None else autotune
    )
    dataloader = dataloader.with_options(options)
    
    # 所有数据集（包括验证集）都进行打乱操作，但验证集使用较小的shuffle_size
    val_shuffle_size = 512 if evaluate_flag else shuffle_size
    dataloader = dataloader.shuffle(buffer_size=val_shuffle_size, reshuffle_each_iteration=True, seed=None)
    print(f"使用shuffle_size={val_shuffle_size}进行数据打乱")
    
    # 批量化数据
    dataloader = dataloader.batch(batch_size)
        
    # 解析example
    dataloader = dataloader.map(
        partial(example_parser, feature_description=feature_description, evaluate_flag=evaluate_flag),
        num_parallel_calls=num_parallel_calls if num_parallel_calls is not None else autotune
    )
    
    # 预取数据提升性能
    dataloader = dataloader.prefetch(buffer_size=buffer_size)
    return dataloader

# =====================
# 自定义回调：每个epoch保存模型和验证指标
# =====================
class SaveMetricsAndModelCallback(tf.keras.callbacks.Callback):
    """
    每个epoch结束后，评估验证集并保存模型和指标到指定目录。
    Args:
        save_dir: 保存目录
        val_dataset: 验证集dataloader
    """
    def __init__(self, save_dir, val_dataset):
        self.save_dir = save_dir
        self.val_dataset = val_dataset
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

    def on_epoch_end(self, epoch, logs=None):
        # 评估验证集，返回损失和准确率
        # 注意：由于验证集被打乱，每次评估结果可能略有不同
        val_loss, val_acc = self.model.evaluate(self.val_dataset.take(50), verbose=0)  # 取更多验证样本
        # 保存指标到txt文件
        metrics_file = os.path.join(self.save_dir, f'metrics_epoch_{epoch + 1}.txt')
        with open(metrics_file, 'w') as f:
            f.write(f'Epoch {epoch + 1} - Val Loss: {val_loss}, Val Acc: {val_acc}\n')
            f.write(f'注意：由于验证集被打乱，每次评估结果可能略有不同\n')
        # 保存模型权重
        model_path = os.path.join(self.save_dir, f'model_epoch_{epoch + 1}')
        self.model.save_pretrained(model_path)
        print(f'Epoch {epoch + 1} - Metrics and model saved. Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}')

# =====================
# 创建训练和验证数据加载器
# =====================
train_dataloader = get_dataloader(train_tfrecord_path, seq_len=seq_len, batch_size=batch_size, evaluate_flag=False) 
valid_dataloader = get_dataloader(valid_tfrecord_path, seq_len=seq_len, batch_size=batch_size, evaluate_flag=True) 

# =====================
# 分布式训练策略（多GPU）
# =====================
strategy = tf.distribute.MirroredStrategy(devices=['/device:GPU:0', '/device:GPU:1',
                                                  '/device:GPU:2', '/device:GPU:3',
                                                  '/device:GPU:4', '/device:GPU:5',
                                                  '/device:GPU:6', '/device:GPU:7',]) 
print(f'Number of devices: {strategy.num_replicas_in_sync}') 
 
# 根据GPU数量调整全局批量大小
GLOBAL_BATCH_SIZE = 16 * strategy.num_replicas_in_sync  
 
# 启用设备日志，显示操作在哪个设备上执行
# 便于调试分布式训练
# =====================
tf.debugging.set_log_device_placement(True) 
# =====================
# 设置训练样本总量和每轮步数
# =====================
total_train_samples = 4886824  # 总训练样本数
# total_train_samples = 365233
steps_per_epoch = total_train_samples // GLOBAL_BATCH_SIZE  # 每个epoch的步数

 
# =====================
# 在分布式策略范围内创建和编译模型
# =====================
with strategy.scope(): 
    # 从之前训练的检查点加载ALBERT模型，使用自定义的MLP模型
    model = TFAlbertForSequenceClassificationMLP.from_pretrained('albert-base-chinese-cluecorpussmall', num_labels=2)
    # 设置所有层为可训练
    model.trainable = True
    
    # 添加权重正则化，防止模型过度拟合
    for layer in model.layers:
        layer.trainable = True
        # 为dense层添加L2正则化
        if hasattr(layer, 'kernel_regularizer'):
            layer.kernel_regularizer = tf.keras.regularizers.l2(0.01)
    
    # =====================
    # 打印模型关键信息
    # =====================
    # 方法1: 打印模型概要
    print("\n==================== 模型概要 ====================")
    model.summary(line_length=120)
    
    # 优化器和学习率设置
    # =====================
    # 计算总训练步数和预热步数
    total_epochs = 20  # 总训练轮数
    warmup_epochs = 2  # 前2个epoch用于预热
    
    # 设置初始学习率和最终学习率
    initial_learning_rate = 1e-8  # 初始学习率
    max_learning_rate = 1e-5      # 预热后达到的最大学习率
    min_learning_rate = 1e-6      # 最终衰减到的最小学习率
    
    print(f"\n学习率设置: 预热轮数={warmup_epochs}, 总轮数={total_epochs}")
    print(f"初始学习率={initial_learning_rate}, 最大学习率={max_learning_rate}, 最终学习率={min_learning_rate}")
    
    # 使用固定学习率初始化Adam优化器，学习率将由回调函数控制
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=initial_learning_rate,  # 初始设置为最小值，后续由回调控制
        beta_1=0.9,            # 动量衰减率
        beta_2=0.999,          # 平方梯度衰减率
        epsilon=1e-8,          # 数值稳定性常数
        clipnorm=0.5           # 减小梯度裁剪阈值，防止梯度爆炸
    )
    
    print("使用Adam优化器，启用动量和梯度裁剪，提高学习稳定性。")

    # 计算类别权重来解决数据不平衡问题
    # =====================
    # 根据用户要求，使用均衡的类别权重（1:1）
    weight_for_0 = 1.0  # 负样本权重
    weight_for_1 = 1.0  # 正样本权重
    class_weight = {0: weight_for_0, 1: weight_for_1}
    
    print(f"\n应用均衡类别权重: 类别0(负样本)={class_weight[0]:.2f}, 类别1(正样本)={class_weight[1]:.2f}")
    print(f"使用1:1权重可以使模型公平对待正负样本，适合样本均衡的情况。")

    # 保留标签平滑
    label_smoothing = 0.1

    # 使用TF自带的CategoricalCrossentropy实现标签平滑
    print(f"使用自定义方式实现标签平滑: {label_smoothing}")
    
    # 标签平滑替代实现函数
    def apply_label_smoothing(y_true, num_classes=2, smoothing=0.1):
        """将整数标签转换为带有平滑的one-hot编码"""
        # 将整数标签转为one-hot向量
        y_hot = tf.one_hot(tf.cast(y_true, tf.int32), num_classes)
        # 平滑标签
        y_smooth = (1.0 - smoothing) * y_hot + smoothing / num_classes
        return y_smooth

    # 自定义带权重的损失函数
    class WeightedSparseCategoricalCrossentropy(tf.keras.losses.Loss):
        def __init__(self, class_weights, from_logits=True, reduction=tf.keras.losses.Reduction.AUTO, name='weighted_sparse_categorical_crossentropy'):
            super().__init__(reduction=reduction, name=name)
            self.class_weights = class_weights
            self.from_logits = from_logits
            self.loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(
                from_logits=from_logits, reduction=tf.keras.losses.Reduction.NONE)
                
        def call(self, y_true, y_pred):
            # 获取每个样本对应的权重
            weights = tf.gather(tf.constant(list(self.class_weights.values()), dtype=tf.float32), tf.cast(y_true, tf.int32))
            
            # 手动应用标签平滑 (可选功能，需要修改损失计算方式)
            if hasattr(self, 'use_label_smoothing') and self.use_label_smoothing:
                # 这里可以使用KL散度或者交叉熵计算平滑后的损失
                # 但当前版本继续使用SparseCategoricalCrossentropy
                pass
                
            # 计算未加权的损失
            unweighted_losses = self.loss_fn(y_true, y_pred)
            # 应用权重
            weighted_losses = unweighted_losses * weights
            return weighted_losses

    # 使用带权重的损失函数
    weighted_loss = WeightedSparseCategoricalCrossentropy(
        class_weight, from_logits=True)
    
    # 添加L2正则化损失
    def add_regularization_loss(model, weight_decay=0.01):
        reg_losses = []
        for layer in model.layers:
            if isinstance(layer, tf.keras.layers.Dense):
                reg_losses.append(
                    tf.keras.regularizers.l2(weight_decay)(layer.kernel))
        return tf.math.add_n(reg_losses) if reg_losses else 0.0
    
    # 自定义训练步骤，添加正则化损失
    @tf.function
    def train_step(model, x, y, optimizer, loss_fn, weight_decay=0.01):
        with tf.GradientTape() as tape:
            predictions = model(x, training=True)
            loss = loss_fn(y, predictions)
            reg_loss = add_regularization_loss(model, weight_decay)
            total_loss = loss + reg_loss
        
        gradients = tape.gradient(total_loss, model.trainable_variables)
        # 梯度裁剪 - 防止梯度爆炸或消失
        gradients, _ = tf.clip_by_global_norm(gradients, clip_norm=1.0)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        return total_loss
    
    """
    # 方案二：使用CategoricalCrossentropy代替SparseCategoricalCrossentropy实现标签平滑
    # 这需要先将标签转换为one-hot格式
    # 示例代码(需要在模型训练前转换数据):
    
    def sparse_to_categorical_dataset(dataset, num_classes=2):
        def convert_sparse_to_categorical(features, labels):
            # 原始标签转换为one-hot编码
            labels = tf.one_hot(tf.cast(labels, tf.int32), num_classes)
            return features, labels
            
        return dataset.map(convert_sparse_to_categorical)
    
    # 转换后使用标准CategoricalCrossentropy，它支持label_smoothing参数
    categorical_loss = tf.keras.losses.CategoricalCrossentropy(
        from_logits=True, 
        label_smoothing=0.1
    )
    
    # 这里仍使用自定义权重函数，但基于CategoricalCrossentropy
    """
    
    # 重新编译模型，仅添加AUC指标
    model.compile(
        optimizer=optimizer,
        loss=weighted_loss,
        metrics=[
            'sparse_categorical_accuracy'  # 只使用一个基本指标，减少可能的错误
        ]
    )
    

# =====================
# 分发数据集到各个设备
# =====================
dist_dataset = strategy.experimental_distribute_dataset(train_dataloader) 


# =====================
# 设置保存路径和回调函数
# =====================
save_dir = 'training_results_8'
save_callback = SaveMetricsAndModelCallback(save_dir,valid_dataloader)

# =====================
# 添加验证监控回调类
# =====================
class ValidationMonitorCallback(tf.keras.callbacks.Callback):
    """
    监控验证过程中的数据和模型输出
    """
    def __init__(self, val_dataset, num_samples=20, max_batches=10):
        super().__init__()
        self.val_dataset = val_dataset
        self.num_samples = num_samples  # 每个epoch打印的样本数
        self.max_batches = max_batches  # 最多检查的batch数量
        
    def on_epoch_end(self, epoch, logs=None):
        print(f"\n============== Epoch {epoch + 1} 验证集监控 ==============")
        
        # 存储所有标签和预测值，用于分析分布
        all_labels = []
        all_predictions = []
        all_logits = []
        
        # 存储前N个样本详细信息
        detailed_samples = []
        samples_collected = 0
        
        # 遍历部分验证数据
        for batch_idx, (inputs, labels) in enumerate(self.val_dataset):
            if batch_idx >= self.max_batches:  # 只检查前max_batches个batch
                break
                
            # 获取模型输出
            outputs = self.model(inputs, training=False)
            # 处理不同格式的输出
            if isinstance(outputs, tuple):
                logits = outputs[0]
            else:
                logits = outputs.logits
                
            # 转换为numpy数组
            input_ids = inputs['input_ids'].numpy()
            attention_mask = inputs['attention_mask'].numpy()
            labels_np = labels.numpy()
            logits_np = logits.numpy()
            predictions = np.argmax(logits_np, axis=-1)
            
            # 添加到汇总列表
            all_labels.extend(labels_np)
            all_predictions.extend(predictions)
            all_logits.extend(logits_np)
            
            # 收集详细样本信息
            for i in range(len(labels_np)):
                if samples_collected < self.num_samples:
                    # 为了节省空间，只保存input_ids的有效部分（非padding部分）
                    valid_length = np.sum(attention_mask[i])
                    samples_collected += 1
                    detailed_samples.append({
                        "样本索引": samples_collected,
                        "input_ids": input_ids[i][:valid_length],
                        "label": labels_np[i],
                        "logits": logits_np[i],
                        "prediction": predictions[i],
                        "is_correct": predictions[i] == labels_np[i]
                    })
                else:
                    break
            
            if samples_collected >= self.num_samples:
                break
        
        # 打印详细样本信息
        print(f"\n前{len(detailed_samples)}个样本的详细信息:")
        for sample in detailed_samples:
            print(f"样本 {sample['样本索引']}:")
            print(f"  输入(input_ids): {sample['input_ids'][:10]}... (长度: {len(sample['input_ids'])})")
            print(f"  标签(label): {sample['label']}")
            print(f"  logits: {sample['logits']}")
            print(f"  预测(prediction): {sample['prediction']}")
            print(f"  是否正确: {sample['is_correct']}")
            print()
        
        # 转换为numpy数组以便统计
        all_labels = np.array(all_labels)
        all_predictions = np.array(all_predictions)
        all_logits = np.array(all_logits)
        
        # 计算统计信息
        accuracy = np.mean(all_predictions == all_labels)
        
        # 分析类别分布
        label_counts = np.bincount(all_labels, minlength=2)
        pred_counts = np.bincount(all_predictions, minlength=2)
        
        # 分析每个类别的准确率
        class_accuracy = []
        for cls in range(2):
            if np.sum(all_labels == cls) > 0:
                cls_acc = np.mean(all_predictions[all_labels == cls] == cls)
                class_accuracy.append(cls_acc)
            else:
                class_accuracy.append(0)
        
        # 分析logits分布
        logits_mean = np.mean(all_logits, axis=0)
        logits_std = np.std(all_logits, axis=0)
        
        # 打印统计结果
        print(f"\n验证集统计 (随机采样的{len(all_labels)}个样本):")
        print(f"总体准确率: {accuracy:.4f}")
        print(f"标签分布: 类别0: {label_counts[0]}, 类别1: {label_counts[1]}")
        print(f"预测分布: 类别0: {pred_counts[0]}, 类别1: {pred_counts[1]}")
        print(f"类别0准确率: {class_accuracy[0]:.4f}, 类别1准确率: {class_accuracy[1]:.4f}")
        print(f"Logits均值: {logits_mean}")
        print(f"Logits标准差: {logits_std}")
        
        # 查看连续相同预测的情况
        same_pred_count = 0
        for i in range(1, len(all_predictions)):
            if all_predictions[i] == all_predictions[i-1]:
                same_pred_count += 1
        print(f"连续相同预测的比例: {same_pred_count/(len(all_predictions)-1):.4f}")
        
        # 检查模型是否总是预测同一类别
        if np.min(pred_counts) == 0:
            print("\n警告: 模型对所有样本预测为同一类别!")
            print(f"预测的类别: {np.argmax(pred_counts)}")
            print(f"所有logits的平均值: {np.mean(all_logits, axis=0)}")
            print(f"logits的最小值: {np.min(all_logits, axis=0)}")
            print(f"logits的最大值: {np.max(all_logits, axis=0)}")
        
        # 保存详细样本信息到文件
        details_file = os.path.join(save_dir, f'val_details_epoch_{epoch + 1}.txt')
        with open(details_file, 'w') as f:
            f.write(f"Epoch {epoch + 1} 验证样本详情:\n\n")
            for sample in detailed_samples:
                f.write(f"样本 {sample['样本索引']}:\n")
                f.write(f"  输入: {sample['input_ids'][:10]}...\n")
                f.write(f"  标签: {sample['label']}\n")
                f.write(f"  logits: {sample['logits']}\n")
                f.write(f"  预测: {sample['prediction']}\n")
                f.write(f"  是否正确: {sample['is_correct']}\n\n")
                
        # 保存评估重要指标
        metrics_file = os.path.join(save_dir, f'val_metrics_epoch_{epoch + 1}.txt')
        with open(metrics_file, 'w') as f:
            f.write(f"Epoch {epoch + 1} 验证指标:\n")
            f.write(f"样本数: {len(all_labels)}\n")
            f.write(f"总体准确率: {accuracy:.4f}\n")
            f.write(f"类别0准确率: {class_accuracy[0]:.4f}\n")
            f.write(f"类别1准确率: {class_accuracy[1]:.4f}\n")
            f.write(f"标签分布: 类别0: {label_counts[0]}, 类别1: {label_counts[1]}\n")
            f.write(f"预测分布: 类别0: {pred_counts[0]}, 类别1: {pred_counts[1]}\n")
            f.write(f"Logits均值: {logits_mean}\n")
            f.write(f"Logits标准差: {logits_std}\n")

# =====================
# 添加回调
# =====================
# 添加提前停止回调，防止过拟合
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=5,
    restore_best_weights=True,
    verbose=1
)

# 添加模型检查点回调，保存最佳模型
checkpoint = tf.keras.callbacks.ModelCheckpoint(
    os.path.join(save_dir, 'best_model'),
    monitor='val_loss',
    save_best_only=True,
    verbose=1
)

# 自定义学习率调度策略，实现预热和衰减
def lr_scheduler(epoch, lr):
    if epoch < warmup_epochs:
        # 预热阶段：从初始学习率线性增加到最大学习率
        lr_weight = epoch / warmup_epochs
        return initial_learning_rate + lr_weight * (max_learning_rate - initial_learning_rate)
    else:
        # 衰减阶段：从最大学习率指数衰减到最小学习率
        decay_epochs = total_epochs - warmup_epochs
        decay_rate = (epoch - warmup_epochs) / decay_epochs
        # 使用余弦衰减，比线性衰减更平滑
        cosine_decay = 0.5 * (1 + np.cos(np.pi * decay_rate))
        return min_learning_rate + cosine_decay * (max_learning_rate - min_learning_rate)

# 添加学习率调度回调
lr_scheduler_callback = tf.keras.callbacks.LearningRateScheduler(
    lr_scheduler,
    verbose=1  # 打印每个epoch的学习率
)

# 初始化验证监控回调
val_monitor_callback = ValidationMonitorCallback(valid_dataloader, num_samples=20, max_batches=10)

# =====================
# 移除class_weight参数，因为权重已经在损失函数中应用
# =====================
model.fit(dist_dataset, epochs=total_epochs, steps_per_epoch=steps_per_epoch, 
         validation_data=valid_dataloader.take(50),
         callbacks=[save_callback, val_monitor_callback, early_stopping, checkpoint, lr_scheduler_callback])

print("训练完成。")