import numpy as np
import matplotlib.pyplot as plt
import os
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set, Optional

class SequenceAnalyzer:
    """
    监控LLM生成过程中的终止信号指标：
    1. 终止符统计
    2. 生成的token数目
    3. 重复模式检测
    4. 句法完整性分析
    """
    
    def __init__(self, prompt_tokens: List[int], tokenizer):
        """
        初始化序列分析器
        
        Args:
            prompt_tokens: 输入prompt的token列表
            tokenizer: 分词器，用于将token转换为文本
        """
        # 保存输入的prompt tokens
        self.prompt_tokens = prompt_tokens
        self.prompt_length = len(prompt_tokens)
        self.tokenizer = tokenizer
        
        # 初始化已生成的tokens列表
        self.generated_tokens = []
        
        # 终止符集合
        self.term_chars = set(['.', '!', '?', '"', ')', ']', '}'])
        
        # 成对符号的映射
        self.paired_symbols = {
            '(': ')', 
            '[': ']', 
            '{': '}',
            '"': '"',
            "'": "'",
        }
        
        # 初始化指标记录
        self.metrics = {
            'step': [],                      # 步骤索引
            'term_char_count': [],           # 终止符数量
            'token_count': [],               # 生成的token数目
            'unigram_repetition': [],        # 1-gram重复率
            'bigram_repetition': [],         # 2-gram重复率
            'trigram_repetition': [],        # 3-gram重复率
            'syntax_depth': [],              # 句法深度
            'unclosed_structures': []        # 未闭合结构数量
        }
        
        # 计算prompt中的终止符数量
        prompt_text = self.tokenizer.decode(self.prompt_tokens)
        self.prompt_term_chars = sum(1 for c in prompt_text if c in self.term_chars)
        
        # 初始化句法分析状态
        self.symbol_stack = []
        
    def update(self, new_token: int) -> Dict[str, float]:
        """
        使用新生成的token更新分析指标
        
        Args:
            new_token: 新生成的token ID
            
        Returns:
            当前step的所有指标值
        """
        # 添加新token到已生成列表
        self.generated_tokens.append(new_token)
        current_step = len(self.generated_tokens)
        
        # 更新步骤索引
        self.metrics['step'].append(current_step)
        
        # 获取当前完整文本（包括prompt和生成内容）
        current_text = self.tokenizer.decode(self.prompt_tokens + self.generated_tokens)
        generated_text = self.tokenizer.decode(self.generated_tokens)
        
        # 1. 更新终止符统计
        term_char_count = sum(1 for c in current_text if c in self.term_chars) - self.prompt_term_chars
        self.metrics['term_char_count'].append(term_char_count)
        
        # 2. 更新生成的token数目
        token_count = len(self.generated_tokens)
        self.metrics['token_count'].append(token_count)
        
        # 3. 更新重复模式检测
        # 为了高效计算，我们使用字符级别的n-gram
        # 计算1-gram重复率
        if len(generated_text) > 0:
            char_counts = Counter(generated_text)
            unigram_repetition = 1.0 - len(char_counts) / len(generated_text)
            self.metrics['unigram_repetition'].append(unigram_repetition)
        else:
            self.metrics['unigram_repetition'].append(0.0)
        
        # 计算2-gram重复率
        if len(generated_text) > 1:
            bigrams = [generated_text[i:i+2] for i in range(len(generated_text) - 1)]
            bigram_counts = Counter(bigrams)
            bigram_repetition = 1.0 - len(bigram_counts) / len(bigrams)
            self.metrics['bigram_repetition'].append(bigram_repetition)
        else:
            self.metrics['bigram_repetition'].append(0.0)
            
        # 计算3-gram重复率
        if len(generated_text) > 2:
            trigrams = [generated_text[i:i+3] for i in range(len(generated_text) - 2)]
            trigram_counts = Counter(trigrams)
            trigram_repetition = 1.0 - len(trigram_counts) / len(trigrams)
            self.metrics['trigram_repetition'].append(trigram_repetition)
        else:
            self.metrics['trigram_repetition'].append(0.0)
        
        # 4. 更新句法完整性分析
        # 解码最后一个token以获取其文本
        last_token_text = self.tokenizer.decode([new_token])
        
        # 更新符号栈
        for char in last_token_text:
            # 如果是开放符号，入栈
            if char in self.paired_symbols:
                self.symbol_stack.append(char)
            # 如果是闭合符号，检查是否匹配栈顶，匹配则出栈
            elif char in self.paired_symbols.values():
                # 找到对应的开放符号
                opening_symbols = [k for k, v in self.paired_symbols.items() if v == char]
                
                # 如果栈不为空且栈顶是对应的开放符号，出栈
                if self.symbol_stack and self.symbol_stack[-1] in opening_symbols:
                    self.symbol_stack.pop()
        
        # 更新句法深度和未闭合结构数量
        syntax_depth = len(self.symbol_stack)
        self.metrics['syntax_depth'].append(syntax_depth)
        self.metrics['unclosed_structures'].append(syntax_depth)
        
        # 返回当前step的指标
        current_metrics = {
            'term_char_count': term_char_count,
            'token_count': token_count,
            'unigram_repetition': self.metrics['unigram_repetition'][-1],
            'bigram_repetition': self.metrics['bigram_repetition'][-1],
            'trigram_repetition': self.metrics['trigram_repetition'][-1],
            'syntax_depth': syntax_depth,
            'unclosed_structures': syntax_depth
        }
        
        return current_metrics
    
    def update_batch(self, new_tokens: List[int]) -> List[Dict[str, float]]:
        """
        批量更新多个token的指标
        
        Args:
            new_tokens: 新生成的token ID列表
            
        Returns:
            每个step的指标列表
        """
        results = []
        for token in new_tokens:
            results.append(self.update(token))
        return results
    
    def plot_metrics(self, save_dir: str = "./sequence_metrics") -> None:
        """
        绘制指标图表并保存
        
        Args:
            save_dir: 保存图表的目录
        """
        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        
        # 定义要绘制的指标组
        metric_groups = [
            {
                'name': 'termination_signals',
                'title': 'Termination Signals',
                'metrics': ['term_char_count', 'token_count']
            },
            {
                'name': 'repetition_patterns',
                'title': 'Repetition Patterns',
                'metrics': ['unigram_repetition', 'bigram_repetition', 'trigram_repetition']
            },
            {
                'name': 'syntax_completeness',
                'title': 'Syntax Completeness',
                'metrics': ['syntax_depth', 'unclosed_structures']
            }
        ]
        
        # 绘制每组指标
        for group in metric_groups:
            plt.figure(figsize=(10, 6))
            
            for metric in group['metrics']:
                plt.plot(self.metrics['step'], self.metrics[metric], label=metric)
            
            plt.xlabel('Generation Step')
            plt.ylabel('Metric Value')
            plt.title(group['title'])
            plt.grid(True)
            plt.legend()
            
            # 保存图表
            save_path = os.path.join(save_dir, f"{group['name']}.png")
            plt.savefig(save_path)
            plt.close()
            
            print(f"Saved {group['title']} plot to {save_path}")
        
        # 绘制所有指标的组合图
        plt.figure(figsize=(12, 8))
        
        # 标准化所有指标到0-1范围以便比较
        normalized_metrics = {}
        for metric in self.metrics:
            if metric != 'step' and len(self.metrics[metric]) > 0:
                values = np.array(self.metrics[metric])
                min_val = np.min(values)
                max_val = np.max(values)
                
                if max_val > min_val:
                    normalized_metrics[metric] = (values - min_val) / (max_val - min_val)
                else:
                    normalized_metrics[metric] = values - min_val
        
        # 绘制标准化指标
        for metric, values in normalized_metrics.items():
            plt.plot(self.metrics['step'], values, label=metric)
        
        plt.xlabel('Generation Step')
        plt.ylabel('Normalized Metric Value (0-1)')
        plt.title('Combined Generation Metrics (Normalized)')
        plt.grid(True)
        plt.legend()
        
        # 保存组合图
        combined_path = os.path.join(save_dir, "combined_metrics.png")
        plt.savefig(combined_path)
        plt.close()
        
        print(f"Saved combined metrics plot to {combined_path}")
    
    def get_current_metrics(self) -> Dict[str, float]:
        """
        获取最新的指标值
        
        Returns:
            最新的指标值字典
        """
        if not self.metrics['step']:
            return {}
            
        return {
            'term_char_count': self.metrics['term_char_count'][-1],
            'token_count': self.metrics['token_count'][-1],
            'unigram_repetition': self.metrics['unigram_repetition'][-1],
            'bigram_repetition': self.metrics['bigram_repetition'][-1],
            'trigram_repetition': self.metrics['trigram_repetition'][-1],
            'syntax_depth': self.metrics['syntax_depth'][-1],
            'unclosed_structures': self.metrics['unclosed_structures'][-1]
        }
    
    def get_all_metrics(self) -> Dict[str, List]:
        """
        获取所有指标的完整历史
        
        Returns:
            所有指标的历史记录
        """
        return self.metrics


# 示例使用方法
def example_usage():
    """
    示例代码，展示如何使用SequenceAnalyzer
    """
    from transformers import AutoTokenizer
    
    # 加载tokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    
    # 示例prompt
    prompt = "Tell me about the history of artificial intelligence."
    prompt_tokens = tokenizer.encode(prompt)
    
    # 初始化分析器
    analyzer = SequenceAnalyzer(prompt_tokens, tokenizer)
    
    # 模拟生成过程
    generated_tokens = tokenizer.encode("Artificial intelligence began in the 1950s. Researchers developed early systems. Many advances followed.")
    
    # 逐个token更新指标
    for token in generated_tokens:
        metrics = analyzer.update(token)
        print(f"Token: {tokenizer.decode([token])}, Metrics: {metrics}")
    
    # 绘制并保存图表
    analyzer.plot_metrics()


if __name__ == "__main__":
    example_usage() 