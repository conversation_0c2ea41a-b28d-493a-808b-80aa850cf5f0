问题描述:当前使用XGBoost进行回归预测，目标值被分成9个区间：[0, 48, 96, 144, 192, 240, 288, 336, 2049]。最后一个区间（336-2049）跨度极大（1713个单位），而前8个区间每个跨度只有48个单位。这种不均匀分布导致模型在最后一个区间的预测误差很大，显著提升了整体MAE。
解决方案：动态样本权重调整
核心思路:
通过为不同区间的样本分配不同的权重，让模型在训练时更加关注容易出现大误差的区间（特别是最后一个区间），从而减少这些区间的预测误差对整体MAE的影响。
1. 权重计算方法
区间跨度权重：根据每个区间的跨度大小分配权重
前8个区间跨度为48，权重设为1.0（基准权重）.使用平方根缩放：sqrt(1713/48) ≈ 6.0，避免权重过大
2. 区间跨度权重 × 调节因子,默认调节因子为0.4（也就是最后一个区间默认权重为2.4）

nohup python train_xgb_configurable.py --config config3 > config3_mae_early_stopping_interval16_training.log 2>&1 &

prompt_id,token_position,remaining_tokens,bert_predicted_length,bert_predicted_remaining,xgb_predicted_class,xgb_predicted_tokens,true_class,absolute_error,mae_with_special_rule,is_last_interval,xgb_prob_class_0,xgb_prob_class_1,xgb_prob_class_2,xgb_prob_class_3,xgb_prob_class_4,xgb_prob_class_5,xgb_prob_class_6,xgb_prob_class_7,prompt



请你重新建立一个文件kman.py，帮我实现一个贝叶斯先验的卡尔曼滤波器，在bert_xgb_gaussian_softlabel_output/gaussian_softlabel_20250711_204606中的，存在的complete_validation_records.csv。其中token_position就是每次生成的一个token，token_position=0代表生成的第0个token；bert_predicted_length是bert预测的生成长度，bert_predicted_remaining就是用bert_predicted_length-token_position，是bert预测的剩余的token数目。xgb_prob_class_i代表xgboost预测为i类别的分类概率。 xgb_predicted_tokens就是xgboost的预测输出，也就是观测值，采用bert模型做预测的prompt生成长度x,每一步生成一个token，我们就认为生成到第t步，剩余的token数目就等于Xt=x -t。将Xt作为预测值。就不需要进行在线训练，而是离线得到结果，也就是通过complete_validation_records.csv中的数据对结果再平滑一下；以下是问题的详细介绍，然后请你根据这个描述，给我写出你的实现方案，不必立刻写代码：

基于卡尔曼滤波的大模型生成长度预测优化
问题背景
我们需要根据prompt的特征对大模型生成输出长度做终止预测，预测还有多少token后会生成终止。具体设置如下：

每当大模型生成一个token，remaining token应该在原有基础上减1
预测模型在每次大模型生成一个token后预测还剩多少个token
上一次和本次预测理论上存在减少1个token的关系
希望通过平滑处理提高预测准确度

实验设置详情

预测方法：先使用BERT对prompt文本生成长度进行预测，然后在LLM运行过程中获得特征信号并进行预测
分箱设置：[0, 48, 96, 144, 192, 240, 288, 336, 384, 2049]（当然这个分箱）
评估策略：

对于384-2049区间：只要预测值和真实值都在此区间，MAE=0
对于0-384区间：需要精确预测，关心具体数值


预测频率：每隔16个token做一次剩余token预测（一个页面存储16个token）

卡尔曼滤波基本思路
分层滤波架构
两阶段滤波策略：

粗粒度阶段（384-2049）：区间保持滤波
精细阶段（0-384）：数值精确滤波

粗粒度阶段（384-2049区间）
目标： 确保预测稳定落在384-2049区间内
滤波设计：

状态变量： 剩余token数量的区间中心值估计
观测处理： 当XGBoost预测为384-2049区间时，使用区间中心值（约1216）作为观测
约束机制： 滤波后强制约束结果在384-2049范围内
转移模型： 每16个token理论递减，但允许较大的过程噪声

精细阶段（0-384区间）
切换时机： 当滤波预测值首次进入384以下时启动
状态空间重新初始化：

使用切换时刻的预测值作为新的初始状态
重新设置协方差矩阵，反映精细预测的需求

多分箱精细滤波：

根据XGBoost的分箱预测，使用对应分箱的中心值作为观测

融合先验分布的优化方案
方案1：贝叶斯先验初始化状态
具体操作：
设先验分布为：P(区间i) = pi
BERT预测值为：x_bert
定位BERT预测落在区间j

初始协方差设计：
P₀ = 基础方差 / pj
详细解释：

基础方差选择： 可以设为50-200，根据数据波动程度调整
区间定位： 判断x_bert落在哪个分箱区间
边界处理： 如果x_bert在边界附近，可以用相邻区间概率的加权平均

方案2：动态观测噪声调整
具体操作：
当XGBoost预测落在区间i时：
R_i = 基础观测噪声 / sqrt(pi)

其中：
- 基础观测噪声 = 分箱宽度/2 (如48/2=24)
- pi为区间i的先验概率
详细解释：

物理意义： 高频区间的预测更可信，低频区间的预测需要更多怀疑
数值范围： 如果pi=0.20，R_i=24/0.45≈53；如果pi=0.02，R_i=24/0.14≈170


1. 卡曼还需要这个先验知识
对于每个分箱的占比如下：
分箱分布详情:
分箱              样本数          占比       累计占比      
--------------------------------------------------
[0, 48)         789,452      24.59  % 24.59    %
[48, 96)        619,642      19.30  % 43.89    %
[96, 144)       480,861      14.98  % 58.87    %
[144, 192)      377,759      11.77  % 70.63    %
[192, 240)      288,559      8.99   % 79.62    %
[240, 288)      199,389      6.21   % 85.83    %
[288, 336)      123,644      3.85   % 89.68    %
[336, 2049]     331,229      10.32  % 100.00   %

2. lmsyschat1m prompt 太多了

CUDA_VISIBLE_DEVICES=3 nohup python dataset_maker.py --dataset_type lmsyschat1m --gen_max_length 2048 --save_interval 500 --dataset_split 6 --batch_start 0 --batch_num 14 > lmsyschat1m_dataset_generatio_6_0-13.log 2>&1 &



docker run --restart=always --gpus all -itd --ipc=host  -p 8893:22 b441e15571a4

python dataset_maker.py --dataset_type lmsyschat1m --dataset_split 6 --batch_start 0 --batch_num 14

python dataset_maker.py --dataset_type sharegpt_prompt_data --split 1 --batch_start 0 --batch_num 14


CUDA_VISIBLE_DEVICES=0 nohup python dataset_maker.py --dataset_type sharegpt_prompt_data --split 1 --batch_start 0 --batch_num 14 > output_sharegpt_1.log 2>&1 &


nohup python train_xgb_gaussian_softlabel.py --config config3 > config3_soft_label_interval_16_training.log 2>&1 &


CUDA_VISIBLE_DEVICES=3 nohup python dataset_maker.py --dataset_type lmsyschat1m --gen_max_length 2048 --save_interval 500 --dataset_split 1 --batch_start 0 --batch_num 14 > lmsyschat1m_dataset_generatio_1_0-13.log 2>&1 &


CUDA_VISIBLE_DEVICES=6 nohup python dataset_maker.py --dataset_type sharegpt_prompt_data --split 1 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_8_1_10.log 2>&1 &

python dataset_maker.py --dataset_type sharegpt_prompt_data --split 1 --batch_start 5 --batch_num 14 --batch_size 500

CUDA_VISIBLE_DEVICES=6 nohup python dataset_maker.py --dataset_type sharegpt_prompt_data --split 8 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 14 > sharegpt_datas
et_8_1_14.log 2>&1 &



nohup python3 train_xgb_gaussian_softlabel.py --config config1 --sigma 24.0 --min_prob 0.005 > config1_softlabel_interval32.log 2>&1 &

CUDA_VISIBLE_DEVICES=6 nohup python dataset_maker.py --dataset_type sharegpt --split 1 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_8_1_10.log 2>&1 &

CUDA_VISIBLE_DEVICES=6 nohup python dataset_maker.py --dataset_type sharegpt --split 1 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_1_1_10.log 2>&1 &


CUDA_VISIBLE_DEVICES=5 nohup python dataset_maker.py --dataset_type sharegpt --split 3 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_3_1_10.log 2>&1 &

CUDA_VISIBLE_DEVICES=4 nohup python dataset_maker.py --dataset_type sharegpt --split 4 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_4_1_10.log 2>&1 &

CUDA_VISIBLE_DEVICES=3 nohup python dataset_maker.py --dataset_type sharegpt --split 5 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_5_1_10.log 2>&1 &

CUDA_VISIBLE_DEVICES=1 nohup python dataset_maker.py --dataset_type sharegpt --split 7 --gen_max_length 2048 --save_interval 500 --batch_start 1 --batch_num 10 > sharegpt_dataset_7_1_10.log 2>&1 &

CUDA_VISIBLE_DEVICES=0 nohup python dataset_maker.py --dataset_type sharegpt --split 8 --gen_max_length 2048 --save_interval 500 --batch_start 2 --batch_num 10 > sharegpt_dataset_8_2_3.log 2>&1 &