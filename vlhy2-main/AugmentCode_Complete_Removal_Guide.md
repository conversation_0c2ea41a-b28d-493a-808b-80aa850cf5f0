# AugmentCode VS Code插件完全清理指南

## 概述
本指南将帮助您彻底删除AugmentCode VS Code插件的所有痕迹，确保下次安装时系统无任何残留记录，仿佛从未安装过该插件。

## ⚠️ 重要提醒
- 请在执行删除操作前备份重要数据
- 建议关闭所有VS Code和Cursor实例后再进行清理
- 按照顺序逐一删除，避免遗漏

## 需要删除的文件和文件夹

### 1. 插件安装目录
**路径：** `/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/`

**原因：** 这是AugmentCode插件的主要安装目录，包含插件的所有核心文件、资源文件和配置信息。删除此目录可以移除插件的主体程序。

**包含内容：**
- 插件清单文件 (.vsixmanifest)
- 插件代码 (out/ 目录)
- 图标和字体文件 (icon.png, augment-icon-font.woff, augment-kb-icon-font.woff)
- 文档文件 (README.md, CHANGELOG.md)
- 配置文件 (package.json)
- Web视图资源 (common-webviews/, media/)

### 2. VS Code全局存储数据
**路径：** `/Users/<USER>/Library/Application Support/Code/User/globalStorage/augment.vscode-augment/`

**原因：** 此目录存储AugmentCode插件的全局状态数据和用户资产，包括插件的运行时数据、缓存信息和用户个性化设置。删除此目录可以清除插件的所有运行时痕迹。

**包含内容：**
- `augment-global-state/` - 插件全局状态数据
- `augment-user-assets/` - 用户资产和缓存文件

### 3. Cursor全局存储数据
**路径：** `/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/augment.vscode-augment/`

**原因：** 如果您同时使用Cursor编辑器，AugmentCode插件也会在Cursor中创建类似的全局存储数据。删除此目录确保在Cursor中也无任何残留。

**包含内容：**
- `augment-global-state/` - 插件在Cursor中的全局状态数据

### 4. VS Code用户设置中的配置
**路径：** `/Users/<USER>/Library/Application Support/Code/User/settings.json`

**原因：** 该文件包含大量AugmentCode插件的用户配置信息，特别是`augment.chat.userGuidelines`设置项包含了详细的用户指南配置。需要手动编辑此文件，删除所有以`augment.`开头的配置项。

**需要删除的配置项：**
- `augment.chat.userGuidelines` - 包含详细的用户指南配置
- 其他所有以`augment.`开头的配置项

### 5. VS Code日志文件中的记录
**路径：** `/Users/<USER>/Library/Application Support/Code/logs/`

**原因：** VS Code的日志文件中包含大量AugmentCode插件的运行记录、错误信息和调试数据。虽然这些日志会随时间自动清理，但为了彻底清除痕迹，建议删除包含AugmentCode相关记录的日志文件。

**影响的日志文件类型：**
- `renderer.log` - 渲染进程日志，包含插件加载和运行记录
- `exthost.log` - 扩展主机日志，包含插件激活记录
- `network.log` - 网络日志，包含插件更新检查记录

### 6. 下载和临时文件
**路径：** 
- `/Users/<USER>/Downloads/AugmentCode-Free-master.zip`
- `/Users/<USER>/Desktop/cursor_temp/AugmentCode-Free/`
- `/Users/<USER>/Desktop/cursor_temp/augment-cleaner-darwin-amd64-v4.2`
- `/Users/<USER>/Desktop/cursor_temp/Augmentcode无限续杯工具/`

**原因：** 这些是与AugmentCode相关的下载文件、源码和工具。删除这些文件可以清除所有相关的临时文件和工具，确保系统中没有任何AugmentCode的相关文件残留。

### 7. JetBrains IDE中的AugmentCode插件（如果适用）
**路径：**
- `/Users/<USER>/Library/Application Support/JetBrains/PyCharm2025.2/plugins/intellij-augment/`
- `/Users/<USER>/Library/Application Support/JetBrains/PyCharm2025.1/plugins/intellij-augment/`

**原因：** 如果您在JetBrains IDE（如PyCharm）中也安装了AugmentCode插件，这些目录包含相关的插件文件。删除这些目录可以确保在所有IDE中都清除AugmentCode的痕迹。

## 清理步骤

### 第一步：关闭所有相关应用
```bash
# 关闭所有VS Code实例
pkill -f "Visual Studio Code"

# 关闭所有Cursor实例  
pkill -f "Cursor"

# 关闭所有JetBrains IDE实例
pkill -f "PyCharm"
```

### 第二步：删除插件目录和数据文件
```bash
# 删除VS Code插件安装目录
rm -rf "/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/"

# 删除VS Code全局存储数据
rm -rf "/Users/<USER>/Library/Application Support/Code/User/globalStorage/augment.vscode-augment/"

# 删除Cursor全局存储数据
rm -rf "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/augment.vscode-augment/"

# 删除JetBrains插件目录
rm -rf "/Users/<USER>/Library/Application Support/JetBrains/PyCharm2025.2/plugins/intellij-augment/"
rm -rf "/Users/<USER>/Library/Application Support/JetBrains/PyCharm2025.1/plugins/intellij-augment/"

# 删除下载和临时文件
rm -f "/Users/<USER>/Downloads/AugmentCode-Free-master.zip"
rm -rf "/Users/<USER>/Desktop/cursor_temp/AugmentCode-Free/"
rm -f "/Users/<USER>/Desktop/cursor_temp/augment-cleaner-darwin-amd64-v4.2"
rm -rf "/Users/<USER>/Desktop/cursor_temp/Augmentcode无限续杯工具/"
```

### 第三步：清理配置文件
```bash
# 备份VS Code设置文件
cp "/Users/<USER>/Library/Application Support/Code/User/settings.json" "/Users/<USER>/Library/Application Support/Code/User/settings.json.backup"

# 手动编辑settings.json文件，删除所有以"augment."开头的配置项
# 建议使用文本编辑器打开文件并手动删除相关配置
```

### 第四步：清理日志文件（可选）
```bash
# 删除包含AugmentCode记录的日志目录（这会删除所有VS Code日志）
rm -rf "/Users/<USER>/Library/Application Support/Code/logs/"

# 注意：这会删除所有VS Code日志，如果您需要保留其他日志，请手动筛选删除
```

## 验证清理结果

### 检查是否还有残留文件
```bash
# 搜索系统中是否还有augment相关文件
find /Users/<USER>"*augment*" -type f 2>/dev/null
find /Users/<USER>"*augment*" -type d 2>/dev/null

# 检查VS Code扩展目录
ls -la "/Users/<USER>/.vscode/extensions/" | grep augment

# 检查全局存储目录
ls -la "/Users/<USER>/Library/Application Support/Code/User/globalStorage/" | grep augment
ls -la "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/" | grep augment
```

### 检查配置文件
```bash
# 检查VS Code设置文件中是否还有augment相关配置
grep -i "augment" "/Users/<USER>/Library/Application Support/Code/User/settings.json"
```

## 重要说明

1. **完全清理**：按照本指南执行后，系统中将不会保留任何AugmentCode插件的痕迹，下次安装时将完全像首次安装一样。

2. **数据备份**：在执行删除操作前，建议备份重要的配置文件，以防意外删除其他重要设置。

3. **版本差异**：插件目录名称可能因版本不同而略有差异（如`augment.vscode-augment-0.509.1`），请根据实际情况调整路径。

4. **权限问题**：某些文件可能需要管理员权限才能删除，如遇到权限问题，请在命令前添加`sudo`。

5. **自动更新**：清理完成后，建议检查VS Code的自动更新设置，确保不会自动重新安装该插件。

## 故障排除

如果在清理过程中遇到问题：

1. **文件被占用**：确保所有相关应用程序已完全关闭
2. **权限不足**：使用`sudo`命令或在系统设置中调整文件权限
3. **文件不存在**：某些文件可能已被删除或路径不同，这是正常现象
4. **配置文件格式**：编辑settings.json时注意保持JSON格式正确

完成以上步骤后，AugmentCode插件将被彻底清除，系统将恢复到从未安装过该插件的状态。
