# Trojan Add-on Script 使用说明

## 概述

`trojan_add.sh` 是一个增强脚本，用于在已有的 lvhy.sh 安装基础上添加 Trojan 协议支持。该脚本可以让您的服务器同时支持 **Hysteria2**、**VLESS Reality** 和 **Trojan** 三种协议。

## 前置条件

1. **必须先运行 lvhy.sh 脚本**，安装 Hysteria2 + Reality (共存) 模式
2. **服务器需要 root 权限**
3. **确保 Sing-Box 服务正常运行**

## 安装步骤

### 1. 上传脚本到服务器

```bash
# 将 trojan_add.sh 上传到服务器的 /root 目录
scp trojan_add.sh root@your-server-ip:/root/
```

### 2. 设置执行权限

```bash
chmod +x /root/trojan_add.sh
```

### 3. 运行脚本

```bash
cd /root
./trojan_add.sh
```

## 配置选项

脚本运行时会提示您输入以下配置：

### 端口配置
- **默认端口**: 8080
- **建议**: 使用非标准端口避免冲突
- **注意**: 确保端口未被其他服务占用

### SNI/域名配置
- **默认域名**: www.cloudflare.com
- **建议选择**: 
  - `www.microsoft.com`
  - `www.apple.com`
  - `www.google.com`
  - `www.cloudflare.com`
- **原则**: 选择大型、稳定的网站作为伪装目标

## 脚本功能特性

### 🔧 自动化配置
- ✅ 自动检测现有 Sing-Box 安装
- ✅ 自动生成 Trojan 密码
- ✅ 自动生成自签名证书
- ✅ 自动备份原配置文件
- ✅ 自动验证配置文件语法

### 🛡️ 安全特性
- ✅ 配置文件自动备份
- ✅ 语法验证确保服务稳定
- ✅ 自签名证书加密传输
- ✅ 随机生成强密码

### 📊 信息管理
- ✅ 自动生成客户端导入链接
- ✅ 显示二维码（如果安装了 qrencode）
- ✅ 保存配置信息到持久化文件
- ✅ 与 lvhy.sh 配置信息集成

## 配置文件结构

脚本会在现有配置基础上添加 Trojan inbound：

```json
{
    "type": "trojan",
    "tag": "trojan-in",
    "listen": "::",
    "listen_port": 8080,
    "users": [
        {
            "password": "your-generated-password"
        }
    ],
    "tls": {
        "enabled": true,
        "server_name": "www.cloudflare.com",
        "certificate_path": "/etc/trojan/cert.pem",
        "key_path": "/etc/trojan/private.key"
    },
    "fallback": {
        "server": "www.cloudflare.com",
        "server_port": 443
    }
}
```

## 客户端配置

### Trojan 客户端配置参数
- **服务器地址**: 您的服务器IP
- **端口**: 您设置的端口（默认8080）
- **密码**: 脚本生成的密码
- **SNI**: 您设置的域名
- **允许不安全**: 是（因为使用自签名证书）

### 导入链接格式
```
trojan://password@server-ip:port?sni=domain&allowInsecure=1#Trojan-server-ip
```

## 服务管理

### 检查服务状态
```bash
systemctl status sing-box
```

### 查看实时日志
```bash
journalctl -u sing-box -f
```

### 重启服务
```bash
systemctl restart sing-box
```

## 多协议端口分配

安装完成后，您的服务器将监听以下端口：

| 协议 | 默认端口 | 传输协议 | 用途 |
|------|----------|----------|------|
| VLESS Reality | 443 | TCP | 最佳伪装效果 |
| Hysteria2 | 8443 | UDP | 高性能传输 |
| Trojan | 8080 | TCP | 稳定可靠 |

## 4用户分配建议

基于您的2GB/2核服务器配置，推荐以下用户分配：

### 方案A：均衡分配
- **用户1**: VLESS Reality (443端口)
- **用户2**: Hysteria2 (8443端口)  
- **用户3**: Trojan (8080端口)
- **用户4**: 轮换使用或备用协议

### 方案B：按需分配
- **高稳定性需求用户**: VLESS Reality
- **低延迟需求用户**: Hysteria2
- **兼容性需求用户**: Trojan
- **备用用户**: 任意协议

## 性能监控

### 资源使用预估
- **CPU使用率**: 50-70%（三协议并行）
- **内存占用**: 1.0-1.5GB
- **网络负载**: 根据用户使用情况

### 监控命令
```bash
# 查看资源使用
htop

# 查看网络连接
ss -tuln | grep -E "(443|8443|8080)"

# 查看进程状态
ps aux | grep sing-box
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8080
   # 或使用其他端口
   ```

2. **证书问题**
   ```bash
   # 检查证书文件
   ls -la /etc/trojan/
   # 重新生成证书
   rm -rf /etc/trojan && ./trojan_add.sh
   ```

3. **配置文件错误**
   ```bash
   # 检查配置语法
   /usr/local/bin/sing-box check -c /usr/local/etc/sing-box/config.json
   # 恢复备份
   cp /usr/local/etc/sing-box/config.json.backup.* /usr/local/etc/sing-box/config.json
   ```

4. **服务启动失败**
   ```bash
   # 查看详细错误
   journalctl -u sing-box --no-pager
   # 手动启动测试
   /usr/local/bin/sing-box run -c /usr/local/etc/sing-box/config.json
   ```

## 安全建议

1. **定期更换密码**
   - 重新运行脚本生成新密码
   - 更新客户端配置

2. **监控连接**
   - 定期检查异常连接
   - 监控流量使用情况

3. **备份配置**
   - 脚本自动备份，建议额外手动备份
   - 保存重要配置信息

4. **防火墙配置**
   ```bash
   # 开放必要端口
   ufw allow 443
   ufw allow 8443
   ufw allow 8080
   ```

## 卸载说明

如需移除 Trojan 配置：

1. **停止服务**
   ```bash
   systemctl stop sing-box
   ```

2. **恢复配置**
   ```bash
   cp /usr/local/etc/sing-box/config.json.backup.* /usr/local/etc/sing-box/config.json
   ```

3. **删除证书**
   ```bash
   rm -rf /etc/trojan
   ```

4. **重启服务**
   ```bash
   systemctl start sing-box
   ```

## 技术支持

- **原始脚本**: lvhy.sh by jcnf-那坨
- **Trojan增强**: AI Assistant
- **问题反馈**: 请检查配置文件和日志

---

**注意**: 此脚本仅用于学习和研究目的，请遵守当地法律法规。
