#!/bin/bash

# Trojan Certificate Fix Script
# 修复 Trojan 证书生成问题

# --- Configuration ---
TROJAN_CERT_DIR="/etc/trojan"
TROJAN_CERT_KEY="${TROJAN_CERT_DIR}/private.key"
TROJAN_CERT_PEM="${TROJAN_CERT_DIR}/cert.pem"

# --- Colors ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

fix_trojan_certificate() {
    local domain="${1:-www.microsoft.com}"
    
    info "开始修复 Trojan 证书..."
    info "使用域名: $domain"
    
    # 清理旧证书
    if [ -d "$TROJAN_CERT_DIR" ]; then
        warn "删除旧的证书文件..."
        rm -rf "$TROJAN_CERT_DIR"
    fi
    
    # 创建证书目录
    mkdir -p "$TROJAN_CERT_DIR"
    
    # 生成私钥 (使用标准方法)
    info "生成 RSA 私钥..."
    if ! openssl genrsa -out "$TROJAN_CERT_KEY" 2048; then
        error "生成私钥失败"
        return 1
    fi
    
    # 验证私钥
    if ! openssl rsa -in "$TROJAN_CERT_KEY" -check -noout; then
        error "私钥验证失败"
        return 1
    fi
    
    # 生成证书签名请求 (CSR)
    info "生成证书签名请求..."
    local csr_file="${TROJAN_CERT_DIR}/cert.csr"
    if ! openssl req -new -key "$TROJAN_CERT_KEY" -out "$csr_file" -subj "/C=US/ST=CA/L=San Francisco/O=Example/CN=${domain}"; then
        error "生成 CSR 失败"
        return 1
    fi
    
    # 生成自签名证书
    info "生成自签名证书..."
    if ! openssl x509 -req -in "$csr_file" -signkey "$TROJAN_CERT_KEY" -out "$TROJAN_CERT_PEM" -days 365; then
        error "生成证书失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f "$csr_file"
    
    # 设置正确的权限
    chmod 600 "$TROJAN_CERT_KEY"
    chmod 644 "$TROJAN_CERT_PEM"
    
    # 验证生成的证书
    info "验证证书..."
    if ! openssl x509 -in "$TROJAN_CERT_PEM" -text -noout >/dev/null 2>&1; then
        error "证书验证失败"
        return 1
    fi
    
    # 验证私钥和证书匹配
    local key_hash=$(openssl rsa -in "$TROJAN_CERT_KEY" -pubout -outform DER 2>/dev/null | openssl dgst -sha256)
    local cert_hash=$(openssl x509 -in "$TROJAN_CERT_PEM" -pubkey -noout -outform DER 2>/dev/null | openssl dgst -sha256)
    
    if [ "$key_hash" != "$cert_hash" ]; then
        error "私钥和证书不匹配"
        return 1
    fi
    
    success "证书生成和验证完成"
    
    # 显示证书信息
    info "证书详细信息:"
    openssl x509 -in "$TROJAN_CERT_PEM" -subject -issuer -dates -noout
    
    # 显示文件信息
    info "文件信息:"
    ls -la "$TROJAN_CERT_DIR"
    
    return 0
}

# 主函数
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${GREEN} Trojan 证书修复工具 ${NC}"
    echo -e "${BLUE}================================${NC}"
    
    check_root
    
    # 获取域名参数
    local domain="$1"
    if [ -z "$domain" ]; then
        read -p "请输入证书域名 (默认: www.microsoft.com): " domain
        domain=${domain:-www.microsoft.com}
    fi
    
    # 修复证书
    if fix_trojan_certificate "$domain"; then
        success "证书修复完成！"
        echo
        info "接下来的步骤:"
        echo "1. 重新运行 trojan_add.sh 脚本"
        echo "2. 或者重启 sing-box 服务: systemctl restart sing-box"
        echo "3. 检查服务状态: systemctl status sing-box"
    else
        error "证书修复失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
